@echo off
REM ============================================================================
REM DEX900DN System Validation - Test All Components
REM ============================================================================

title DEX900DN System Validation

REM Set console colors
color 0E

echo.
echo ============================================================================
echo                    DEX900DN SYSTEM VALIDATION
echo ============================================================================
echo.
echo [INFO] Running comprehensive system validation...
echo [INFO] This will test all components without live trading
echo.

REM Change to script directory
cd /d "%~dp0"

REM Validate project structure
if not exist "src" (
    echo [ERROR] Source directory not found
    pause
    exit /b 1
)

REM Create logs directory
if not exist "logs" mkdir logs

echo [INFO] Starting validation process...
echo.

REM Run system validation
python validate_complete_system.py

REM Check result
if errorlevel 1 (
    echo.
    echo ============================================================================
    echo                           VALIDATION FAILED
    echo ============================================================================
    echo.
    echo [ERROR] System validation found critical issues
    echo [INFO] Please review the validation report for details
    echo [INFO] Do not start live trading until issues are resolved
    echo.
) else (
    echo.
    echo ============================================================================
    echo                         VALIDATION SUCCESSFUL
    echo ============================================================================
    echo.
    echo [SUCCESS] System validation completed successfully
    echo [INFO] System is ready for production trading
    echo [INFO] You can now run start_dex900dn.bat to begin trading
    echo.
)

echo [INFO] Validation report saved with timestamp
echo [INFO] Check logs directory for detailed results
echo.
pause
exit /b 0
