"""
Test script for core DEX900DN trading system modules
Tests cycle_engine.py and data_feed.py functionality
"""

import sys
import os
import time
from datetime import datetime, timedelta

# Add src and root to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))

from src.modules.cycle_engine import TimeCycleTracker, cycle_tracker
from src.modules.data_feed import <PERSON><PERSON><PERSON><PERSON>, TickData
from config.config import config

def test_cycle_engine():
    """Test the cycle engine functionality"""
    print("=" * 50)
    print("TESTING CYCLE ENGINE")
    print("=" * 50)
    
    # Create a new tracker for testing
    tracker = TimeCycleTracker()
    
    # Test 1: Start spike countdown
    print("\n1. Testing spike countdown start...")
    result = tracker.start_spike_countdown()
    print(f"Countdown started: {result}")
    print(f"State: {tracker.get_state_summary()}")
    
    # Test 2: Check cycle progress
    print("\n2. Testing cycle progress...")
    progress = tracker.get_cycle_progress()
    print(f"Cycle active: {progress['cycle_active']}")
    print(f"Progress: {progress['progress_percent']:.2f}%")
    print(f"Time to spike window: {progress['time_to_spike_window']}")
    
    # Test 3: Simulate time passage to spike window
    print("\n3. Simulating time passage...")
    # Manually set cycle start time to 14 minutes ago to test spike window
    tracker.state.cycle_start_time = datetime.now() - timedelta(seconds=850)
    
    in_window = tracker.spike_window_alert()
    print(f"In spike window: {in_window}")
    print(f"State: {tracker.get_state_summary()}")
    
    # Test 4: Test volatility peak tracking
    print("\n4. Testing volatility tracking...")
    tracker.update_volatility_peak(0.05)
    tracker.update_volatility_peak(0.15)  # Should become new peak
    tracker.update_volatility_peak(0.10)  # Should not update peak
    print(f"Volatility peak: {tracker.state.volatility_peak}")
    
    # Test 5: Test cycle reset
    print("\n5. Testing cycle reset...")
    reset_result = tracker.reset_cycle(0.01)  # Low volatility should trigger reset
    print(f"Cycle reset: {reset_result}")
    print(f"State after reset: {tracker.get_state_summary()}")
    
    print("\nCycle engine tests completed!")

def test_data_handler():
    """Test the data handler functionality (without MT5 connection)"""
    print("\n" + "=" * 50)
    print("TESTING DATA HANDLER")
    print("=" * 50)
    
    # Create a data handler (will fail MT5 connection but we can test other functions)
    handler = DataHandler()
    
    # Test 1: Create mock tick data
    print("\n1. Testing with mock tick data...")
    mock_ticks = []
    base_price = 25000.0
    
    for i in range(100):
        # Create some price movement
        price_change = (i % 10 - 5) * 10  # Oscillating price
        bid = base_price + price_change
        ask = bid + 5  # 5 point spread
        
        tick = TickData(
            timestamp=datetime.now() - timedelta(seconds=100-i),
            bid=bid,
            ask=ask,
            volume=100 + (i % 20) * 10,  # Varying volume
            spread=5.0
        )
        mock_ticks.append(tick)
        
        # Add to handler buffers
        handler.tick_buffer.append(tick)
        handler.price_buffer.append(tick.mid_price)
        handler.volume_buffer.append(tick.volume)
    
    print(f"Created {len(mock_ticks)} mock ticks")
    
    # Test 2: Calculate volatility
    print("\n2. Testing volatility calculation...")
    volatility = handler.calculate_volatility(5)  # 5-minute window
    print(f"5-minute volatility: {volatility:.6f}")
    
    # Test 3: Test volume spike detection
    print("\n3. Testing volume spike detection...")
    # Add a high volume tick
    spike_tick = TickData(
        timestamp=datetime.now(),
        bid=base_price,
        ask=base_price + 5,
        volume=500,  # High volume
        spread=5.0
    )
    handler.tick_buffer.append(spike_tick)
    handler.volume_buffer.append(spike_tick.volume)
    
    volume_spike = handler.detect_volume_spike()
    print(f"Volume spike detected: {volume_spike}")
    
    # Test 4: Test price drop detection
    print("\n4. Testing price drop detection...")
    # Add some ticks with significant price drop
    for i in range(10):
        drop_price = base_price - (i * 60)  # 60 points per second drop
        tick = TickData(
            timestamp=datetime.now() - timedelta(seconds=10-i),
            bid=drop_price,
            ask=drop_price + 5,
            volume=150,
            spread=5.0
        )
        handler.price_buffer.append(tick.mid_price)
    
    spike_detected, drop_amount = handler.detect_price_drop()
    print(f"Price spike detected: {spike_detected}")
    print(f"Drop amount: {drop_amount:.1f} points")
    
    # Test 5: Get market summary
    print("\n5. Testing market summary...")
    summary = handler.get_market_summary()
    print(f"Current price: {summary['current_price']}")
    print(f"Volatility: {summary['volatility_5min']:.6f}")
    print(f"Data points in buffers: {summary['data_points']}")
    
    print("\nData handler tests completed!")

def test_integration():
    """Test integration between cycle engine and data handler"""
    print("\n" + "=" * 50)
    print("TESTING INTEGRATION")
    print("=" * 50)
    
    # Test scenario: Detect spike and start cycle
    print("\n1. Testing spike detection and cycle start...")
    
    # Simulate a spike scenario
    tracker = TimeCycleTracker()
    handler = DataHandler()
    
    # Mock high volatility scenario
    high_vol = 0.20  # 20% volatility
    tracker.update_volatility_peak(high_vol)
    
    # Start cycle based on spike
    tracker.start_spike_countdown()
    
    print(f"Cycle started with volatility peak: {tracker.state.volatility_peak}")
    print(f"Cycle state: {tracker.get_state_summary()}")
    
    # Test cycle reset when volatility drops
    print("\n2. Testing cycle reset on low volatility...")
    low_vol = 0.01  # 1% volatility (< 10% of peak)
    reset_result = tracker.reset_cycle(low_vol)
    
    print(f"Reset triggered: {reset_result}")
    print(f"Final state: {tracker.get_state_summary()}")
    
    print("\nIntegration tests completed!")

def main():
    """Run all tests"""
    print("DEX900DN TRADING SYSTEM - CORE MODULE TESTS")
    print("=" * 60)
    
    try:
        # Test individual modules
        test_cycle_engine()
        test_data_handler()
        test_integration()
        
        print("\n" + "=" * 60)
        print("ALL TESTS COMPLETED SUCCESSFULLY!")
        print("=" * 60)
        
    except Exception as e:
        print(f"\nTEST FAILED: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
