"""
Comprehensive Test Suite for Phase Detection System
Tests the phase classifier integration with cycle tracker and data handler
"""

import sys
import os
import time
import unittest
from datetime import datetime, timedelta

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))

from src.modules.cycle_engine import TimeCycleTracker
from src.modules.data_feed import DataHandler, TickData
from src.modules.phase_classifier import PhaseClassifier, MarketPhase, create_phase_classifier
from config.config import config

class TestPhaseClassifier(unittest.TestCase):
    """Test phase classification logic"""
    
    def setUp(self):
        self.cycle_tracker = TimeCycleTracker()
        self.data_handler = DataHandler()
        self.classifier = PhaseClassifier(self.cycle_tracker, self.data_handler)
        
        # Populate with some test data
        self._populate_test_data()
    
    def _populate_test_data(self):
        """Add test data to data handler"""
        base_price = 25000.0
        for i in range(100):
            tick = TickData(
                timestamp=datetime.now() - timedelta(seconds=100-i),
                bid=base_price + (i % 10) - 5,
                ask=base_price + (i % 10) - 5 + 5,
                volume=100 + (i % 20),
                spread=5.0
            )
            self.data_handler.tick_buffer.append(tick)
            self.data_handler.price_buffer.append(tick.mid_price)
            self.data_handler.volume_buffer.append(tick.volume)
    
    def test_spike_phase_detection(self):
        """Test SPIKE phase detection"""
        # Set up spike conditions
        # 1. Start cycle and move to spike window
        self.cycle_tracker.start_spike_countdown(datetime.now() - timedelta(seconds=850))
        
        # 2. Add high volatility data
        base_price = 25000.0
        for i in range(50):
            # Create volatile price movement
            price_change = (-1)**i * (i * 20)  # Alternating large moves
            tick = TickData(
                timestamp=datetime.now() - timedelta(seconds=50-i),
                bid=base_price + price_change,
                ask=base_price + price_change + 5,
                volume=200,  # Higher volume
                spread=5.0
            )
            self.data_handler.price_buffer.append(tick.mid_price)
            self.data_handler.volume_buffer.append(tick.volume)
        
        # 3. Classify phase
        signal = self.classifier.classify_phase()
        
        # Should detect SPIKE phase
        self.assertEqual(signal.phase, MarketPhase.SPIKE)
        self.assertGreater(signal.confidence, 0.6)
        self.assertTrue(signal.in_spike_window)
        # Could be either spike window detection or price drop detection
        self.assertTrue("price drop" in signal.reasoning.lower() or "spike window" in signal.reasoning.lower())
    
    def test_calm_phase_detection(self):
        """Test CALM phase detection"""
        # Set up calm conditions
        # 1. No active cycle (outside spike window)
        self.cycle_tracker.reset_cycle(0.01)
        
        # 2. Add low volatility data
        base_price = 25000.0
        for i in range(100):
            # Small, consistent price movements
            price_change = (i % 3) - 1  # -1, 0, 1 point changes
            tick = TickData(
                timestamp=datetime.now() - timedelta(seconds=100-i),
                bid=base_price + price_change,
                ask=base_price + price_change + 5,
                volume=100,  # Normal volume
                spread=5.0
            )
            self.data_handler.price_buffer.append(tick.mid_price)
            self.data_handler.volume_buffer.append(tick.volume)
        
        # 3. Classify phase
        signal = self.classifier.classify_phase()
        
        # Should detect CALM phase
        self.assertEqual(signal.phase, MarketPhase.CALM)
        self.assertGreater(signal.confidence, 0.6)
        self.assertFalse(signal.in_spike_window)
        self.assertIn("Low volatility", signal.reasoning)
    
    def test_hold_phase_detection(self):
        """Test HOLD phase detection"""
        # Set up transitional conditions
        # 1. In spike window but low volatility
        self.cycle_tracker.start_spike_countdown(datetime.now() - timedelta(seconds=850))
        
        # 2. Add moderate volatility data (not high enough for spike)
        base_price = 25000.0
        for i in range(50):
            price_change = (i % 5) * 2  # Small but consistent changes
            tick = TickData(
                timestamp=datetime.now() - timedelta(seconds=50-i),
                bid=base_price + price_change,
                ask=base_price + price_change + 5,
                volume=100,
                spread=5.0
            )
            self.data_handler.price_buffer.append(tick.mid_price)
            self.data_handler.volume_buffer.append(tick.volume)
        
        # 3. Classify phase
        signal = self.classifier.classify_phase()
        
        # Should detect HOLD phase
        self.assertEqual(signal.phase, MarketPhase.HOLD)
        self.assertTrue(signal.in_spike_window)
        self.assertIn("spike window but volatility too low", signal.reasoning)
    
    def test_price_drop_spike_detection(self):
        """Test SPIKE detection based on major price drops"""
        # Clear existing data
        self.data_handler.price_buffer.clear()
        
        # Add major price drop
        base_price = 25000.0
        for i in range(10):
            drop_price = base_price - (i * 60)  # 600 point drop in 10 seconds
            self.data_handler.price_buffer.append(drop_price)
        
        # Classify phase
        signal = self.classifier.classify_phase()
        
        # Should detect SPIKE even outside window due to major drop
        self.assertEqual(signal.phase, MarketPhase.SPIKE)
        self.assertTrue(signal.price_drop_detected)
        self.assertIn("price drop", signal.reasoning.lower())
    
    def test_phase_stability_tracking(self):
        """Test phase stability calculation"""
        # Generate consistent CALM signals
        self.cycle_tracker.reset_cycle(0.01)
        
        for _ in range(10):
            signal = self.classifier.classify_phase()
            time.sleep(0.01)  # Small delay to create history
        
        stability = self.classifier.get_phase_stability()
        self.assertGreater(stability, 0.7)  # Should be stable
    
    def test_trading_decision_logic(self):
        """Test should_trade decision logic"""
        # Test HOLD phase - should not trade
        self.cycle_tracker.start_spike_countdown(datetime.now() - timedelta(seconds=850))
        # Low volatility in spike window = HOLD
        should_trade, reason = self.classifier.should_trade()
        
        # May vary based on data, but test the logic exists
        self.assertIsInstance(should_trade, bool)
        self.assertIsInstance(reason, str)
        self.assertIn("phase", reason.lower())
    
    def test_phase_summary(self):
        """Test comprehensive phase summary"""
        summary = self.classifier.get_phase_summary()
        
        # Check all required fields are present
        required_fields = [
            'current_phase', 'confidence', 'stability', 'volatility',
            'in_spike_window', 'volume_spike_detected', 'price_drop_detected',
            'reasoning', 'phase_duration', 'last_phase_change', 'timestamp'
        ]
        
        for field in required_fields:
            self.assertIn(field, summary)
        
        # Check data types
        self.assertIn(summary['current_phase'], ['CALM', 'SPIKE', 'HOLD'])
        self.assertIsInstance(summary['confidence'], float)
        self.assertIsInstance(summary['stability'], float)

class TestPhaseIntegration(unittest.TestCase):
    """Test integration between all components"""
    
    def setUp(self):
        self.cycle_tracker = TimeCycleTracker()
        self.data_handler = DataHandler()
        self.classifier = create_phase_classifier(self.cycle_tracker, self.data_handler)
    
    def test_full_spike_scenario(self):
        """Test complete spike detection scenario"""
        print("\n=== TESTING FULL SPIKE SCENARIO ===")
        
        # Step 1: Start cycle
        self.cycle_tracker.start_spike_countdown()
        print(f"1. Cycle started: {self.cycle_tracker.get_state_summary()}")
        
        # Step 2: Move to spike window
        self.cycle_tracker.state.cycle_start_time = datetime.now() - timedelta(seconds=850)
        in_window = self.cycle_tracker.spike_window_alert()
        print(f"2. In spike window: {in_window}")
        
        # Step 3: Add volatile market data
        base_price = 25000.0
        for i in range(20):
            # Create high volatility
            price_change = (-1)**i * (i * 30)  # Large alternating moves
            tick = TickData(
                timestamp=datetime.now() - timedelta(seconds=20-i),
                bid=base_price + price_change,
                ask=base_price + price_change + 5,
                volume=300,  # High volume
                spread=5.0
            )
            self.data_handler.tick_buffer.append(tick)
            self.data_handler.price_buffer.append(tick.mid_price)
            self.data_handler.volume_buffer.append(tick.volume)
        
        # Step 4: Classify phase
        signal = self.classifier.classify_phase()
        print(f"3. Phase detected: {signal.phase.value}")
        print(f"4. Confidence: {signal.confidence:.2f}")
        print(f"5. Reasoning: {signal.reasoning}")
        
        # Step 5: Check trading decision
        should_trade, trade_reason = self.classifier.should_trade()
        print(f"6. Should trade: {should_trade} - {trade_reason}")
        
        # Verify spike detection
        self.assertEqual(signal.phase, MarketPhase.SPIKE)
        self.assertGreater(signal.confidence, 0.6)
    
    def test_full_calm_scenario(self):
        """Test complete calm detection scenario"""
        print("\n=== TESTING FULL CALM SCENARIO ===")
        
        # Step 1: Reset cycle (outside spike window)
        self.cycle_tracker.reset_cycle(0.01)
        print("1. Cycle reset - outside spike window")
        
        # Step 2: Add stable market data
        base_price = 25000.0
        for i in range(100):
            # Small, consistent movements
            price_change = (i % 2) * 2 - 1  # -1, 1, -1, 1...
            tick = TickData(
                timestamp=datetime.now() - timedelta(seconds=100-i),
                bid=base_price + price_change,
                ask=base_price + price_change + 5,
                volume=100,  # Normal volume
                spread=5.0
            )
            self.data_handler.tick_buffer.append(tick)
            self.data_handler.price_buffer.append(tick.mid_price)
            self.data_handler.volume_buffer.append(tick.volume)
        
        # Step 3: Classify phase multiple times for stability
        for i in range(5):
            signal = self.classifier.classify_phase()
            time.sleep(0.01)
        
        print(f"2. Phase detected: {signal.phase.value}")
        print(f"3. Confidence: {signal.confidence:.2f}")
        print(f"4. Stability: {self.classifier.get_phase_stability():.2f}")
        print(f"5. Reasoning: {signal.reasoning}")
        
        # Step 4: Check trading decision
        should_trade, trade_reason = self.classifier.should_trade()
        print(f"6. Should trade: {should_trade} - {trade_reason}")
        
        # Verify calm detection
        self.assertEqual(signal.phase, MarketPhase.CALM)
        self.assertGreater(signal.confidence, 0.5)
    
    def test_phase_transitions(self):
        """Test phase transitions over time"""
        print("\n=== TESTING PHASE TRANSITIONS ===")
        
        phases_detected = []
        
        # Scenario 1: Start in CALM
        self.cycle_tracker.reset_cycle(0.01)
        self._add_calm_data()
        signal = self.classifier.classify_phase()
        phases_detected.append(signal.phase)
        print(f"1. Initial phase: {signal.phase.value}")
        
        # Scenario 2: Transition to HOLD (start cycle but low volatility)
        self.cycle_tracker.start_spike_countdown(datetime.now() - timedelta(seconds=850))
        signal = self.classifier.classify_phase()
        phases_detected.append(signal.phase)
        print(f"2. After cycle start: {signal.phase.value}")
        
        # Scenario 3: Transition to SPIKE (add high volatility)
        self._add_volatile_data()
        signal = self.classifier.classify_phase()
        phases_detected.append(signal.phase)
        print(f"3. After volatility increase: {signal.phase.value}")
        
        # Verify we saw different phases
        unique_phases = set(phases_detected)
        self.assertGreater(len(unique_phases), 1, "Should detect multiple phases")
        print(f"4. Phases detected: {[p.value for p in phases_detected]}")
    
    def _add_calm_data(self):
        """Add calm market data"""
        base_price = 25000.0
        for i in range(50):
            price_change = (i % 2) - 0.5  # Small changes
            tick = TickData(
                timestamp=datetime.now() - timedelta(seconds=50-i),
                bid=base_price + price_change,
                ask=base_price + price_change + 5,
                volume=100,
                spread=5.0
            )
            self.data_handler.price_buffer.append(tick.mid_price)
            self.data_handler.volume_buffer.append(tick.volume)
    
    def _add_volatile_data(self):
        """Add volatile market data"""
        base_price = 25000.0
        for i in range(30):
            price_change = (-1)**i * (i * 25)  # Large alternating moves
            tick = TickData(
                timestamp=datetime.now() - timedelta(seconds=30-i),
                bid=base_price + price_change,
                ask=base_price + price_change + 5,
                volume=250,  # Higher volume
                spread=5.0
            )
            self.data_handler.price_buffer.append(tick.mid_price)
            self.data_handler.volume_buffer.append(tick.volume)

def main():
    """Run all phase detection tests"""
    print("DEX900DN PHASE DETECTION SYSTEM - TEST SUITE")
    print("=" * 60)
    
    # Run unit tests
    unittest.main(argv=[''], exit=False, verbosity=2)
    
    print("\n" + "=" * 60)
    print("PHASE DETECTION TESTS COMPLETED!")
    print("=" * 60)

if __name__ == "__main__":
    main()
