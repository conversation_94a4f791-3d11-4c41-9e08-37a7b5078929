"""
Monte Carlo Simulation Engine for DEX900DN Trading System
Performs risk assessment and stop-loss optimization using Monte Carlo methods
"""

import logging
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, Any, List, Tuple, Optional
from dataclasses import dataclass
from concurrent.futures import ThreadPoolExecutor
import multiprocessing
import psutil

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))
from config.config import config

# Setup logging
logging.basicConfig(level=getattr(logging, config.LOG_LEVEL))
logger = logging.getLogger(__name__)

@dataclass
class SimulationParameters:
    """Parameters for Monte Carlo simulation"""
    num_simulations: int = 10000
    time_horizon_days: int = 30
    initial_price: float = 25000.0
    volatility: float = 0.15
    drift: float = 0.0
    confidence_level: float = 0.95
    
@dataclass
class SimulationResult:
    """Results from Monte Carlo simulation"""
    simulation_id: int
    final_price: float
    max_drawdown: float
    max_runup: float
    hit_stop_loss: bool
    hit_take_profit: bool
    days_to_exit: int
    total_return: float

@dataclass
class RiskMetrics:
    """Comprehensive risk metrics from simulation"""
    var_95: float  # Value at Risk at 95% confidence
    var_99: float  # Value at Risk at 99% confidence
    expected_return: float
    volatility: float
    max_drawdown: float
    sharpe_ratio: float
    win_rate: float
    profit_factor: float
    optimal_stop_loss: float
    optimal_take_profit: float

class MonteCarloSimulator:
    """
    Monte Carlo Simulation Engine for DEX900DN Risk Management
    
    Key Functions:
    - Simulate price paths using geometric Brownian motion
    - Optimize stop-loss levels for different market conditions
    - Calculate Value at Risk (VaR) and risk metrics
    - Assess position sizing recommendations
    """
    
    def __init__(self, num_cores: int = None):
        self.num_cores = num_cores or min(multiprocessing.cpu_count(), 8)
        self.cpu_threshold = 70.0  # Reduce cores if CPU usage > 70%
        self.simulation_cache = {}

        # Dramatically reduce simulation count for real-time performance
        self.calm_simulation_count = 50   # CALM phase: 50 simulations (was 1,250)
        self.spike_simulation_count = 200 # SPIKE phase: 200 simulations

        logger.info(f"MonteCarloSimulator initialized with {self.num_cores} cores")

    def _get_optimal_cores(self) -> int:
        """Get optimal number of cores based on current CPU usage"""
        try:
            cpu_usage = psutil.cpu_percent(interval=0.1)
            if cpu_usage > self.cpu_threshold:
                optimal_cores = max(1, self.num_cores // 2)
                logger.warning(f"🔥 High CPU usage ({cpu_usage:.1f}%) - reducing cores to {optimal_cores}")
                return optimal_cores
            return self.num_cores
        except:
            return self.num_cores

    def simulate_price_paths(self, params: SimulationParameters) -> List[np.ndarray]:
        """
        Generate Monte Carlo price paths using geometric Brownian motion
        
        Args:
            params: Simulation parameters
            
        Returns:
            List of price path arrays
        """
        logger.info(f"Generating {params.num_simulations} price paths...")

        # Get optimal cores based on current CPU usage
        optimal_cores = self._get_optimal_cores()

        # Use fewer cores for small simulation counts to reduce overhead
        if params.num_simulations <= 250:
            optimal_cores = min(optimal_cores, 2)

        # Time parameters
        dt = 1.0 / 365.0  # Daily time step
        num_steps = int(params.time_horizon_days)
        
        # Pre-calculate random numbers for efficiency
        np.random.seed(42)  # For reproducible results
        random_shocks = np.random.normal(0, 1, (params.num_simulations, num_steps))
        
        price_paths = []
        
        for i in range(params.num_simulations):
            # Initialize price path
            prices = np.zeros(num_steps + 1)
            prices[0] = params.initial_price
            
            # Generate path using geometric Brownian motion
            for t in range(1, num_steps + 1):
                # dS = S * (μ * dt + σ * dW)
                drift_component = params.drift * dt
                diffusion_component = params.volatility * np.sqrt(dt) * random_shocks[i, t-1]
                
                prices[t] = prices[t-1] * np.exp(drift_component + diffusion_component)
            
            price_paths.append(prices)
        
        logger.info(f"Generated {len(price_paths)} price paths")
        return price_paths
    
    def simulate_trading_strategy(self, price_paths: List[np.ndarray], 
                                 strategy_type: str, entry_price: float,
                                 stop_loss: float, take_profit: float) -> List[SimulationResult]:
        """
        Simulate trading strategy across multiple price paths
        
        Args:
            price_paths: List of price path arrays
            strategy_type: 'LONG' or 'SHORT'
            entry_price: Entry price for the trade
            stop_loss: Stop loss level
            take_profit: Take profit level
            
        Returns:
            List of simulation results
        """
        results = []
        
        for sim_id, prices in enumerate(price_paths):
            result = self._simulate_single_trade(
                sim_id, prices, strategy_type, entry_price, stop_loss, take_profit
            )
            results.append(result)
        
        return results
    
    def _simulate_single_trade(self, sim_id: int, prices: np.ndarray, 
                              strategy_type: str, entry_price: float,
                              stop_loss: float, take_profit: float) -> SimulationResult:
        """Simulate a single trade across one price path"""
        
        hit_stop_loss = False
        hit_take_profit = False
        days_to_exit = len(prices) - 1
        max_drawdown = 0.0
        max_runup = 0.0
        
        for day, price in enumerate(prices[1:], 1):  # Skip initial price
            if strategy_type == 'LONG':
                # Long position logic
                current_pnl = price - entry_price
                
                # Check stop loss
                if price <= stop_loss:
                    hit_stop_loss = True
                    days_to_exit = day
                    break
                
                # Check take profit
                if price >= take_profit:
                    hit_take_profit = True
                    days_to_exit = day
                    break
                
                # Track drawdown and runup
                if current_pnl < max_drawdown:
                    max_drawdown = current_pnl
                if current_pnl > max_runup:
                    max_runup = current_pnl
                    
            else:  # SHORT
                # Short position logic
                current_pnl = entry_price - price
                
                # Check stop loss (price goes up)
                if price >= stop_loss:
                    hit_stop_loss = True
                    days_to_exit = day
                    break
                
                # Check take profit (price goes down)
                if price <= take_profit:
                    hit_take_profit = True
                    days_to_exit = day
                    break
                
                # Track drawdown and runup
                if current_pnl < max_drawdown:
                    max_drawdown = current_pnl
                if current_pnl > max_runup:
                    max_runup = current_pnl
        
        # Calculate final return
        final_price = prices[min(days_to_exit, len(prices) - 1)]
        if strategy_type == 'LONG':
            total_return = final_price - entry_price
        else:
            total_return = entry_price - final_price
        
        return SimulationResult(
            simulation_id=sim_id,
            final_price=final_price,
            max_drawdown=max_drawdown,
            max_runup=max_runup,
            hit_stop_loss=hit_stop_loss,
            hit_take_profit=hit_take_profit,
            days_to_exit=days_to_exit,
            total_return=total_return
        )
    
    def optimize_stop_loss(self, strategy_type: str, entry_price: float,
                          take_profit: float, volatility: float,
                          num_simulations: int = None) -> Dict[str, Any]:
        """
        Optimize stop-loss level using Monte Carlo simulation
        
        Args:
            strategy_type: 'LONG' or 'SHORT'
            entry_price: Entry price for optimization
            take_profit: Take profit level
            volatility: Market volatility
            num_simulations: Number of simulations (default from config)
            
        Returns:
            Dict with optimization results
        """
        # Use reduced simulations for stop-loss optimization
        num_simulations = self.calm_simulation_count  # 50 simulations for speed

        logger.info(f"Optimizing stop-loss for {strategy_type} position ({num_simulations} sims)...")
        
        # Define stop-loss levels to test
        if strategy_type == 'LONG':
            stop_loss_levels = np.arange(
                entry_price - 500,  # 500 points below entry
                entry_price - 50,   # 50 points below entry
                25  # 25 point increments
            )
        else:  # SHORT
            stop_loss_levels = np.arange(
                entry_price + 50,   # 50 points above entry
                entry_price + 500,  # 500 points above entry
                25  # 25 point increments
            )
        
        # Simulation parameters
        params = SimulationParameters(
            num_simulations=num_simulations,
            time_horizon_days=30,
            initial_price=entry_price,
            volatility=volatility,
            drift=0.0
        )
        
        # Generate price paths
        price_paths = self.simulate_price_paths(params)
        
        optimization_results = []
        
        for stop_loss in stop_loss_levels:
            # Run simulation for this stop-loss level
            results = self.simulate_trading_strategy(
                price_paths, strategy_type, entry_price, stop_loss, take_profit
            )
            
            # Calculate metrics for this stop-loss level
            metrics = self._calculate_optimization_metrics(results)
            metrics['stop_loss_level'] = stop_loss
            optimization_results.append(metrics)
        
        # Find optimal stop-loss (maximize risk-adjusted return)
        best_result = max(optimization_results, 
                         key=lambda x: x['risk_adjusted_return'])
        
        logger.info(f"Optimal stop-loss: {best_result['stop_loss_level']:.1f}")
        
        return {
            'optimal_stop_loss': best_result['stop_loss_level'],
            'expected_return': best_result['expected_return'],
            'win_rate': best_result['win_rate'],
            'risk_adjusted_return': best_result['risk_adjusted_return'],
            'max_drawdown': best_result['max_drawdown'],
            'all_results': optimization_results
        }
    
    def _calculate_optimization_metrics(self, results: List[SimulationResult]) -> Dict[str, float]:
        """Calculate optimization metrics from simulation results"""
        returns = [r.total_return for r in results]
        winning_trades = [r for r in results if r.total_return > 0]
        
        expected_return = np.mean(returns)
        return_std = np.std(returns)
        win_rate = len(winning_trades) / len(results)
        max_drawdown = min([r.max_drawdown for r in results])
        
        # Risk-adjusted return (Sharpe-like ratio)
        risk_adjusted_return = expected_return / return_std if return_std > 0 else 0
        
        return {
            'expected_return': expected_return,
            'return_std': return_std,
            'win_rate': win_rate,
            'max_drawdown': max_drawdown,
            'risk_adjusted_return': risk_adjusted_return
        }
    
    def calculate_var(self, returns: List[float], confidence_levels: List[float] = None) -> Dict[str, float]:
        """
        Calculate Value at Risk (VaR) at different confidence levels
        
        Args:
            returns: List of returns
            confidence_levels: Confidence levels (default: [0.95, 0.99])
            
        Returns:
            Dict with VaR values
        """
        confidence_levels = confidence_levels or [0.95, 0.99]
        
        returns_array = np.array(returns)
        var_results = {}
        
        for confidence in confidence_levels:
            # VaR is the negative of the percentile
            percentile = (1 - confidence) * 100
            var_value = -np.percentile(returns_array, percentile)
            var_results[f'var_{int(confidence*100)}'] = var_value
        
        return var_results
    
    def run_comprehensive_risk_assessment(self, strategy_type: str, 
                                        entry_price: float, volatility: float,
                                        num_simulations: int = None) -> RiskMetrics:
        """
        Run comprehensive risk assessment using Monte Carlo simulation
        
        Args:
            strategy_type: 'LONG' or 'SHORT'
            entry_price: Entry price
            volatility: Market volatility
            num_simulations: Number of simulations
            
        Returns:
            RiskMetrics object with comprehensive risk analysis
        """
        num_simulations = num_simulations or config.MONTE_CARLO_RUNS

        # Use phase-specific simulation counts for optimal performance
        if strategy_type == "CALM":
            actual_simulations = self.calm_simulation_count  # 50 simulations
        else:
            actual_simulations = self.spike_simulation_count  # 200 simulations

        logger.info(f"Running {strategy_type} phase risk assessment ({actual_simulations} simulations)...")

        # Simulation parameters
        params = SimulationParameters(
            num_simulations=actual_simulations,
            time_horizon_days=30,
            initial_price=entry_price,
            volatility=volatility,
            drift=0.0
        )
        
        # Generate price paths
        price_paths = self.simulate_price_paths(params)
        
        # Test multiple stop-loss and take-profit combinations
        if strategy_type == 'LONG':
            stop_loss = entry_price - config.CALM_STOP_LOSS
            take_profit = entry_price + config.CALM_TAKE_PROFIT_MAX
        else:  # SHORT
            stop_loss = entry_price + config.SPIKE_STOP_LOSS_MIN
            take_profit = entry_price - config.SPIKE_TAKE_PROFIT
        
        # Run simulation
        results = self.simulate_trading_strategy(
            price_paths, strategy_type, entry_price, stop_loss, take_profit
        )
        
        # Calculate comprehensive metrics
        returns = [r.total_return for r in results]
        var_metrics = self.calculate_var(returns)
        
        winning_trades = [r for r in results if r.total_return > 0]
        losing_trades = [r for r in results if r.total_return < 0]
        
        expected_return = np.mean(returns)
        volatility_metric = np.std(returns)
        win_rate = len(winning_trades) / len(results)
        
        # Profit factor
        total_profits = sum([r.total_return for r in winning_trades])
        total_losses = abs(sum([r.total_return for r in losing_trades]))
        profit_factor = total_profits / total_losses if total_losses > 0 else float('inf')
        
        # Sharpe ratio (assuming risk-free rate = 0)
        sharpe_ratio = expected_return / volatility_metric if volatility_metric > 0 else 0
        
        # Maximum drawdown
        max_drawdown = min([r.max_drawdown for r in results])
        
        # Optimize stop-loss
        optimization = self.optimize_stop_loss(
            strategy_type, entry_price, take_profit, volatility, num_simulations
        )
        
        return RiskMetrics(
            var_95=var_metrics['var_95'],
            var_99=var_metrics['var_99'],
            expected_return=expected_return,
            volatility=volatility_metric,
            max_drawdown=max_drawdown,
            sharpe_ratio=sharpe_ratio,
            win_rate=win_rate,
            profit_factor=profit_factor,
            optimal_stop_loss=optimization['optimal_stop_loss'],
            optimal_take_profit=take_profit
        )
    
    def get_simulation_summary(self, results: List[SimulationResult]) -> Dict[str, Any]:
        """Generate summary statistics from simulation results"""
        returns = [r.total_return for r in results]
        
        return {
            'total_simulations': len(results),
            'expected_return': np.mean(returns),
            'return_std': np.std(returns),
            'min_return': np.min(returns),
            'max_return': np.max(returns),
            'win_rate': len([r for r in results if r.total_return > 0]) / len(results),
            'avg_days_to_exit': np.mean([r.days_to_exit for r in results]),
            'stop_loss_hit_rate': len([r for r in results if r.hit_stop_loss]) / len(results),
            'take_profit_hit_rate': len([r for r in results if r.hit_take_profit]) / len(results)
        }
