#!/usr/bin/env python3
"""
Quick test to verify VaR calculation fixes
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.risk.monte_carlo import MonteCarloSimulator, SimulationParameters
from src.risk.risk_manager import IntegratedRiskManager
from src.risk.position_sizing import AccountInfo

def test_var_calculation():
    """Test VaR calculation with corrected percentage returns"""
    print("🧪 Testing VaR Calculation Fix")
    print("=" * 50)
    
    # Initialize components
    simulator = MonteCarloSimulator()
    risk_manager = IntegratedRiskManager()
    
    # Test account info
    account = AccountInfo(
        balance=4810.17,
        equity=4810.17,
        margin_used=0.0,
        margin_available=4810.17,
        max_risk_per_trade=0.02,  # 2% per trade
        max_portfolio_risk=0.06
    )
    
    # Test parameters
    entry_price = 67000.0
    stop_loss = 66700.0  # 300 points SL
    take_profit = 67050.0  # 50 points TP
    volatility = 0.15
    
    print(f"📊 Test Parameters:")
    print(f"   Entry Price: {entry_price:,.0f}")
    print(f"   Stop Loss: {stop_loss:,.0f} ({entry_price - stop_loss:.0f} points)")
    print(f"   Take Profit: {take_profit:,.0f} ({take_profit - entry_price:.0f} points)")
    print(f"   Account Balance: ${account.balance:,.2f}")
    print(f"   Max Risk per Trade: {account.max_risk_per_trade:.1%}")
    print()
    
    # Run Monte Carlo risk assessment
    print("🎲 Running Monte Carlo Risk Assessment...")
    try:
        risk_metrics = simulator.run_comprehensive_risk_assessment(
            'LONG', entry_price, volatility, num_simulations=50
        )
        
        print("✅ Monte Carlo completed successfully!")
        print(f"   VaR 95%: {risk_metrics.var_95:.2%}")
        print(f"   VaR 99%: {risk_metrics.var_99:.2%}")
        print(f"   Expected Return: {risk_metrics.expected_return:.2%}")
        print(f"   Win Rate: {risk_metrics.win_rate:.1%}")
        print(f"   Sharpe Ratio: {risk_metrics.sharpe_ratio:.3f}")
        print(f"   Max Drawdown: {risk_metrics.max_drawdown:.2%}")
        print()
        
        # Test risk decision
        print("🛡️ Testing Risk Management Decision...")
        risk_manager.account_info = account
        
        risk_decision = risk_manager.assess_trade_risk(
            'CALM', entry_price, stop_loss, take_profit, volatility
        )
        
        print(f"   Decision: {'✅ APPROVED' if risk_decision.allow_trade else '❌ REJECTED'}")
        print(f"   Position Size: {risk_decision.position_size:.4f} lots")
        print(f"   Risk Level: {risk_decision.risk_level}")
        print(f"   Confidence: {risk_decision.confidence:.1%}")
        print(f"   Reasoning: {risk_decision.reasoning}")
        
        # Calculate actual risk
        if risk_decision.position_size > 0:
            risk_dollars = risk_decision.position_size * (entry_price - stop_loss) * 1.0  # $1 per point
            risk_percent = risk_dollars / account.balance
            print(f"   Actual Risk: ${risk_dollars:.2f} ({risk_percent:.2%} of account)")
        
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_var_calculation()
