"""
Phase Classifier for DEX900DN Trading System
Determines CALM vs SPIKE vs HOLD phases based on cycle timing and market conditions
"""

import logging
from datetime import datetime
from typing import Tuple, Dict, Any, Optional
from enum import Enum
from dataclasses import dataclass

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))
from config.config import config
from src.modules.cycle_engine import TimeCycleTracker
from src.modules.data_feed import DataHandler

# Setup logging
logging.basicConfig(level=getattr(logging, config.LOG_LEVEL))
logger = logging.getLogger(__name__)

class MarketPhase(Enum):
    """Market phase enumeration"""
    CALM = "CALM"
    SPIKE = "SPIKE"
    HOLD = "HOLD"

@dataclass
class PhaseSignal:
    """Data class for phase detection signals"""
    phase: MarketPhase
    confidence: float
    timestamp: datetime
    volatility: float
    in_spike_window: bool
    volume_spike_detected: bool
    price_drop_detected: bool
    reasoning: str

class PhaseClassifier:
    """
    Phase Classifier for DEX900DN Trading System
    
    Core Logic (from your specifications):
    if spike_window_alert() and volatility > 0.15: 
        return "SPIKE"
    elif volatility < 0.05 and not spike_window_alert():
        return "CALM"
    else:
        return "HOLD"
    """
    
    def __init__(self, cycle_tracker: TimeCycleTracker = None, data_handler: DataHandler = None):
        self.cycle_tracker = cycle_tracker
        self.data_handler = data_handler
        
        # Phase detection parameters
        self.spike_volatility_threshold = config.SPIKE_VOLATILITY_THRESHOLD  # 0.15
        self.calm_volatility_threshold = config.CALM_VOLATILITY_THRESHOLD    # 0.05
        self.min_spike_drop = config.MIN_SPIKE_DROP                          # 500 points
        
        # Phase history for stability
        self.phase_history = []
        self.max_history_length = 10
        
        # Current phase state
        self.current_phase = MarketPhase.HOLD
        self.last_phase_change = datetime.now()
        self.phase_duration = 0.0
        
        logger.info("PhaseClassifier initialized")
        logger.info(f"Spike volatility threshold: {self.spike_volatility_threshold}")
        logger.info(f"Calm volatility threshold: {self.calm_volatility_threshold}")
    
    def classify_phase(self, market_data: Dict[str, Any] = None) -> PhaseSignal:
        """
        Main phase classification logic

        Args:
            market_data: Optional market data (for compatibility)

        Returns:
            PhaseSignal: Complete phase detection result
        """
        current_time = datetime.now()
        
        # Get current market data
        volatility = self._get_current_volatility()
        in_spike_window = self._is_in_spike_window()
        volume_spike = self._detect_volume_spike()
        price_drop_detected, drop_amount = self._detect_price_drop()
        
        # Apply core classification logic
        phase, confidence, reasoning = self._apply_classification_logic(
            volatility, in_spike_window, volume_spike, price_drop_detected, drop_amount
        )
        
        # Create phase signal
        signal = PhaseSignal(
            phase=phase,
            confidence=confidence,
            timestamp=current_time,
            volatility=volatility,
            in_spike_window=in_spike_window,
            volume_spike_detected=volume_spike,
            price_drop_detected=price_drop_detected,
            reasoning=reasoning
        )
        
        # Update phase history and state
        self._update_phase_state(signal)
        
        # Log phase changes
        if phase != self.current_phase:
            logger.warning(f"PHASE CHANGE: {self.current_phase.value} -> {phase.value}")
            logger.info(f"Reasoning: {reasoning}")
            self.current_phase = phase
            self.last_phase_change = current_time
        
        return signal
    
    def _apply_classification_logic(self, volatility: float, in_spike_window: bool, 
                                  volume_spike: bool, price_drop_detected: bool, 
                                  drop_amount: float) -> Tuple[MarketPhase, float, str]:
        """
        Apply the core classification logic from your specifications
        
        Returns:
            Tuple[MarketPhase, confidence, reasoning]
        """
        
        # SPIKE Phase Detection
        # Primary condition: spike window + high volatility
        if in_spike_window and volatility > self.spike_volatility_threshold:
            confidence = min(0.9, 0.5 + (volatility / self.spike_volatility_threshold) * 0.4)
            reasoning = f"Spike window active + high volatility ({volatility:.4f} > {self.spike_volatility_threshold})"
            
            # Boost confidence with additional signals
            if volume_spike:
                confidence = min(0.95, confidence + 0.1)
                reasoning += " + volume spike"
            
            if price_drop_detected:
                confidence = min(0.98, confidence + 0.1)
                reasoning += f" + price drop ({drop_amount:.1f} points)"
            
            return MarketPhase.SPIKE, confidence, reasoning
        
        # Enhanced SPIKE detection: Major price drop even outside window
        if price_drop_detected and drop_amount >= self.min_spike_drop:
            # Lower volatility threshold for major price drops
            min_volatility_for_drop = 0.02 if drop_amount >= self.min_spike_drop * 2 else 0.05

            if volatility > min_volatility_for_drop:
                confidence = 0.8 if in_spike_window else 0.6
                reasoning = f"Major price drop ({drop_amount:.1f} points) + elevated volatility ({volatility:.4f})"

                if volume_spike:
                    confidence = min(0.9, confidence + 0.1)
                    reasoning += " + volume spike"

                return MarketPhase.SPIKE, confidence, reasoning
            else:
                # Even with low volatility, very large drops should trigger spike
                if drop_amount >= self.min_spike_drop * 1.5:
                    confidence = 0.7 if in_spike_window else 0.5
                    reasoning = f"Massive price drop ({drop_amount:.1f} points) despite low volatility"
                    return MarketPhase.SPIKE, confidence, reasoning
        
        # CALM Phase Detection
        # Primary condition: low volatility + not in spike window + no major price drops
        if (volatility < self.calm_volatility_threshold and
            not in_spike_window and
            not (price_drop_detected and drop_amount >= self.min_spike_drop)):

            confidence = min(0.9, 0.6 + (self.calm_volatility_threshold - volatility) / self.calm_volatility_threshold * 0.3)
            reasoning = f"Low volatility ({volatility:.4f} < {self.calm_volatility_threshold}) + outside spike window"

            # Reduce confidence if there are conflicting signals
            if volume_spike:
                confidence *= 0.8
                reasoning += " (reduced: volume spike detected)"

            return MarketPhase.CALM, confidence, reasoning
        
        # Check for price drop that should trigger SPIKE even with low volatility
        if price_drop_detected and drop_amount >= self.min_spike_drop:
            confidence = 0.6
            reasoning = f"Significant price drop ({drop_amount:.1f} points) with low volatility - potential spike"
            return MarketPhase.SPIKE, confidence, reasoning

        # HOLD Phase (default/transition state)
        # All other conditions
        confidence = 0.5
        reasoning_parts = []

        if in_spike_window and volatility <= self.spike_volatility_threshold:
            reasoning_parts.append(f"In spike window but volatility too low ({volatility:.4f})")
        elif not in_spike_window and volatility >= self.calm_volatility_threshold:
            reasoning_parts.append(f"Outside spike window but volatility too high ({volatility:.4f})")
        else:
            reasoning_parts.append("Transitional market conditions")

        if volume_spike:
            reasoning_parts.append("volume spike present")

        reasoning = " + ".join(reasoning_parts) if reasoning_parts else "Neutral conditions"

        return MarketPhase.HOLD, confidence, reasoning
    
    def _get_current_volatility(self) -> float:
        """Get current market volatility with enhanced calculation"""
        if self.data_handler:
            # Try shorter window first for more responsive detection
            short_vol = self.data_handler.calculate_volatility(1)  # 1-minute volatility
            long_vol = self.data_handler.calculate_volatility(5)   # 5-minute volatility

            # Use the higher of the two for spike detection
            return max(short_vol, long_vol)
        return 0.0
    
    def _is_in_spike_window(self) -> bool:
        """Check if currently in spike alert window"""
        if self.cycle_tracker:
            return self.cycle_tracker.spike_window_alert()
        return False
    
    def _detect_volume_spike(self) -> bool:
        """Detect volume spike"""
        if self.data_handler:
            return self.data_handler.detect_volume_spike()
        return False
    
    def _detect_price_drop(self) -> Tuple[bool, float]:
        """Detect significant price drops"""
        if self.data_handler:
            return self.data_handler.detect_price_drop()
        return False, 0.0
    
    def _update_phase_state(self, signal: PhaseSignal) -> None:
        """Update phase history and state tracking"""
        # Add to history
        self.phase_history.append(signal)
        
        # Maintain history length
        if len(self.phase_history) > self.max_history_length:
            self.phase_history.pop(0)
        
        # Update phase duration
        if signal.phase == self.current_phase:
            self.phase_duration = (signal.timestamp - self.last_phase_change).total_seconds()
        else:
            self.phase_duration = 0.0
    
    def get_phase_stability(self) -> float:
        """
        Calculate phase stability based on recent history
        
        Returns:
            float: Stability score (0.0 to 1.0)
        """
        if len(self.phase_history) < 3:
            return 0.5
        
        recent_phases = [signal.phase for signal in self.phase_history[-5:]]
        most_common_phase = max(set(recent_phases), key=recent_phases.count)
        stability = recent_phases.count(most_common_phase) / len(recent_phases)
        
        return stability
    
    def get_phase_summary(self) -> Dict[str, Any]:
        """
        Get comprehensive phase analysis summary
        
        Returns:
            Dict containing current phase information
        """
        current_signal = self.classify_phase()
        stability = self.get_phase_stability()
        
        return {
            "current_phase": current_signal.phase.value,
            "confidence": current_signal.confidence,
            "stability": stability,
            "volatility": current_signal.volatility,
            "in_spike_window": current_signal.in_spike_window,
            "volume_spike_detected": current_signal.volume_spike_detected,
            "price_drop_detected": current_signal.price_drop_detected,
            "reasoning": current_signal.reasoning,
            "phase_duration": self.phase_duration,
            "last_phase_change": self.last_phase_change,
            "timestamp": current_signal.timestamp
        }
    
    def should_trade(self) -> Tuple[bool, str]:
        """
        Determine if trading should be active based on current phase
        
        Returns:
            Tuple[bool, reason]: (should_trade, reason)
        """
        signal = self.classify_phase()
        stability = self.get_phase_stability()
        
        # Don't trade in HOLD phase
        if signal.phase == MarketPhase.HOLD:
            return False, "Market in HOLD phase - waiting for clear signals"
        
        # Require minimum confidence
        if signal.confidence < 0.6:
            return False, f"Low confidence ({signal.confidence:.2f}) - waiting for stronger signals"
        
        # Require minimum stability
        if stability < 0.6:
            return False, f"Low phase stability ({stability:.2f}) - waiting for consistent signals"
        
        # All conditions met
        return True, f"{signal.phase.value} phase confirmed with {signal.confidence:.2f} confidence"
    
    def reset_phase_history(self) -> None:
        """Reset phase history (useful for testing or system restart)"""
        self.phase_history.clear()
        self.current_phase = MarketPhase.HOLD
        self.last_phase_change = datetime.now()
        self.phase_duration = 0.0
        logger.info("Phase history reset")

    def debug_classification(self) -> Dict[str, Any]:
        """Get detailed debug information about current classification"""
        volatility = self._get_current_volatility()
        in_spike_window = self._is_in_spike_window()
        volume_spike = self._detect_volume_spike()
        price_drop_detected, drop_amount = self._detect_price_drop()

        return {
            "volatility": volatility,
            "spike_threshold": self.spike_volatility_threshold,
            "calm_threshold": self.calm_volatility_threshold,
            "in_spike_window": in_spike_window,
            "volume_spike": volume_spike,
            "price_drop_detected": price_drop_detected,
            "drop_amount": drop_amount,
            "min_spike_drop": self.min_spike_drop,
            "data_points": len(self.data_handler.price_buffer) if self.data_handler else 0
        }

# Global phase classifier instance
def create_phase_classifier(cycle_tracker: TimeCycleTracker = None, 
                          data_handler: DataHandler = None) -> PhaseClassifier:
    """Factory function to create phase classifier with dependencies"""
    return PhaseClassifier(cycle_tracker, data_handler)
