"""
Debug script for phase detection issues
"""

import sys
import os
from datetime import datetime, timedelta

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))

from src.modules.cycle_engine import TimeCycleTracker
from src.modules.data_feed import DataHandler, TickData
from src.modules.phase_classifier import PhaseClassifier, MarketPhase
from config.config import config

def debug_spike_detection():
    """Debug spike detection scenario"""
    print("=== DEBUGGING SPIKE DETECTION ===")
    
    # Setup
    cycle_tracker = TimeCycleTracker()
    data_handler = DataHandler()
    classifier = PhaseClassifier(cycle_tracker, data_handler)
    
    # Step 1: Set up spike window
    cycle_tracker.start_spike_countdown(datetime.now() - timedelta(seconds=850))
    print(f"1. In spike window: {cycle_tracker.spike_window_alert()}")
    
    # Step 2: Add highly volatile data
    base_price = 25000.0
    print("2. Adding volatile price data...")
    
    for i in range(50):
        # Create extreme price movements
        price_change = (-1)**i * (i * 50)  # Very large alternating moves
        tick = TickData(
            timestamp=datetime.now() - timedelta(seconds=50-i),
            bid=base_price + price_change,
            ask=base_price + price_change + 5,
            volume=300,  # High volume
            spread=5.0
        )
        data_handler.tick_buffer.append(tick)
        data_handler.price_buffer.append(tick.mid_price)
        data_handler.volume_buffer.append(tick.volume)
    
    # Step 3: Debug classification
    debug_info = classifier.debug_classification()
    print("3. Debug information:")
    for key, value in debug_info.items():
        print(f"   {key}: {value}")
    
    # Step 4: Classify
    signal = classifier.classify_phase()
    print(f"4. Phase: {signal.phase.value}")
    print(f"5. Confidence: {signal.confidence:.3f}")
    print(f"6. Reasoning: {signal.reasoning}")
    
    # Step 5: Manual volatility check
    print("7. Manual volatility calculations:")
    vol_1min = data_handler.calculate_volatility(1)
    vol_5min = data_handler.calculate_volatility(5)
    print(f"   1-minute volatility: {vol_1min:.6f}")
    print(f"   5-minute volatility: {vol_5min:.6f}")
    print(f"   Spike threshold: {config.SPIKE_VOLATILITY_THRESHOLD}")
    
    # Step 6: Check price drop detection
    price_drop, drop_amount = data_handler.detect_price_drop()
    print(f"8. Price drop detected: {price_drop}, amount: {drop_amount:.1f}")
    
    return signal.phase == MarketPhase.SPIKE

def debug_price_drop_detection():
    """Debug price drop spike detection"""
    print("\n=== DEBUGGING PRICE DROP DETECTION ===")
    
    # Setup
    cycle_tracker = TimeCycleTracker()
    data_handler = DataHandler()
    classifier = PhaseClassifier(cycle_tracker, data_handler)
    
    # Clear existing data
    data_handler.price_buffer.clear()
    
    # Add major price drop
    base_price = 25000.0
    print("1. Adding major price drop...")
    
    for i in range(10):
        drop_price = base_price - (i * 60)  # 600 point drop in 10 seconds
        data_handler.price_buffer.append(drop_price)
        print(f"   Price at second {i}: {drop_price}")
    
    # Debug classification
    debug_info = classifier.debug_classification()
    print("2. Debug information:")
    for key, value in debug_info.items():
        print(f"   {key}: {value}")
    
    # Classify
    signal = classifier.classify_phase()
    print(f"3. Phase: {signal.phase.value}")
    print(f"4. Confidence: {signal.confidence:.3f}")
    print(f"5. Reasoning: {signal.reasoning}")
    
    return signal.phase == MarketPhase.SPIKE

def test_volatility_calculation():
    """Test volatility calculation with known data"""
    print("\n=== TESTING VOLATILITY CALCULATION ===")
    
    data_handler = DataHandler()
    data_handler.price_buffer.clear()
    
    # Add known volatile data
    prices = [25000, 24950, 25050, 24900, 25100, 24850, 25150, 24800, 25200]
    print(f"1. Test prices: {prices}")
    
    for price in prices:
        data_handler.price_buffer.append(price)
    
    vol = data_handler.calculate_volatility(1)  # 1 minute (but we have 9 data points)
    print(f"2. Calculated volatility: {vol:.6f}")
    print(f"3. Spike threshold: {config.SPIKE_VOLATILITY_THRESHOLD}")
    print(f"4. Above threshold: {vol > config.SPIKE_VOLATILITY_THRESHOLD}")
    
    # Manual calculation for verification
    import numpy as np
    prices_array = np.array(prices)
    returns = np.diff(prices_array) / prices_array[:-1]
    manual_vol = np.std(returns)
    print(f"5. Manual volatility calculation: {manual_vol:.6f}")

def main():
    """Run all debug tests"""
    print("DEX900DN PHASE DETECTION - DEBUG SESSION")
    print("=" * 50)
    
    # Test volatility calculation
    test_volatility_calculation()
    
    # Test spike detection
    spike_success = debug_spike_detection()
    
    # Test price drop detection
    drop_success = debug_price_drop_detection()
    
    print("\n" + "=" * 50)
    print("DEBUG RESULTS:")
    print(f"Spike detection working: {spike_success}")
    print(f"Price drop detection working: {drop_success}")
    print("=" * 50)

if __name__ == "__main__":
    main()
