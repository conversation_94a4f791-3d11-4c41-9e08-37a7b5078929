"""
DEX900DN Trading System Configuration
Core parameters for the time-cycle driven + phase-specific execution system
"""

import os
from typing import Dict, Any

class TradingConfig:
    """Main configuration class for DEX900DN trading system"""
    
    # ==================== CORE SYSTEM PARAMETERS ====================
    
    # Asset Configuration
    SYMBOL = "DEX 900 DOWN Index"  # DEX 900 DOWN Index
    TIMEFRAME = "1s"     # 1-second resolution for tick data
    
    # ==================== TIME-CYCLE PARAMETERS ====================
    
    # Spike Cycle Timing (from your specifications)
    SPIKE_CYCLE_DURATION = 900  # 900 seconds (15 minutes)
    SPIKE_WINDOW_START = 840    # 14 minutes (14*60 = 840 seconds)
    SPIKE_WINDOW_END = 960      # 16 minutes (16*60 = 960 seconds)
    
    # ==================== VOLATILITY THRESHOLDS ====================
    
    # Phase Detection Thresholds
    SPIKE_VOLATILITY_THRESHOLD = 0.15  # 15% volatility for SPIKE phase
    CALM_VOLATILITY_THRESHOLD = 0.05   # 5% volatility for CALM phase
    VOLATILITY_WINDOW = 300             # 5 minutes for volatility calculation
    
    # ==================== SPIKE DETECTION PARAMETERS ====================
    
    # Minimum spike requirements
    MIN_SPIKE_DROP = 500        # 500 points minimum drop
    SPIKE_TIME_WINDOW = 10      # 10 seconds for spike detection
    VOLUME_SPIKE_MULTIPLIER = 2.0  # 200% of baseline volume
    VOLUME_BASELINE_WINDOW = 1800   # 30 minutes for volume baseline
    
    # ==================== TRADING STRATEGY PARAMETERS ====================
    
    # Bollinger Bands (for CALM phase)
    BB_PERIOD = 20
    BB_DEVIATION = 1.0
    
    # RSI Parameters
    RSI_PERIOD = 14
    RSI_THRESHOLD = 50
    
    # ==================== POSITION SIZING ====================
    
    # Risk per phase
    CALM_PHASE_RISK = 0.05  # 5% risk for calm phase
    SPIKE_PHASE_RISK = 0.01 # 1% risk for spike phase
    
    # ==================== PROFIT/LOSS PARAMETERS ====================
    
    # CALM Phase P/L
    CALM_TAKE_PROFIT_MIN = 50   # 50 points minimum TP
    CALM_TAKE_PROFIT_MAX = 100  # 100 points maximum TP
    CALM_STOP_LOSS = 300        # 300 points SL
    
    # SPIKE Phase P/L
    SPIKE_TAKE_PROFIT = 500     # 500 points TP
    SPIKE_PARTIAL_CLOSE = 0.5   # Close 50% at first TP
    SPIKE_TRAILING_STOP = 200   # 200 points trailing stop
    SPIKE_STOP_LOSS_MIN = 1500  # 1500 points minimum SL
    SPIKE_STOP_LOSS_MAX = 3000  # 3000 points maximum SL
    
    # ==================== RISK MANAGEMENT ====================
    
    # Daily limits
    DAILY_PROFIT_LIMIT = 0.02   # 2% daily profit limit
    DAILY_LOSS_LIMIT = -0.02    # 2% daily loss limit
    
    # Monte Carlo simulation parameters
    MONTE_CARLO_RUNS = 10000
    CONFIDENCE_LEVEL = 0.95
    
    # ==================== API CONFIGURATION ====================

    # MT5 Configuration
    MT5_LOGIN = os.getenv('MT5_LOGIN', '')
    MT5_PASSWORD = os.getenv('MT5_PASSWORD', '')
    MT5_SERVER = os.getenv('MT5_SERVER', '')
    MAGIC_NUMBER = 900900  # Unique identifier for DEX900DN trades
    MAX_SLIPPAGE = 20      # Maximum slippage in points
    
    # Deriv API Configuration
    DERIV_API_TOKEN = os.getenv('DERIV_API_TOKEN', '')
    DERIV_APP_ID = os.getenv('DERIV_APP_ID', '')
    
    # ==================== LOGGING CONFIGURATION ====================
    
    LOG_LEVEL = "INFO"
    LOG_FORMAT = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    LOG_FILE = "logs/dex900dn_trading.log"
    
    # ==================== DATA STORAGE ====================
    
    DATA_DIR = "src/data"
    HISTORICAL_DATA_FILE = "src/data/dex900dn_historical.csv"
    LIVE_DATA_BUFFER_SIZE = 1000  # Keep last 1000 ticks in memory
    
    @classmethod
    def get_config_dict(cls) -> Dict[str, Any]:
        """Return all configuration as dictionary"""
        config = {}
        for attr in dir(cls):
            if not attr.startswith('_') and not callable(getattr(cls, attr)):
                config[attr] = getattr(cls, attr)
        return config
    
    @classmethod
    def validate_config(cls) -> bool:
        """Validate critical configuration parameters"""
        required_params = [
            'SYMBOL', 'SPIKE_CYCLE_DURATION', 'SPIKE_VOLATILITY_THRESHOLD',
            'MIN_SPIKE_DROP', 'CALM_PHASE_RISK', 'SPIKE_PHASE_RISK'
        ]
        
        for param in required_params:
            if not hasattr(cls, param):
                raise ValueError(f"Missing required configuration parameter: {param}")
        
        # Validate ranges
        if cls.SPIKE_WINDOW_START >= cls.SPIKE_WINDOW_END:
            raise ValueError("SPIKE_WINDOW_START must be less than SPIKE_WINDOW_END")
        
        if cls.CALM_VOLATILITY_THRESHOLD >= cls.SPIKE_VOLATILITY_THRESHOLD:
            raise ValueError("CALM_VOLATILITY_THRESHOLD must be less than SPIKE_VOLATILITY_THRESHOLD")
        
        return True

# Create global config instance
config = TradingConfig()
