"""
Advanced Position Sizing for DEX900DN Trading System
Dynamic position sizing based on volatility, account balance, and Monte Carlo results
"""

import logging
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, Tuple, List
from dataclasses import dataclass
from enum import Enum

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))
from config.config import config
from src.risk.monte_carlo import MonteCarloSimulator, RiskMetrics

# Setup logging
logging.basicConfig(level=getattr(logging, config.LOG_LEVEL))
logger = logging.getLogger(__name__)

class SizingMethod(Enum):
    """Position sizing methods"""
    FIXED_PERCENT = "FIXED_PERCENT"
    KELLY_CRITERION = "KELLY_CRITERION"
    VOLATILITY_ADJUSTED = "VOLATILITY_ADJUSTED"
    MONTE_CARLO_OPTIMIZED = "MONTE_CARLO_OPTIMIZED"
    RISK_PARITY = "RISK_PARITY"

@dataclass
class AccountInfo:
    """Account information for position sizing"""
    balance: float
    equity: float
    margin_used: float
    margin_available: float
    max_risk_per_trade: float
    max_portfolio_risk: float

@dataclass
class PositionSizeResult:
    """Result from position sizing calculation"""
    position_size: float
    risk_amount: float
    sizing_method: SizingMethod
    confidence: float
    reasoning: str
    max_loss_potential: float
    kelly_fraction: Optional[float] = None
    volatility_adjustment: Optional[float] = None

class AdvancedPositionSizer:
    """
    Advanced Position Sizing Engine for DEX900DN Trading System
    
    Features:
    - Multiple sizing methods (Fixed %, Kelly, Volatility-adjusted, Monte Carlo)
    - Dynamic adjustment based on market conditions
    - Risk-based position sizing with Monte Carlo optimization
    - Account balance and drawdown protection
    """
    
    def __init__(self, monte_carlo_simulator: MonteCarloSimulator = None):
        self.monte_carlo = monte_carlo_simulator or MonteCarloSimulator()
        
        # Default account settings (can be updated)
        self.default_account = AccountInfo(
            balance=10000.0,
            equity=10000.0,
            margin_used=0.0,
            margin_available=10000.0,
            max_risk_per_trade=0.02,  # 2% per trade
            max_portfolio_risk=0.06   # 6% total portfolio risk
        )
        
        # Position sizing parameters
        self.min_position_size = 0.001
        self.max_position_size = 0.05  # Reduced from 0.10 to 0.05 for safety

        # Point value for DEX 900 DOWN Index (critical for risk calculation)
        self.point_value = 1.0  # $1 per point movement per lot

        # Volatility adjustment parameters
        self.base_volatility = 0.15  # 15% base volatility
        self.volatility_lookback = 20  # 20 periods for volatility calculation
        
        logger.info("AdvancedPositionSizer initialized")
    
    def calculate_position_size(self, strategy_type: str, entry_price: float,
                              stop_loss: float, take_profit: float,
                              current_volatility: float,
                              account_info: AccountInfo = None,
                              sizing_method: SizingMethod = SizingMethod.MONTE_CARLO_OPTIMIZED) -> PositionSizeResult:
        """
        Calculate optimal position size using specified method
        
        Args:
            strategy_type: 'CALM' or 'SPIKE'
            entry_price: Entry price for the trade
            stop_loss: Stop loss level
            take_profit: Take profit level
            current_volatility: Current market volatility
            account_info: Account information
            sizing_method: Position sizing method to use
            
        Returns:
            PositionSizeResult with calculated position size
        """
        account = account_info or self.default_account
        
        logger.debug(f"Calculating position size: {sizing_method.value}")
        
        # FORCE FIXED_PERCENT for CALM phase to ensure correct risk calculation
        if strategy_type == 'CALM' or sizing_method == SizingMethod.FIXED_PERCENT:
            return self._fixed_percent_sizing(strategy_type, entry_price, stop_loss, account)

        elif sizing_method == SizingMethod.KELLY_CRITERION:
            result = self._kelly_criterion_sizing(strategy_type, entry_price, stop_loss,
                                              take_profit, current_volatility, account)

        elif sizing_method == SizingMethod.VOLATILITY_ADJUSTED:
            result = self._volatility_adjusted_sizing(strategy_type, entry_price, stop_loss,
                                                   current_volatility, account)

        elif sizing_method == SizingMethod.MONTE_CARLO_OPTIMIZED:
            result = self._monte_carlo_optimized_sizing(strategy_type, entry_price, stop_loss,
                                                    take_profit, current_volatility, account)

        elif sizing_method == SizingMethod.RISK_PARITY:
            result = self._risk_parity_sizing(strategy_type, entry_price, stop_loss,
                                          current_volatility, account)
        
        else:
            # Default to fixed percent
            result = self._fixed_percent_sizing(strategy_type, entry_price, stop_loss, account)

        # Log position sizing decision
        logger.info(f"📊 Position sizing: {strategy_type} phase → {result.position_size:.4f} lots "
                   f"(method: {result.sizing_method.value}, confidence: {result.confidence:.1%})")

        return result
    
    def _fixed_percent_sizing(self, strategy_type: str, entry_price: float,
                            stop_loss: float, account: AccountInfo) -> PositionSizeResult:
        """Fixed percentage position sizing"""
        
        # Use 2% daily risk limit instead of config values that are too high
        risk_percent = account.max_risk_per_trade  # 2% per trade (not 5% from config!)

        # Calculate risk amount in dollars
        risk_amount = account.balance * risk_percent

        # Calculate stop loss distance in points
        stop_loss_distance_points = abs(entry_price - stop_loss)

        # CORRECT FORMULA: Position Size = Risk Amount / (Stop Loss Points × Point Value)
        if stop_loss_distance_points > 0:
            position_size = risk_amount / (stop_loss_distance_points * self.point_value)
        else:
            position_size = self.min_position_size
        
        # Apply limits
        position_size = max(self.min_position_size, min(self.max_position_size, position_size))
        
        return PositionSizeResult(
            position_size=position_size,
            risk_amount=risk_amount,
            sizing_method=SizingMethod.FIXED_PERCENT,
            confidence=0.8,
            reasoning=f"Fixed {risk_percent*100:.1f}% risk per trade (${risk_amount:.2f} / {stop_loss_distance_points:.0f} pts)",
            max_loss_potential=position_size * stop_loss_distance_points * self.point_value
        )
    
    def _kelly_criterion_sizing(self, strategy_type: str, entry_price: float,
                              stop_loss: float, take_profit: float,
                              volatility: float, account: AccountInfo) -> PositionSizeResult:
        """Kelly Criterion position sizing"""
        
        # Estimate win probability and average win/loss based on strategy
        if strategy_type == 'CALM':
            # Conservative estimates for calm strategy
            win_probability = 0.65
            avg_win = abs(take_profit - entry_price)
            avg_loss = abs(entry_price - stop_loss)
        else:  # SPIKE
            # More aggressive estimates for spike strategy
            win_probability = 0.55
            avg_win = abs(entry_price - take_profit)
            avg_loss = abs(stop_loss - entry_price)
        
        # Kelly formula: f = (bp - q) / b
        # where b = avg_win/avg_loss, p = win_probability, q = 1-p
        b = avg_win / avg_loss if avg_loss > 0 else 1
        p = win_probability
        q = 1 - p
        
        kelly_fraction = (b * p - q) / b if b > 0 else 0
        
        # Apply Kelly fraction with safety factor (typically 25-50% of full Kelly)
        safety_factor = 0.25  # Use 25% of Kelly recommendation
        adjusted_kelly = kelly_fraction * safety_factor
        
        # Ensure positive and within limits
        position_size = max(self.min_position_size, 
                          min(self.max_position_size, adjusted_kelly))
        
        risk_amount = account.balance * position_size
        stop_loss_distance = abs(entry_price - stop_loss)
        
        return PositionSizeResult(
            position_size=position_size,
            risk_amount=risk_amount,
            sizing_method=SizingMethod.KELLY_CRITERION,
            confidence=0.7,
            reasoning=f"Kelly criterion: {kelly_fraction:.3f} (25% applied)",
            max_loss_potential=position_size * stop_loss_distance,
            kelly_fraction=kelly_fraction
        )
    
    def _volatility_adjusted_sizing(self, strategy_type: str, entry_price: float,
                                  stop_loss: float, volatility: float,
                                  account: AccountInfo) -> PositionSizeResult:
        """Volatility-adjusted position sizing"""
        
        # Base position size
        base_risk = config.CALM_PHASE_RISK if strategy_type == 'CALM' else config.SPIKE_PHASE_RISK
        
        # Volatility adjustment factor
        volatility_ratio = self.base_volatility / volatility if volatility > 0 else 1.0
        volatility_adjustment = np.sqrt(volatility_ratio)  # Square root for smoother adjustment
        
        # Apply volatility adjustment (higher volatility = smaller position)
        adjusted_risk = base_risk * volatility_adjustment
        
        # Ensure within reasonable bounds
        adjusted_risk = max(0.005, min(0.05, adjusted_risk))  # 0.5% to 5%
        
        # Calculate position size using correct formula
        risk_amount = account.balance * adjusted_risk
        stop_loss_distance_points = abs(entry_price - stop_loss)
        if stop_loss_distance_points > 0:
            position_size = risk_amount / (stop_loss_distance_points * self.point_value)
        else:
            position_size = self.min_position_size
        
        # Apply limits
        position_size = max(self.min_position_size, min(self.max_position_size, position_size))
        
        return PositionSizeResult(
            position_size=position_size,
            risk_amount=risk_amount,
            sizing_method=SizingMethod.VOLATILITY_ADJUSTED,
            confidence=0.85,
            reasoning=f"Volatility adjusted: {volatility:.3f} vs base {self.base_volatility:.3f}",
            max_loss_potential=position_size * stop_loss_distance_points * self.point_value,
            volatility_adjustment=volatility_adjustment
        )
    
    def _monte_carlo_optimized_sizing(self, strategy_type: str, entry_price: float,
                                    stop_loss: float, take_profit: float,
                                    volatility: float, account: AccountInfo) -> PositionSizeResult:
        """Monte Carlo optimized position sizing"""
        
        try:
            # Run Monte Carlo risk assessment
            mc_strategy_type = 'LONG' if strategy_type == 'CALM' else 'SHORT'
            risk_metrics = self.monte_carlo.run_comprehensive_risk_assessment(
                mc_strategy_type, entry_price, volatility, num_simulations=1000  # Faster for real-time
            )
            
            # Base position size on Monte Carlo results
            base_risk = config.CALM_PHASE_RISK if strategy_type == 'CALM' else config.SPIKE_PHASE_RISK
            
            # Adjust based on Monte Carlo metrics
            sharpe_adjustment = min(2.0, max(0.5, 1.0 + risk_metrics.sharpe_ratio))
            win_rate_adjustment = min(1.5, max(0.7, risk_metrics.win_rate * 1.5))
            var_adjustment = min(1.2, max(0.8, 1.0 - (risk_metrics.var_95 / 1000)))  # Normalize VaR
            
            # Combined adjustment
            mc_adjustment = (sharpe_adjustment * win_rate_adjustment * var_adjustment) / 3
            adjusted_risk = base_risk * mc_adjustment
            
            # Ensure within bounds
            adjusted_risk = max(0.005, min(0.08, adjusted_risk))
            
            # Calculate position size using correct formula
            risk_amount = account.balance * adjusted_risk
            stop_loss_distance_points = abs(entry_price - stop_loss)
            if stop_loss_distance_points > 0:
                position_size = risk_amount / (stop_loss_distance_points * self.point_value)
            else:
                position_size = self.min_position_size
            
            # Apply limits
            position_size = max(self.min_position_size, min(self.max_position_size, position_size))
            
            confidence = min(0.95, 0.7 + (risk_metrics.win_rate * 0.3))
            
            return PositionSizeResult(
                position_size=position_size,
                risk_amount=risk_amount,
                sizing_method=SizingMethod.MONTE_CARLO_OPTIMIZED,
                confidence=confidence,
                reasoning=f"MC optimized: Sharpe {risk_metrics.sharpe_ratio:.2f}, WR {risk_metrics.win_rate:.2f}",
                max_loss_potential=position_size * stop_loss_distance_points * self.point_value
            )
            
        except Exception as e:
            logger.warning(f"Monte Carlo sizing failed: {e}, falling back to volatility adjusted")
            return self._volatility_adjusted_sizing(strategy_type, entry_price, stop_loss, 
                                                   volatility, account)
    
    def _risk_parity_sizing(self, strategy_type: str, entry_price: float,
                          stop_loss: float, volatility: float,
                          account: AccountInfo) -> PositionSizeResult:
        """Risk parity position sizing"""
        
        # Target risk contribution per trade
        target_risk_contribution = 0.015  # 1.5% risk contribution
        
        # Calculate position size to achieve target risk
        stop_loss_distance = abs(entry_price - stop_loss)
        
        # Risk parity: Position size inversely proportional to volatility and stop distance
        risk_factor = volatility * (stop_loss_distance / entry_price)
        position_size = target_risk_contribution / risk_factor if risk_factor > 0 else self.min_position_size
        
        # Apply limits
        position_size = max(self.min_position_size, min(self.max_position_size, position_size))
        
        risk_amount = position_size * stop_loss_distance
        
        return PositionSizeResult(
            position_size=position_size,
            risk_amount=risk_amount,
            sizing_method=SizingMethod.RISK_PARITY,
            confidence=0.75,
            reasoning=f"Risk parity: {target_risk_contribution*100}% target contribution",
            max_loss_potential=risk_amount
        )
    
    def get_recommended_sizing_method(self, strategy_type: str, 
                                    market_conditions: Dict[str, Any]) -> SizingMethod:
        """
        Recommend optimal sizing method based on strategy and market conditions
        
        Args:
            strategy_type: 'CALM' or 'SPIKE'
            market_conditions: Dict with volatility, trend, etc.
            
        Returns:
            Recommended SizingMethod
        """
        volatility = market_conditions.get('volatility', 0.15)
        trend_strength = market_conditions.get('trend_strength', 0.5)
        
        # High volatility environments
        if volatility > 0.20:
            return SizingMethod.VOLATILITY_ADJUSTED
        
        # Calm strategy in stable conditions
        elif strategy_type == 'CALM' and volatility < 0.10:
            return SizingMethod.MONTE_CARLO_OPTIMIZED
        
        # Spike strategy in volatile conditions
        elif strategy_type == 'SPIKE' and volatility > 0.15:
            return SizingMethod.KELLY_CRITERION
        
        # Default to Monte Carlo for most conditions
        else:
            return SizingMethod.MONTE_CARLO_OPTIMIZED
    
    def validate_position_size(self, position_size: float, account: AccountInfo,
                             existing_positions: List[Dict] = None) -> Tuple[bool, str]:
        """
        Validate if position size is acceptable given account constraints
        
        Args:
            position_size: Proposed position size
            account: Account information
            existing_positions: List of existing positions
            
        Returns:
            Tuple[bool, str]: (is_valid, reason)
        """
        existing_positions = existing_positions or []
        
        # Check minimum size
        if position_size < self.min_position_size:
            return False, f"Position size {position_size:.4f} below minimum {self.min_position_size}"
        
        # Check maximum size
        if position_size > self.max_position_size:
            return False, f"Position size {position_size:.4f} above maximum {self.max_position_size}"
        
        # Check available margin
        required_margin = position_size * account.balance
        if required_margin > account.margin_available:
            return False, f"Insufficient margin: need {required_margin:.2f}, available {account.margin_available:.2f}"
        
        # Check total portfolio risk
        total_risk = sum([pos.get('risk_amount', 0) for pos in existing_positions])
        new_total_risk = total_risk + (position_size * account.balance)
        max_portfolio_risk = account.balance * account.max_portfolio_risk
        
        if new_total_risk > max_portfolio_risk:
            return False, f"Portfolio risk limit exceeded: {new_total_risk:.2f} > {max_portfolio_risk:.2f}"
        
        return True, "Position size validated"
    
    def get_sizing_summary(self, strategy_type: str, entry_price: float,
                          stop_loss: float, take_profit: float,
                          volatility: float, account: AccountInfo = None) -> Dict[str, Any]:
        """Get summary of all sizing methods for comparison"""
        
        account = account or self.default_account
        methods = [
            SizingMethod.FIXED_PERCENT,
            SizingMethod.KELLY_CRITERION,
            SizingMethod.VOLATILITY_ADJUSTED,
            SizingMethod.MONTE_CARLO_OPTIMIZED,
            SizingMethod.RISK_PARITY
        ]
        
        results = {}
        for method in methods:
            try:
                result = self.calculate_position_size(
                    strategy_type, entry_price, stop_loss, take_profit,
                    volatility, account, method
                )
                results[method.value] = {
                    'position_size': result.position_size,
                    'risk_amount': result.risk_amount,
                    'confidence': result.confidence,
                    'reasoning': result.reasoning
                }
            except Exception as e:
                results[method.value] = {'error': str(e)}
        
        return results
