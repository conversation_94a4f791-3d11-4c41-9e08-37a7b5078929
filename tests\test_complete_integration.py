"""
Complete Integration Tests for DEX900DN Trading System
End-to-end testing of the entire trading workflow
"""

import sys
import os
import unittest
import time
import threading
from datetime import datetime, timedelta
from unittest.mock import Mock, patch, MagicMock

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))

from src.main_controller import DEX900DNController, SystemState
from src.monitoring.system_monitor import SystemHealthMonitor, AlertLevel, HealthStatus
from src.execution.trading_executor import TradeSignal
from config.config import config

class TestCompleteIntegration(unittest.TestCase):
    """Test complete system integration"""
    
    def setUp(self):
        """Set up test environment"""
        self.controller = None
        self.monitor = None
    
    def tearDown(self):
        """Clean up after tests"""
        if self.controller and self.controller.system_state == SystemState.RUNNING:
            self.controller.stop_system()
        
        if self.monitor and self.monitor.monitoring_active:
            self.monitor.stop_monitoring()
    
    def test_system_initialization(self):
        """Test complete system initialization"""
        print("\n🔧 Testing System Initialization...")
        
        # Initialize controller
        self.controller = DEX900DNController()
        
        # Check initial state
        self.assertEqual(self.controller.system_state, SystemState.STOPPED)
        self.assertIsNotNone(self.controller.cycle_tracker)
        self.assertIsNotNone(self.controller.data_handler)
        self.assertIsNotNone(self.controller.phase_classifier)
        self.assertIsNotNone(self.controller.strategy_manager)
        self.assertIsNotNone(self.controller.risk_manager)
        self.assertIsNotNone(self.controller.trading_executor)
        
        print("   ✅ All components initialized successfully")
    
    def test_system_startup_shutdown(self):
        """Test system startup and shutdown sequence"""
        print("\n🚀 Testing System Startup/Shutdown...")
        
        self.controller = DEX900DNController()
        
        # Test startup
        print("   Starting system...")
        success = self.controller.start_system()
        self.assertTrue(success)
        self.assertEqual(self.controller.system_state, SystemState.RUNNING)
        
        # Wait a moment for system to stabilize
        time.sleep(2)
        
        # Check system status
        status = self.controller.get_system_status()
        self.assertEqual(status.state, SystemState.RUNNING)
        self.assertGreater(status.uptime.total_seconds(), 0)
        
        print(f"   ✅ System running - Uptime: {status.uptime.total_seconds():.1f}s")
        
        # Test shutdown
        print("   Stopping system...")
        success = self.controller.stop_system()
        self.assertTrue(success)
        self.assertEqual(self.controller.system_state, SystemState.STOPPED)
        
        print("   ✅ System stopped successfully")
    
    def test_health_monitoring_integration(self):
        """Test health monitoring integration"""
        print("\n🔍 Testing Health Monitoring Integration...")
        
        self.monitor = SystemHealthMonitor(check_interval=1.0)
        
        # Start monitoring
        self.monitor.start_monitoring()
        self.assertTrue(self.monitor.monitoring_active)
        
        # Wait for some monitoring cycles
        time.sleep(3)
        
        # Check health status
        health = self.monitor.get_system_health()
        self.assertIn('overall_status', health)
        self.assertIn('system_resources', health)
        self.assertIn('component_health', health)
        
        print(f"   ✅ Health monitoring active - Status: {health['overall_status']}")
        print(f"   CPU: {health['system_resources'].get('cpu_percent', 0):.1f}%")
        print(f"   Memory: {health['system_resources'].get('memory_percent', 0):.1f}%")
        
        # Test alert creation
        alert = self.monitor._create_alert(
            AlertLevel.INFO, 'test_component', 'Test alert message'
        )
        
        self.assertEqual(alert.level, AlertLevel.INFO)
        self.assertEqual(alert.component, 'test_component')
        self.assertFalse(alert.acknowledged)
        
        # Stop monitoring
        self.monitor.stop_monitoring()
        self.assertFalse(self.monitor.monitoring_active)
        
        print("   ✅ Health monitoring integration successful")
    
    @patch('src.execution.mt5_connection.MT5_AVAILABLE', False)
    def test_complete_trading_workflow(self):
        """Test complete trading workflow in simulation mode"""
        print("\n📊 Testing Complete Trading Workflow...")
        
        self.controller = DEX900DNController()
        
        # Start system
        success = self.controller.start_system()
        self.assertTrue(success)
        
        # Wait for system to process some cycles
        time.sleep(3)
        
        # Check that cycles are being processed
        status = self.controller.get_system_status()
        self.assertGreater(status.cycles_processed, 0)
        
        print(f"   ✅ Processed {status.cycles_processed} trading cycles")
        print(f"   Current phase: {status.current_phase}")
        print(f"   Trades executed: {status.trades_executed}")
        
        # Test signal execution
        signal = TradeSignal(
            strategy='CALM',
            action='BUY',
            symbol=config.SYMBOL,
            volume=0.01,
            confidence=0.8,
            reasoning="Integration test signal"
        )
        
        result = self.controller.trading_executor.execute_signal(signal)
        # In simulation mode, this might fail due to no connection, which is expected
        
        print(f"   Signal execution result: {'Success' if result.success else 'Failed (expected in simulation)'}")
        
        # Stop system
        self.controller.stop_system()
        
        print("   ✅ Complete trading workflow tested")
    
    def test_emergency_stop_functionality(self):
        """Test emergency stop functionality"""
        print("\n🚨 Testing Emergency Stop Functionality...")
        
        self.controller = DEX900DNController()
        
        # Start system
        self.controller.start_system()
        self.assertEqual(self.controller.system_state, SystemState.RUNNING)
        
        # Trigger emergency stop
        self.controller.emergency_stop("Integration test emergency stop")
        
        # Check system state
        self.assertEqual(self.controller.system_state, SystemState.EMERGENCY_STOP)
        
        # Check that errors were recorded
        status = self.controller.get_system_status()
        self.assertGreater(len(status.errors), 0)
        
        print("   ✅ Emergency stop functionality working")
    
    def test_pause_resume_functionality(self):
        """Test pause and resume functionality"""
        print("\n⏸️ Testing Pause/Resume Functionality...")
        
        self.controller = DEX900DNController()
        
        # Start system
        self.controller.start_system()
        self.assertEqual(self.controller.system_state, SystemState.RUNNING)
        
        # Pause system
        success = self.controller.pause_system()
        self.assertTrue(success)
        self.assertEqual(self.controller.system_state, SystemState.PAUSED)
        
        # Resume system
        success = self.controller.resume_system()
        self.assertTrue(success)
        self.assertEqual(self.controller.system_state, SystemState.RUNNING)
        
        # Stop system
        self.controller.stop_system()
        
        print("   ✅ Pause/Resume functionality working")
    
    def test_performance_metrics(self):
        """Test performance metrics collection"""
        print("\n📈 Testing Performance Metrics...")
        
        self.controller = DEX900DNController()
        
        # Start system
        self.controller.start_system()
        
        # Wait for some processing
        time.sleep(2)
        
        # Get performance metrics
        metrics = self.controller.get_performance_metrics()
        
        self.assertIn('system_status', metrics)
        self.assertIn('performance', metrics)
        self.assertIn('component_status', metrics)
        
        performance = metrics['performance']
        self.assertIn('avg_cycle_time_ms', performance)
        self.assertIn('cycles_per_second', performance)
        self.assertIn('uptime_hours', performance)
        
        print(f"   ✅ Performance metrics collected")
        print(f"   Avg cycle time: {performance['avg_cycle_time_ms']:.2f}ms")
        print(f"   Cycles per second: {performance['cycles_per_second']:.1f}")
        print(f"   Uptime: {performance['uptime_hours']:.3f} hours")
        
        # Stop system
        self.controller.stop_system()
    
    def test_health_check_integration(self):
        """Test health check integration"""
        print("\n🏥 Testing Health Check Integration...")
        
        self.controller = DEX900DNController()
        
        # Perform health check
        health = self.controller.health_check()
        
        self.assertIn('overall_status', health)
        self.assertIn('components', health)
        self.assertIn('issues', health)
        
        # Should have health status for all components
        expected_components = [
            'cycle_tracker', 'data_handler', 'phase_classifier',
            'strategy_manager', 'risk_manager', 'trading_executor'
        ]
        
        for component in expected_components:
            self.assertIn(component, health['components'])
        
        print(f"   ✅ Health check completed")
        print(f"   Overall status: {health['overall_status']}")
        print(f"   Components checked: {len(health['components'])}")
        print(f"   Issues found: {len(health['issues'])}")

def run_stress_test():
    """Run stress test for system performance"""
    print("\n" + "="*60)
    print("STRESS TEST - SYSTEM PERFORMANCE")
    print("="*60)
    
    controller = DEX900DNController()
    
    try:
        print("\n🔥 Starting stress test...")
        
        # Start system
        controller.start_system()
        
        # Run for extended period
        test_duration = 30  # 30 seconds
        start_time = time.time()
        
        print(f"   Running for {test_duration} seconds...")
        
        while time.time() - start_time < test_duration:
            # Get status periodically
            status = controller.get_system_status()
            
            if status.cycles_processed % 100 == 0 and status.cycles_processed > 0:
                print(f"   Processed {status.cycles_processed} cycles...")
            
            time.sleep(0.1)
        
        # Final metrics
        final_status = controller.get_system_status()
        metrics = controller.get_performance_metrics()
        
        print(f"\n📊 Stress Test Results:")
        print(f"   Duration: {test_duration} seconds")
        print(f"   Cycles processed: {final_status.cycles_processed}")
        print(f"   Cycles per second: {final_status.cycles_processed / test_duration:.1f}")
        print(f"   Avg cycle time: {metrics['performance']['avg_cycle_time_ms']:.2f}ms")
        print(f"   Max cycle time: {metrics['performance']['max_cycle_time_ms']:.2f}ms")
        print(f"   Error count: {len(final_status.errors)}")
        
        # Stop system
        controller.stop_system()
        
        print("   ✅ Stress test completed successfully")
        
    except Exception as e:
        print(f"   ❌ Stress test failed: {str(e)}")
        if controller.system_state == SystemState.RUNNING:
            controller.stop_system()

def run_integration_scenarios():
    """Run various integration scenarios"""
    print("\n" + "="*60)
    print("INTEGRATION SCENARIOS")
    print("="*60)
    
    scenarios = [
        ("Normal Operation", test_normal_operation),
        ("High Frequency Trading", test_high_frequency),
        ("Error Recovery", test_error_recovery),
        ("Resource Monitoring", test_resource_monitoring)
    ]
    
    for scenario_name, scenario_func in scenarios:
        print(f"\n🎯 Scenario: {scenario_name}")
        try:
            scenario_func()
            print(f"   ✅ {scenario_name} completed successfully")
        except Exception as e:
            print(f"   ❌ {scenario_name} failed: {str(e)}")

def test_normal_operation():
    """Test normal operation scenario"""
    controller = DEX900DNController()
    controller.start_system()
    time.sleep(5)
    status = controller.get_system_status()
    assert status.cycles_processed > 0
    controller.stop_system()

def test_high_frequency():
    """Test high frequency scenario"""
    controller = DEX900DNController()
    controller.start_system()
    
    # Simulate high frequency by checking status rapidly
    for _ in range(100):
        status = controller.get_system_status()
        time.sleep(0.01)
    
    controller.stop_system()

def test_error_recovery():
    """Test error recovery scenario"""
    controller = DEX900DNController()
    controller.start_system()
    
    # Simulate error
    controller.errors.append("Simulated error")
    
    # System should continue running
    time.sleep(2)
    assert controller.system_state == SystemState.RUNNING
    
    controller.stop_system()

def test_resource_monitoring():
    """Test resource monitoring scenario"""
    monitor = SystemHealthMonitor(check_interval=0.5)
    monitor.start_monitoring()
    
    time.sleep(3)
    
    health = monitor.get_system_health()
    assert 'system_resources' in health
    
    monitor.stop_monitoring()

def main():
    """Run all integration tests"""
    print("DEX900DN COMPLETE INTEGRATION TEST SUITE")
    print("="*70)
    
    # Run unit tests
    unittest.main(argv=[''], exit=False, verbosity=2)
    
    # Run stress test
    run_stress_test()
    
    # Run integration scenarios
    run_integration_scenarios()
    
    print("\n" + "="*70)
    print("ALL INTEGRATION TESTS COMPLETED!")
    print("="*70)

if __name__ == "__main__":
    main()
