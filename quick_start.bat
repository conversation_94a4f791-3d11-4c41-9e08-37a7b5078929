@echo off
REM ============================================================================
REM DEX900DN Quick Start - Immediate Trading Launch
REM ============================================================================

title DEX900DN Quick Start

REM Set professional console appearance
color 0A

echo.
echo ============================================================================
echo                    DEX900DN QUICK START LAUNCHER
echo ============================================================================
echo.
echo [INFO] Starting DEX900DN Trading System...
echo [INFO] Symbol: DEX 900 DOWN Index
echo [INFO] Magic Number: 900900
echo.

REM Change to script directory
cd /d "%~dp0"

REM Quick validation
if not exist "src" (
    echo [ERROR] Project files not found. Please run from project directory.
    pause
    exit /b 1
)

REM Create required directories
if not exist "logs" mkdir logs
if not exist "data" mkdir data

echo [INFO] Launching production trading system...
echo [INFO] Press Ctrl+C to stop trading gracefully
echo.

REM Start production system directly
python deploy/production_launcher.py

REM Handle exit
if errorlevel 1 (
    echo.
    echo [ERROR] System failed to start. Check logs for details.
    echo.
    pause
    exit /b 1
)

echo.
echo [INFO] DEX900DN Trading System has stopped safely.
echo [INFO] Check logs directory for session details.
echo.
pause
exit /b 0
