"""
Comprehensive Test Suite for Risk Management System
Tests Monte Carlo simulation, position sizing, risk metrics, and integration
"""

import sys
import os
import unittest
import numpy as np
from datetime import datetime, timedelta

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))

from src.risk.monte_carlo import MonteCarloSimulator, SimulationParameters
from src.risk.position_sizing import AdvancedPositionSizer, SizingMethod, AccountInfo
from src.risk.risk_metrics import RiskMetricsCalculator
from src.risk.risk_manager import IntegratedRiskManager
from config.config import config

class TestMonteCarloSimulator(unittest.TestCase):
    """Test Monte Carlo simulation functionality"""
    
    def setUp(self):
        self.simulator = MonteCarloSimulator()
    
    def test_price_path_generation(self):
        """Test price path generation"""
        params = SimulationParameters(
            num_simulations=100,
            time_horizon_days=30,
            initial_price=25000.0,
            volatility=0.15
        )
        
        price_paths = self.simulator.simulate_price_paths(params)
        
        self.assertEqual(len(price_paths), 100)
        self.assertEqual(len(price_paths[0]), 31)  # 30 days + initial
        self.assertEqual(price_paths[0][0], 25000.0)  # Initial price
    
    def test_trading_strategy_simulation(self):
        """Test trading strategy simulation"""
        # Generate simple price paths
        params = SimulationParameters(num_simulations=10, time_horizon_days=10)
        price_paths = self.simulator.simulate_price_paths(params)
        
        # Test LONG strategy
        results = self.simulator.simulate_trading_strategy(
            price_paths, 'LONG', 25000.0, 24700.0, 25300.0
        )
        
        self.assertEqual(len(results), 10)
        for result in results:
            self.assertIsInstance(result.total_return, float)
            self.assertIsInstance(result.hit_stop_loss, bool)
            self.assertIsInstance(result.hit_take_profit, bool)
    
    def test_stop_loss_optimization(self):
        """Test stop loss optimization"""
        optimization = self.simulator.optimize_stop_loss(
            'LONG', 25000.0, 25300.0, 0.15, num_simulations=100
        )
        
        self.assertIn('optimal_stop_loss', optimization)
        self.assertIn('expected_return', optimization)
        self.assertIn('win_rate', optimization)
        self.assertIsInstance(optimization['optimal_stop_loss'], float)
    
    def test_var_calculation(self):
        """Test Value at Risk calculation"""
        returns = np.random.normal(0, 0.02, 1000)  # 2% daily volatility
        var_results = self.simulator.calculate_var(returns, [0.95, 0.99])
        
        self.assertIn('var_95', var_results)
        self.assertIn('var_99', var_results)
        self.assertGreater(var_results['var_99'], var_results['var_95'])
    
    def test_comprehensive_risk_assessment(self):
        """Test comprehensive risk assessment"""
        risk_metrics = self.simulator.run_comprehensive_risk_assessment(
            'LONG', 25000.0, 0.15, num_simulations=100
        )
        
        self.assertIsInstance(risk_metrics.var_95, float)
        self.assertIsInstance(risk_metrics.expected_return, float)
        self.assertIsInstance(risk_metrics.sharpe_ratio, float)
        self.assertIsInstance(risk_metrics.win_rate, float)

class TestPositionSizing(unittest.TestCase):
    """Test position sizing functionality"""
    
    def setUp(self):
        self.sizer = AdvancedPositionSizer()
        self.account = AccountInfo(
            balance=10000.0,
            equity=10000.0,
            margin_used=0.0,
            margin_available=10000.0,
            max_risk_per_trade=0.02,
            max_portfolio_risk=0.06
        )
    
    def test_fixed_percent_sizing(self):
        """Test fixed percentage position sizing"""
        result = self.sizer.calculate_position_size(
            'CALM', 25000.0, 24700.0, 25300.0, 0.10,
            self.account, SizingMethod.FIXED_PERCENT
        )
        
        self.assertEqual(result.sizing_method, SizingMethod.FIXED_PERCENT)
        self.assertGreater(result.position_size, 0)
        self.assertLess(result.position_size, 0.1)  # Within max limit
        self.assertIsInstance(result.reasoning, str)
    
    def test_kelly_criterion_sizing(self):
        """Test Kelly criterion position sizing"""
        result = self.sizer.calculate_position_size(
            'CALM', 25000.0, 24700.0, 25300.0, 0.10,
            self.account, SizingMethod.KELLY_CRITERION
        )
        
        self.assertEqual(result.sizing_method, SizingMethod.KELLY_CRITERION)
        self.assertIsNotNone(result.kelly_fraction)
        self.assertGreater(result.position_size, 0)
    
    def test_volatility_adjusted_sizing(self):
        """Test volatility-adjusted position sizing"""
        # Test with high volatility
        high_vol_result = self.sizer.calculate_position_size(
            'CALM', 25000.0, 24700.0, 25300.0, 0.30,
            self.account, SizingMethod.VOLATILITY_ADJUSTED
        )
        
        # Test with low volatility
        low_vol_result = self.sizer.calculate_position_size(
            'CALM', 25000.0, 24700.0, 25300.0, 0.05,
            self.account, SizingMethod.VOLATILITY_ADJUSTED
        )
        
        # High volatility should result in smaller position
        self.assertLess(high_vol_result.position_size, low_vol_result.position_size)
    
    def test_position_validation(self):
        """Test position size validation"""
        # Valid position
        is_valid, reason = self.sizer.validate_position_size(0.02, self.account)
        self.assertTrue(is_valid)
        
        # Too large position
        is_valid, reason = self.sizer.validate_position_size(0.15, self.account)
        self.assertFalse(is_valid)
        self.assertIn("maximum", reason.lower())
        
        # Too small position
        is_valid, reason = self.sizer.validate_position_size(0.0005, self.account)
        self.assertFalse(is_valid)
        self.assertIn("minimum", reason.lower())
    
    def test_sizing_method_recommendation(self):
        """Test sizing method recommendation"""
        # High volatility market
        method = self.sizer.get_recommended_sizing_method(
            'SPIKE', {'volatility': 0.25, 'trend_strength': 0.8}
        )
        self.assertIn(method, [SizingMethod.VOLATILITY_ADJUSTED, SizingMethod.KELLY_CRITERION])
        
        # Calm market
        method = self.sizer.get_recommended_sizing_method(
            'CALM', {'volatility': 0.08, 'trend_strength': 0.3}
        )
        self.assertIn(method, [SizingMethod.MONTE_CARLO_OPTIMIZED, SizingMethod.FIXED_PERCENT])

class TestRiskMetrics(unittest.TestCase):
    """Test risk metrics calculation"""
    
    def setUp(self):
        self.calculator = RiskMetricsCalculator()
    
    def test_performance_update(self):
        """Test performance tracking updates"""
        # Add some performance data
        for i in range(10):
            pnl = 100 + (i * 10)  # Increasing P/L
            self.calculator.update_performance(pnl)
        
        self.assertEqual(len(self.calculator.equity_curve), 10)
        self.assertEqual(len(self.calculator.returns_history), 9)  # One less than equity
    
    def test_comprehensive_metrics_calculation(self):
        """Test comprehensive metrics calculation"""
        # Add realistic performance data
        base_pnl = 1000
        for i in range(50):
            # Simulate realistic trading returns
            daily_return = np.random.normal(0.001, 0.02)  # 0.1% mean, 2% std
            pnl = base_pnl * (1 + daily_return)
            self.calculator.update_performance(pnl)
            base_pnl = pnl
        
        metrics = self.calculator.calculate_comprehensive_metrics()
        
        self.assertIsInstance(metrics.total_return, float)
        self.assertIsInstance(metrics.sharpe_ratio, float)
        self.assertIsInstance(metrics.max_drawdown, float)
        self.assertIsInstance(metrics.var_95, float)
        self.assertGreaterEqual(metrics.win_rate, 0.0)
        self.assertLessEqual(metrics.win_rate, 1.0)
    
    def test_risk_alerts(self):
        """Test risk alert generation"""
        # Simulate large drawdown
        self.calculator.update_performance(1000)  # Starting equity
        self.calculator.update_performance(800)   # 20% drawdown
        
        # Check if alert was generated
        self.assertGreater(len(self.calculator.alerts), 0)
        
        # Check alert content
        alert = self.calculator.alerts[-1]
        self.assertEqual(alert.alert_type, "MAX_DRAWDOWN")
        self.assertEqual(alert.severity, "HIGH")
    
    def test_risk_dashboard(self):
        """Test risk dashboard generation"""
        # Add some data
        for i in range(20):
            pnl = 1000 + (i * 5) + np.random.normal(0, 10)
            self.calculator.update_performance(pnl)
        
        dashboard = self.calculator.get_risk_dashboard()
        
        self.assertIn('current_metrics', dashboard)
        self.assertIn('risk_status', dashboard)
        self.assertIn('thresholds', dashboard)
        self.assertIn('recent_alerts', dashboard)

class TestIntegratedRiskManager(unittest.TestCase):
    """Test integrated risk management system"""
    
    def setUp(self):
        self.risk_manager = IntegratedRiskManager()
    
    def test_trade_risk_assessment(self):
        """Test comprehensive trade risk assessment"""
        decision = self.risk_manager.assess_trade_risk(
            'CALM', 25000.0, 24700.0, 25300.0, 0.15
        )
        
        self.assertIsInstance(decision.allow_trade, bool)
        self.assertIsInstance(decision.position_size, float)
        self.assertIsInstance(decision.reasoning, str)
        self.assertIn(decision.risk_level, ['LOW', 'MEDIUM', 'HIGH'])
        self.assertGreaterEqual(decision.confidence, 0.0)
        self.assertLessEqual(decision.confidence, 1.0)
    
    def test_basic_limits_checking(self):
        """Test basic risk limits"""
        # Test daily trade limit
        self.risk_manager.daily_trades = 15  # Exceed limit
        
        decision = self.risk_manager.assess_trade_risk(
            'CALM', 25000.0, 24700.0, 25300.0, 0.15
        )
        
        self.assertFalse(decision.allow_trade)
        self.assertIn("Daily trade limit", decision.reasoning)
    
    def test_position_management(self):
        """Test position tracking and management"""
        # Add a position
        position_data = {
            'id': 'test_pos_1',
            'strategy': 'CALM',
            'entry_price': 25000.0,
            'entry_time': datetime.now()
        }
        self.risk_manager.active_positions.append(position_data)
        
        # Update position
        self.risk_manager.update_position('test_pos_1', 150.0, {'current_price': 25150.0})
        
        # Check position was updated
        updated_pos = next(pos for pos in self.risk_manager.active_positions if pos['id'] == 'test_pos_1')
        self.assertEqual(updated_pos['current_pnl'], 150.0)
        
        # Close position
        self.risk_manager.close_position('test_pos_1', 150.0, 'Take profit')
        
        # Check position was removed and added to history
        self.assertEqual(len(self.risk_manager.active_positions), 0)
        self.assertEqual(len(self.risk_manager.trade_history), 1)
    
    def test_risk_dashboard_integration(self):
        """Test integrated risk dashboard"""
        dashboard = self.risk_manager.get_risk_dashboard()
        
        required_sections = [
            'account_status', 'performance_metrics', 'risk_dashboard',
            'limits_status', 'recent_trades', 'active_positions'
        ]
        
        for section in required_sections:
            self.assertIn(section, dashboard)
    
    def test_emergency_stop(self):
        """Test emergency stop functionality"""
        # Add some positions
        self.risk_manager.active_positions = [
            {'id': 'pos1', 'strategy': 'CALM'},
            {'id': 'pos2', 'strategy': 'SPIKE'}
        ]
        
        # Trigger emergency stop
        self.risk_manager.emergency_stop("Test emergency")
        
        # Check positions were cleared
        self.assertEqual(len(self.risk_manager.active_positions), 0)
        self.assertTrue(hasattr(self.risk_manager, 'emergency_stop_active'))

def run_monte_carlo_performance_test():
    """Run performance test for Monte Carlo simulation"""
    print("\n" + "="*60)
    print("MONTE CARLO PERFORMANCE TEST")
    print("="*60)
    
    simulator = MonteCarloSimulator()
    
    # Test different simulation sizes
    simulation_sizes = [100, 1000, 5000, 10000]
    
    for size in simulation_sizes:
        print(f"\nTesting {size} simulations...")
        
        import time
        start_time = time.time()
        
        risk_metrics = simulator.run_comprehensive_risk_assessment(
            'LONG', 25000.0, 0.15, num_simulations=size
        )
        
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"  Duration: {duration:.2f}s")
        print(f"  Rate: {size/duration:.0f} simulations/second")
        print(f"  Expected Return: {risk_metrics.expected_return:.2f}")
        print(f"  Sharpe Ratio: {risk_metrics.sharpe_ratio:.2f}")
        print(f"  Win Rate: {risk_metrics.win_rate:.2%}")
        print(f"  VaR 95%: {risk_metrics.var_95:.2%}")

def run_integration_test():
    """Run integration test with realistic scenarios"""
    print("\n" + "="*60)
    print("RISK MANAGEMENT INTEGRATION TEST")
    print("="*60)
    
    risk_manager = IntegratedRiskManager()
    
    # Test scenario 1: Normal trading conditions
    print("\n1. Testing normal trading conditions...")
    decision = risk_manager.assess_trade_risk(
        'CALM', 25000.0, 24700.0, 25300.0, 0.12
    )
    print(f"   Decision: {'APPROVED' if decision.allow_trade else 'REJECTED'}")
    print(f"   Position Size: {decision.position_size:.4f}")
    print(f"   Risk Level: {decision.risk_level}")
    print(f"   Reasoning: {decision.reasoning}")
    
    # Test scenario 2: High volatility conditions
    print("\n2. Testing high volatility conditions...")
    decision = risk_manager.assess_trade_risk(
        'SPIKE', 25000.0, 25500.0, 24500.0, 0.35
    )
    print(f"   Decision: {'APPROVED' if decision.allow_trade else 'REJECTED'}")
    print(f"   Position Size: {decision.position_size:.4f}")
    print(f"   Risk Level: {decision.risk_level}")
    
    # Test scenario 3: After reaching daily limits
    print("\n3. Testing daily limit enforcement...")
    risk_manager.daily_trades = 12  # Exceed limit
    decision = risk_manager.assess_trade_risk(
        'CALM', 25000.0, 24700.0, 25300.0, 0.10
    )
    print(f"   Decision: {'APPROVED' if decision.allow_trade else 'REJECTED'}")
    print(f"   Reason: {decision.reasoning}")
    
    # Test scenario 4: Risk dashboard
    print("\n4. Testing risk dashboard...")
    dashboard = risk_manager.get_risk_dashboard()
    print(f"   Account Balance: ${dashboard['account_status']['balance']:,.2f}")
    print(f"   Daily P/L: {dashboard['account_status']['daily_pnl_percent']:.2%}")
    print(f"   Active Positions: {dashboard['account_status']['active_positions']}")
    print(f"   Daily Trades: {dashboard['limits_status']['daily_trade_limit']}")

def main():
    """Run all risk management tests"""
    print("DEX900DN RISK MANAGEMENT SYSTEM - COMPREHENSIVE TEST SUITE")
    print("="*70)
    
    # Run unit tests
    unittest.main(argv=[''], exit=False, verbosity=2)
    
    # Run performance tests
    run_monte_carlo_performance_test()
    
    # Run integration tests
    run_integration_test()
    
    print("\n" + "="*70)
    print("ALL RISK MANAGEMENT TESTS COMPLETED!")
    print("="*70)

if __name__ == "__main__":
    main()
