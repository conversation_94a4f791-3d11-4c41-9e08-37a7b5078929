"""
Comprehensive Test Suite for Order Execution System
Tests MT5 integration, order execution, position management, and trading integration
"""

import sys
import os
import unittest
import time
from datetime import datetime, timedelta
from unittest.mock import Mock, patch, MagicMock

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))

from src.execution.mt5_connection import MT5ConnectionManager, ConnectionStatus
from src.execution.order_executor import OrderExecutor, OrderType, OrderResult
from src.execution.position_manager import PositionManager, ManagedPosition, PositionStatus, ExitReason
from src.execution.trading_executor import TradingExecutor, TradeSignal, ExecutionResult
from config.config import config

class TestMT5Connection(unittest.TestCase):
    """Test MT5 connection management"""
    
    def setUp(self):
        self.connection = MT5ConnectionManager(auto_connect=False)
    
    def test_connection_initialization(self):
        """Test connection manager initialization"""
        self.assertEqual(self.connection.symbol, config.SYMBOL)
        self.assertEqual(self.connection.status, ConnectionStatus.DISCONNECTED)
        self.assertFalse(self.connection.is_connected())
    
    def test_connection_status(self):
        """Test connection status reporting"""
        status = self.connection.get_connection_status()
        
        required_fields = [
            'status', 'connected', 'symbol', 'last_error',
            'connection_attempts', 'last_heartbeat', 'monitoring_active'
        ]
        
        for field in required_fields:
            self.assertIn(field, status)
        
        self.assertEqual(status['status'], ConnectionStatus.DISCONNECTED.value)
        self.assertFalse(status['connected'])
    
    @patch('src.execution.mt5_connection.MT5_AVAILABLE', False)
    def test_connection_without_mt5(self):
        """Test connection behavior when MT5 is not available"""
        connection = MT5ConnectionManager(auto_connect=False)
        
        result = connection.connect()
        self.assertFalse(result)
        self.assertEqual(connection.status, ConnectionStatus.ERROR)
    
    def test_monitoring_lifecycle(self):
        """Test connection monitoring start/stop"""
        self.connection._start_monitoring()
        self.assertTrue(self.connection.monitoring_active)
        
        self.connection._stop_monitoring()
        self.assertFalse(self.connection.monitoring_active)

class TestOrderExecutor(unittest.TestCase):
    """Test order execution functionality"""
    
    def setUp(self):
        self.executor = OrderExecutor()
    
    def test_executor_initialization(self):
        """Test order executor initialization"""
        self.assertEqual(self.executor.symbol, config.SYMBOL)
        self.assertEqual(self.executor.magic_number, config.MAGIC_NUMBER)
        self.assertEqual(self.executor.max_slippage, config.MAX_SLIPPAGE)
    
    @patch('src.execution.order_executor.mt5_connection')
    def test_market_order_not_connected(self, mock_connection):
        """Test market order when not connected"""
        mock_connection.is_connected.return_value = False
        
        result = self.executor.place_market_order(OrderType.BUY, 0.01)
        
        self.assertFalse(result.success)
        self.assertIn("Not connected", result.error_description)
    
    @patch('src.execution.order_executor.mt5_connection')
    @patch('src.execution.order_executor.MT5_AVAILABLE', False)
    def test_market_order_simulation(self, mock_connection):
        """Test market order in simulation mode"""
        mock_connection.is_connected.return_value = True
        mock_symbol_info = Mock()
        mock_symbol_info.ask = 25000.0
        mock_symbol_info.bid = 24999.0
        mock_connection.get_symbol_info.return_value = mock_symbol_info
        
        result = self.executor.place_market_order(OrderType.BUY, 0.01)
        
        self.assertTrue(result.success)
        self.assertIsNotNone(result.order_id)
        self.assertEqual(result.volume, 0.01)
        self.assertIn("SIMULATION", result.comment)
    
    def test_invalid_order_type(self):
        """Test invalid order type handling"""
        with patch('src.execution.order_executor.mt5_connection') as mock_connection:
            mock_connection.is_connected.return_value = True
            mock_symbol_info = Mock()
            mock_symbol_info.ask = 25000.0
            mock_connection.get_symbol_info.return_value = mock_symbol_info
            
            # Test invalid market order type
            result = self.executor.place_market_order(OrderType.BUY_LIMIT, 0.01)
            self.assertFalse(result.success)
            self.assertIn("Invalid market order type", result.error_description)
    
    def test_order_tracking(self):
        """Test order execution tracking"""
        initial_count = len(self.executor.order_history)
        
        # Simulate order execution
        request = {'symbol': 'DEX900DN', 'volume': 0.01}
        result = OrderResult(success=True, order_id=123456, volume=0.01)
        
        self.executor._track_order(request, result)
        
        self.assertEqual(len(self.executor.order_history), initial_count + 1)
        self.assertEqual(self.executor.order_history[-1]['result'], result)

class TestPositionManager(unittest.TestCase):
    """Test position management functionality"""
    
    def setUp(self):
        self.manager = PositionManager()
    
    def test_manager_initialization(self):
        """Test position manager initialization"""
        self.assertEqual(self.manager.symbol, config.SYMBOL)
        self.assertEqual(self.manager.magic_number, config.MAGIC_NUMBER)
        self.assertFalse(self.manager.monitoring_active)
        self.assertEqual(len(self.manager.managed_positions), 0)
    
    @patch('src.execution.position_manager.OrderExecutor')
    def test_open_position_success(self, mock_executor_class):
        """Test successful position opening"""
        # Mock order executor
        mock_executor = Mock()
        mock_executor_class.return_value = mock_executor
        
        # Mock successful order result
        mock_result = OrderResult(
            success=True,
            position_id=123456,
            deal_id=789012,
            price=25000.0,
            volume=0.01
        )
        mock_executor.place_market_order.return_value = mock_result
        
        # Create new manager with mocked executor
        manager = PositionManager()
        manager.order_executor = mock_executor
        
        # Open position
        success, position = manager.open_position(
            strategy='CALM',
            order_type=OrderType.BUY,
            volume=0.01,
            stop_loss=24700.0,
            take_profit=25300.0
        )
        
        self.assertTrue(success)
        self.assertIsNotNone(position)
        self.assertEqual(position.strategy, 'CALM')
        self.assertEqual(position.type, 'BUY')
        self.assertEqual(position.volume, 0.01)
        self.assertEqual(position.status, PositionStatus.OPEN)
    
    @patch('src.execution.position_manager.OrderExecutor')
    def test_open_position_failure(self, mock_executor_class):
        """Test failed position opening"""
        # Mock order executor
        mock_executor = Mock()
        mock_executor_class.return_value = mock_executor
        
        # Mock failed order result
        mock_result = OrderResult(
            success=False,
            error_description="Insufficient margin"
        )
        mock_executor.place_market_order.return_value = mock_result
        
        # Create new manager with mocked executor
        manager = PositionManager()
        manager.order_executor = mock_executor
        
        # Attempt to open position
        success, position = manager.open_position(
            strategy='CALM',
            order_type=OrderType.BUY,
            volume=0.01
        )
        
        self.assertFalse(success)
        self.assertIsNone(position)
    
    def test_position_tracking(self):
        """Test position tracking functionality"""
        # Create a mock position
        position = ManagedPosition(
            ticket=123456,
            symbol='DEX900DN',
            type='BUY',
            volume=0.01,
            entry_price=25000.0,
            entry_time=datetime.now(),
            strategy='CALM',
            magic_number=config.MAGIC_NUMBER
        )
        
        # Add to manager
        self.manager.managed_positions[position.ticket] = position
        
        # Test retrieval
        retrieved = self.manager.get_position(position.ticket)
        self.assertEqual(retrieved, position)
        
        # Test list retrieval
        positions = self.manager.get_positions()
        self.assertEqual(len(positions), 1)
        self.assertEqual(positions[0], position)
    
    def test_performance_summary(self):
        """Test performance summary calculation"""
        # Initial summary
        summary = self.manager.get_performance_summary()
        self.assertEqual(summary['total_trades'], 0)
        self.assertEqual(summary['win_rate'], 0)
        
        # Simulate some trades
        self.manager.total_trades = 10
        self.manager.winning_trades = 6
        self.manager.losing_trades = 4
        self.manager.total_realized_pnl = 150.0
        
        summary = self.manager.get_performance_summary()
        self.assertEqual(summary['total_trades'], 10)
        self.assertEqual(summary['win_rate'], 0.6)
        self.assertEqual(summary['total_realized_pnl'], 150.0)
    
    def test_monitoring_lifecycle(self):
        """Test position monitoring lifecycle"""
        self.assertFalse(self.manager.monitoring_active)
        
        self.manager.start_monitoring()
        self.assertTrue(self.manager.monitoring_active)
        
        self.manager.stop_monitoring()
        self.assertFalse(self.manager.monitoring_active)

class TestTradingExecutor(unittest.TestCase):
    """Test trading executor integration"""
    
    def setUp(self):
        self.executor = TradingExecutor()
    
    def test_executor_initialization(self):
        """Test trading executor initialization"""
        self.assertEqual(self.executor.symbol, config.SYMBOL)
        self.assertEqual(self.executor.magic_number, config.MAGIC_NUMBER)
        self.assertTrue(self.executor.auto_execute)
        self.assertEqual(len(self.executor.execution_history), 0)
    
    def test_signal_creation(self):
        """Test trade signal creation"""
        signal = TradeSignal(
            strategy='CALM',
            action='BUY',
            symbol='DEX900DN',
            volume=0.01,
            confidence=0.8,
            reasoning="Bollinger Band + RSI signal"
        )
        
        self.assertEqual(signal.strategy, 'CALM')
        self.assertEqual(signal.action, 'BUY')
        self.assertIsNotNone(signal.timestamp)
    
    @patch('src.execution.trading_executor.mt5_connection')
    def test_execute_entry_signal_risk_rejection(self, mock_connection):
        """Test entry signal execution with risk rejection"""
        # Mock connection
        mock_connection.is_connected.return_value = True
        mock_symbol_info = Mock()
        mock_symbol_info.ask = 25000.0
        mock_symbol_info.bid = 24999.0
        mock_connection.get_symbol_info.return_value = mock_symbol_info
        
        # Mock risk manager to reject trade
        with patch.object(self.executor.risk_manager, 'assess_trade_risk') as mock_risk:
            mock_risk_decision = Mock()
            mock_risk_decision.allow_trade = False
            mock_risk_decision.reasoning = "Daily limit exceeded"
            mock_risk.return_value = mock_risk_decision
            
            signal = TradeSignal(
                strategy='CALM',
                action='BUY',
                symbol='DEX900DN',
                volume=0.01
            )
            
            result = self.executor.execute_signal(signal)
            
            self.assertFalse(result.success)
            self.assertIn("Risk management rejected", result.error_message)
            self.assertEqual(self.executor.rejected_signals, 1)
    
    def test_invalid_signal_action(self):
        """Test handling of invalid signal actions"""
        signal = TradeSignal(
            strategy='CALM',
            action='INVALID_ACTION',
            symbol='DEX900DN'
        )
        
        result = self.executor.execute_signal(signal)
        
        self.assertFalse(result.success)
        self.assertIn("Unknown signal action", result.error_message)
    
    def test_default_calculations(self):
        """Test default stop loss and take profit calculations"""
        signal = TradeSignal(
            strategy='CALM',
            action='BUY',
            symbol='DEX900DN',
            entry_price=25000.0
        )
        
        # Test default stop loss
        default_sl = self.executor._calculate_default_stop_loss(signal)
        expected_sl = 25000.0 - config.CALM_STOP_LOSS
        self.assertEqual(default_sl, expected_sl)
        
        # Test default take profit
        default_tp = self.executor._calculate_default_take_profit(signal)
        expected_tp = 25000.0 + config.CALM_TAKE_PROFIT_MAX
        self.assertEqual(default_tp, expected_tp)
    
    def test_execution_summary(self):
        """Test execution summary generation"""
        # Initial summary
        summary = self.executor.get_execution_summary()
        self.assertEqual(summary['total_signals'], 0)
        self.assertEqual(summary['execution_rate'], 0)
        
        # Simulate some executions
        self.executor.total_signals = 10
        self.executor.executed_signals = 7
        self.executor.rejected_signals = 3
        
        summary = self.executor.get_execution_summary()
        self.assertEqual(summary['total_signals'], 10)
        self.assertEqual(summary['executed_signals'], 7)
        self.assertEqual(summary['execution_rate'], 0.7)

def run_integration_test():
    """Run integration test with realistic scenarios"""
    print("\n" + "="*60)
    print("ORDER EXECUTION SYSTEM INTEGRATION TEST")
    print("="*60)
    
    # Test MT5 connection
    print("\n1. Testing MT5 Connection...")
    connection = MT5ConnectionManager(auto_connect=False)
    status = connection.get_connection_status()
    print(f"   Connection Status: {status['status']}")
    print(f"   Symbol: {status['symbol']}")
    print(f"   Monitoring: {status['monitoring_active']}")
    
    # Test order executor
    print("\n2. Testing Order Executor...")
    executor = OrderExecutor()
    print(f"   Symbol: {executor.symbol}")
    print(f"   Magic Number: {executor.magic_number}")
    print(f"   Max Slippage: {executor.max_slippage}")
    
    # Test position manager
    print("\n3. Testing Position Manager...")
    manager = PositionManager()
    summary = manager.get_performance_summary()
    print(f"   Active Positions: {summary['active_positions']}")
    print(f"   Total Trades: {summary['total_trades']}")
    print(f"   Win Rate: {summary['win_rate']:.1%}")
    
    # Test trading executor
    print("\n4. Testing Trading Executor...")
    trading_exec = TradingExecutor()
    exec_summary = trading_exec.get_execution_summary()
    print(f"   Total Signals: {exec_summary['total_signals']}")
    print(f"   Execution Rate: {exec_summary['execution_rate']:.1%}")
    print(f"   Active Positions: {exec_summary['active_positions']}")
    
    # Test signal execution (simulation)
    print("\n5. Testing Signal Execution...")
    signal = TradeSignal(
        strategy='CALM',
        action='BUY',
        symbol='DEX900DN',
        volume=0.01,
        entry_price=25000.0,
        stop_loss=24700.0,
        take_profit=25300.0,
        confidence=0.85,
        reasoning="Test signal for integration"
    )
    
    print(f"   Signal: {signal.strategy} {signal.action}")
    print(f"   Entry: {signal.entry_price}")
    print(f"   SL: {signal.stop_loss} | TP: {signal.take_profit}")
    print(f"   Confidence: {signal.confidence:.1%}")
    
    # Test connection status
    print("\n6. Testing System Status...")
    system_status = trading_exec.get_connection_status()
    print(f"   MT5 Connected: {system_status['mt5_connection']['connected']}")
    print(f"   Position Manager Active: {system_status['position_manager_active']}")
    print(f"   Active Positions: {system_status['active_positions']}")

def run_performance_test():
    """Run performance test for order execution system"""
    print("\n" + "="*60)
    print("ORDER EXECUTION PERFORMANCE TEST")
    print("="*60)
    
    # Test signal processing speed
    print("\n1. Testing Signal Processing Speed...")
    executor = TradingExecutor()
    
    signals = []
    for i in range(100):
        signal = TradeSignal(
            strategy='CALM',
            action='BUY',
            symbol='DEX900DN',
            volume=0.01,
            entry_price=25000.0 + i,
            confidence=0.8
        )
        signals.append(signal)
    
    start_time = time.time()
    for signal in signals:
        # Just validate signal, don't execute
        result = ExecutionResult(success=True, signal=signal)
    end_time = time.time()
    
    duration = end_time - start_time
    rate = len(signals) / duration
    
    print(f"   Processed {len(signals)} signals in {duration:.3f}s")
    print(f"   Rate: {rate:.0f} signals/second")
    
    # Test position tracking performance
    print("\n2. Testing Position Tracking Performance...")
    manager = PositionManager()
    
    # Create mock positions
    positions = []
    for i in range(50):
        position = ManagedPosition(
            ticket=100000 + i,
            symbol='DEX900DN',
            type='BUY',
            volume=0.01,
            entry_price=25000.0,
            entry_time=datetime.now(),
            strategy='CALM',
            magic_number=config.MAGIC_NUMBER
        )
        positions.append(position)
        manager.managed_positions[position.ticket] = position
    
    start_time = time.time()
    for _ in range(1000):
        summary = manager.get_position_summary()
    end_time = time.time()
    
    duration = end_time - start_time
    rate = 1000 / duration
    
    print(f"   Generated 1,000 position summaries in {duration:.3f}s")
    print(f"   Rate: {rate:.0f} summaries/second")
    print(f"   Tracking {len(positions)} positions")

def main():
    """Run all order execution tests"""
    print("DEX900DN ORDER EXECUTION SYSTEM - COMPREHENSIVE TEST SUITE")
    print("="*70)
    
    # Run unit tests
    unittest.main(argv=[''], exit=False, verbosity=2)
    
    # Run integration tests
    run_integration_test()
    
    # Run performance tests
    run_performance_test()
    
    print("\n" + "="*70)
    print("ALL ORDER EXECUTION TESTS COMPLETED!")
    print("="*70)

if __name__ == "__main__":
    main()
