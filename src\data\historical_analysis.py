"""
Historical Data Analysis for DEX900DN Trading System
Analyzes spike intervals and validates system parameters
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime, timedelta
import logging
from typing import List, Dict, Tuple

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))

from config.config import config

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class HistoricalAnalyzer:
    """Analyzes historical DEX900DN data to validate trading parameters"""
    
    def __init__(self, data_file: str = None):
        self.data_file = data_file or config.HISTORICAL_DATA_FILE
        self.data = None
        self.spike_events = []
        
    def load_data(self, data_file: str = None) -> bool:
        """Load historical data from CSV file"""
        file_path = data_file or self.data_file
        
        try:
            self.data = pd.read_csv(file_path)
            self.data['timestamp'] = pd.to_datetime(self.data['timestamp'])
            self.data = self.data.sort_values('timestamp')
            
            logger.info(f"Loaded {len(self.data)} data points from {file_path}")
            return True
            
        except FileNotFoundError:
            logger.warning(f"Historical data file not found: {file_path}")
            logger.info("Generating sample data for analysis...")
            self._generate_sample_data()
            return True
            
        except Exception as e:
            logger.error(f"Error loading data: {e}")
            return False
    
    def _generate_sample_data(self, days: int = 30) -> None:
        """Generate sample DEX900DN data for testing"""
        logger.info(f"Generating {days} days of sample data...")
        
        # Generate timestamps (1-second intervals)
        start_time = datetime.now() - timedelta(days=days)
        timestamps = pd.date_range(start_time, periods=days*24*3600, freq='1s')
        
        # Generate realistic price data with spikes
        np.random.seed(42)  # For reproducible results
        
        base_price = 25000.0
        prices = []
        volumes = []
        
        for i, ts in enumerate(timestamps):
            # Normal price movement
            price_change = np.random.normal(0, 5)  # Small random changes
            
            # Add periodic spikes (roughly every 15 minutes with some randomness)
            if i % (15 * 60) < 10 and np.random.random() < 0.3:  # 30% chance of spike
                # Create a significant drop
                spike_magnitude = np.random.uniform(300, 800)  # 300-800 point drops
                price_change = -spike_magnitude
            
            base_price += price_change
            prices.append(base_price)
            
            # Generate volume (higher during spikes)
            base_volume = 100
            if abs(price_change) > 100:  # During spikes
                volume = base_volume * np.random.uniform(2, 5)  # 2-5x normal volume
            else:
                volume = base_volume * np.random.uniform(0.5, 1.5)
            
            volumes.append(volume)
        
        # Create DataFrame
        self.data = pd.DataFrame({
            'timestamp': timestamps,
            'mid_price': prices,
            'volume': volumes,
            'bid': [p - 2.5 for p in prices],
            'ask': [p + 2.5 for p in prices],
            'spread': [5.0] * len(prices)
        })
        
        logger.info(f"Generated {len(self.data)} sample data points")
    
    def detect_historical_spikes(self, drop_threshold: int = None, 
                                time_window: int = None) -> List[Dict]:
        """Detect spike events in historical data"""
        if self.data is None:
            logger.error("No data loaded")
            return []
        
        drop_threshold = drop_threshold or config.MIN_SPIKE_DROP
        time_window = time_window or config.SPIKE_TIME_WINDOW
        
        spikes = []
        prices = self.data['mid_price'].values
        timestamps = self.data['timestamp'].values
        
        # Rolling window analysis
        for i in range(time_window, len(prices)):
            window_prices = prices[i-time_window:i+1]
            max_price = np.max(window_prices)
            min_price = np.min(window_prices)
            drop = max_price - min_price
            
            if drop >= drop_threshold:
                spike_event = {
                    'timestamp': timestamps[i],
                    'drop_amount': drop,
                    'max_price': max_price,
                    'min_price': min_price,
                    'duration': time_window
                }
                spikes.append(spike_event)
        
        # Remove duplicate spikes (within 60 seconds of each other)
        filtered_spikes = []
        for spike in spikes:
            if not filtered_spikes:
                filtered_spikes.append(spike)
            else:
                last_spike_time = filtered_spikes[-1]['timestamp']
                time_diff = pd.Timedelta(spike['timestamp'] - last_spike_time).total_seconds()
                if time_diff > 60:  # More than 60 seconds apart
                    filtered_spikes.append(spike)
        
        self.spike_events = filtered_spikes
        logger.info(f"Detected {len(filtered_spikes)} spike events")
        
        return filtered_spikes
    
    def analyze_spike_intervals(self) -> Dict:
        """Analyze intervals between spike events"""
        if len(self.spike_events) < 2:
            logger.warning("Need at least 2 spike events for interval analysis")
            return {}
        
        intervals = []
        for i in range(1, len(self.spike_events)):
            prev_spike = self.spike_events[i-1]['timestamp']
            curr_spike = self.spike_events[i]['timestamp']
            interval = pd.Timedelta(curr_spike - prev_spike).total_seconds()
            intervals.append(interval)
        
        intervals = np.array(intervals)
        
        analysis = {
            'mean_interval': np.mean(intervals),
            'median_interval': np.median(intervals),
            'std_interval': np.std(intervals),
            'min_interval': np.min(intervals),
            'max_interval': np.max(intervals),
            'intervals_near_900s': np.sum((intervals >= 840) & (intervals <= 960)),
            'total_intervals': len(intervals),
            'percentage_near_900s': (np.sum((intervals >= 840) & (intervals <= 960)) / len(intervals)) * 100
        }
        
        logger.info(f"Spike interval analysis:")
        logger.info(f"  Mean interval: {analysis['mean_interval']:.1f} seconds")
        logger.info(f"  Intervals near 900s (14-16min): {analysis['intervals_near_900s']}/{analysis['total_intervals']} ({analysis['percentage_near_900s']:.1f}%)")
        
        return analysis
    
    def validate_system_parameters(self) -> Dict:
        """Validate system parameters against historical data"""
        if self.data is None:
            return {}
        
        # Calculate volatility statistics
        self.data['returns'] = self.data['mid_price'].pct_change()
        volatility_5min = self.data['returns'].rolling(window=300).std()  # 5-minute rolling
        
        # Volume analysis
        volume_baseline = self.data['volume'].rolling(window=1800).mean()  # 30-minute baseline
        volume_spikes = self.data['volume'] > (volume_baseline * config.VOLUME_SPIKE_MULTIPLIER)
        
        validation = {
            'volatility_stats': {
                'mean': volatility_5min.mean(),
                'std': volatility_5min.std(),
                'percentile_95': volatility_5min.quantile(0.95),
                'above_spike_threshold': (volatility_5min > config.SPIKE_VOLATILITY_THRESHOLD).sum(),
                'below_calm_threshold': (volatility_5min < config.CALM_VOLATILITY_THRESHOLD).sum()
            },
            'volume_stats': {
                'mean_volume': self.data['volume'].mean(),
                'volume_spikes_detected': volume_spikes.sum(),
                'spike_percentage': (volume_spikes.sum() / len(self.data)) * 100
            },
            'spike_analysis': self.analyze_spike_intervals()
        }
        
        return validation
    
    def generate_report(self) -> str:
        """Generate comprehensive analysis report"""
        if not self.load_data():
            return "Failed to load data"
        
        # Detect spikes
        spikes = self.detect_historical_spikes()
        
        # Validate parameters
        validation = self.validate_system_parameters()
        
        report = f"""
DEX900DN HISTORICAL DATA ANALYSIS REPORT
========================================

Data Summary:
- Total data points: {len(self.data)}
- Time range: {self.data['timestamp'].min()} to {self.data['timestamp'].max()}
- Price range: {self.data['mid_price'].min():.2f} - {self.data['mid_price'].max():.2f}

Spike Events Detected: {len(spikes)}
- Average drop: {np.mean([s['drop_amount'] for s in spikes]):.1f} points
- Largest drop: {max([s['drop_amount'] for s in spikes]):.1f} points

Spike Interval Analysis:
- Mean interval: {validation['spike_analysis'].get('mean_interval', 0):.1f} seconds
- Intervals near 900s: {validation['spike_analysis'].get('percentage_near_900s', 0):.1f}%

Volatility Analysis:
- Mean 5-min volatility: {validation['volatility_stats']['mean']:.6f}
- 95th percentile: {validation['volatility_stats']['percentile_95']:.6f}
- Above spike threshold ({config.SPIKE_VOLATILITY_THRESHOLD}): {validation['volatility_stats']['above_spike_threshold']} periods

Volume Analysis:
- Mean volume: {validation['volume_stats']['mean_volume']:.2f}
- Volume spikes detected: {validation['volume_stats']['volume_spikes_detected']} ({validation['volume_stats']['spike_percentage']:.2f}%)

Parameter Validation:
- Spike threshold ({config.MIN_SPIKE_DROP} points): {'VALID' if spikes else 'NEEDS ADJUSTMENT'}
- Cycle duration (900s): {'VALID' if validation['spike_analysis'].get('percentage_near_900s', 0) > 20 else 'NEEDS ADJUSTMENT'}
- Volatility thresholds: {'VALID' if validation['volatility_stats']['percentile_95'] > config.SPIKE_VOLATILITY_THRESHOLD else 'NEEDS ADJUSTMENT'}

Recommendations:
- {'System parameters appear well-calibrated' if len(spikes) > 10 else 'Consider adjusting spike detection thresholds'}
- {'Cycle timing is appropriate' if validation['spike_analysis'].get('percentage_near_900s', 0) > 20 else 'Review cycle duration settings'}
"""
        
        return report

def main():
    """Run historical analysis"""
    analyzer = HistoricalAnalyzer()
    report = analyzer.generate_report()
    print(report)
    
    # Save report
    with open('docs/historical_analysis_report.txt', 'w', encoding='utf-8') as f:
        f.write(report)
    
    print("\nReport saved to docs/historical_analysis_report.txt")

if __name__ == "__main__":
    main()
