"""
Spike Phase Trading Strategy for DEX900DN
Short-only momentum fade strategy
Entry: 500pt drop in <10 sec + volume spike + retracement
Exit: 50% TP at 500pt, trailing 200pt stop
"""

import logging
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, Tuple, List
from dataclasses import dataclass
from enum import Enum

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))
from config.config import config
from src.modules.data_feed import DataHandler, TickData

# Setup logging
logging.basicConfig(level=getattr(logging, config.LOG_LEVEL))
logger = logging.getLogger(__name__)

class SpikeSignalType(Enum):
    """Spike trading signal types"""
    NO_SIGNAL = "NO_SIGNAL"
    ENTRY_SHORT = "ENTRY_SHORT"
    PARTIAL_EXIT = "PARTIAL_EXIT"
    FULL_EXIT = "FULL_EXIT"
    STOP_LOSS = "STOP_LOSS"

@dataclass
class SpikeSignal:
    """Spike trading signal data structure"""
    signal_type: SpikeSignalType
    price: float
    timestamp: datetime
    confidence: float
    reasoning: str
    stop_loss: Optional[float] = None
    take_profit: Optional[float] = None
    position_size: Optional[float] = None
    partial_close_percent: Optional[float] = None

@dataclass
class SpikePosition:
    """Spike position tracking with partial closes"""
    entry_price: float
    entry_time: datetime
    initial_size: float
    current_size: float
    stop_loss: float
    take_profit: float
    trailing_stop: float
    current_pnl: float = 0.0
    max_favorable: float = 0.0
    max_adverse: float = 0.0
    partial_closes: List[Tuple[float, float, datetime]] = None  # (price, size, time)
    
    def __post_init__(self):
        if self.partial_closes is None:
            self.partial_closes = []

class SpikePhaseStrategy:
    """
    Spike Phase Engine for DEX900DN Trading System
    
    Strategy Specifications:
    - Entry Trigger: 500pt drop in <10 sec + volume spike + retracement
    - Position: Short-only
    - Exit: 50% TP at 500pt, trailing 200pt stop
    - Risk: 1% per trade (from config.SPIKE_PHASE_RISK)
    """
    
    def __init__(self, data_handler: DataHandler = None):
        self.data_handler = data_handler
        
        # Strategy parameters from config
        self.min_spike_drop = config.MIN_SPIKE_DROP  # 500 points
        self.spike_time_window = config.SPIKE_TIME_WINDOW  # 10 seconds
        self.take_profit_target = config.SPIKE_TAKE_PROFIT  # 500 points
        self.partial_close_percent = config.SPIKE_PARTIAL_CLOSE  # 0.5 (50%)
        self.trailing_stop_distance = config.SPIKE_TRAILING_STOP  # 200 points
        self.stop_loss_min = config.SPIKE_STOP_LOSS_MIN  # 1500 points
        self.stop_loss_max = config.SPIKE_STOP_LOSS_MAX  # 3000 points
        
        # Position management
        self.current_position: Optional[SpikePosition] = None
        self.position_history: List[SpikePosition] = []
        self.risk_per_trade = config.SPIKE_PHASE_RISK  # 0.01 (1%)
        
        # Spike detection state
        self.last_spike_detected = None
        self.spike_high_price = None
        self.spike_detection_time = None
        self.retracement_threshold = 0.3  # 30% retracement for entry
        
        logger.info("SpikePhaseStrategy initialized")
        logger.info(f"Spike trigger: {self.min_spike_drop}pt drop in {self.spike_time_window}s")
        logger.info(f"Exit: {self.partial_close_percent*100}% at {self.take_profit_target}pt, trailing {self.trailing_stop_distance}pt")
        logger.info(f"Stop loss range: {self.stop_loss_min}-{self.stop_loss_max}pt")
    
    def detect_spike_entry_conditions(self) -> Tuple[bool, Dict[str, Any]]:
        """
        Detect if spike entry conditions are met
        
        Returns:
            Tuple[bool, dict]: (conditions_met, analysis_data)
        """
        if not self.data_handler:
            return False, {"error": "No data handler"}
        
        # Check for price drop
        price_drop_detected, drop_amount = self.data_handler.detect_price_drop(
            self.min_spike_drop, self.spike_time_window
        )
        
        # Check for volume spike
        volume_spike = self.data_handler.detect_volume_spike()
        
        # Get current price data
        if len(self.data_handler.price_buffer) < self.spike_time_window:
            return False, {"error": "Insufficient price data"}
        
        recent_prices = list(self.data_handler.price_buffer)[-self.spike_time_window:]
        current_price = recent_prices[-1]
        spike_high = max(recent_prices)
        spike_low = min(recent_prices)
        
        analysis = {
            "price_drop_detected": price_drop_detected,
            "drop_amount": drop_amount,
            "volume_spike": volume_spike,
            "current_price": current_price,
            "spike_high": spike_high,
            "spike_low": spike_low,
            "spike_range": spike_high - spike_low
        }
        
        # Primary spike detection
        if price_drop_detected and drop_amount >= self.min_spike_drop:
            self.last_spike_detected = datetime.now()
            self.spike_high_price = spike_high
            self.spike_detection_time = self.last_spike_detected
            
            logger.warning(f"SPIKE DETECTED: {drop_amount:.1f}pt drop, high: {spike_high:.1f}")
            analysis["spike_detected"] = True
            return True, analysis
        
        analysis["spike_detected"] = False
        return False, analysis
    
    def momentum_fade_entry(self) -> SpikeSignal:
        """
        Execute momentum fade entry on retracement
        
        Returns:
            SpikeSignal: Entry signal or NO_SIGNAL
        """
        if not self.data_handler or len(self.data_handler.price_buffer) == 0:
            return SpikeSignal(
                signal_type=SpikeSignalType.NO_SIGNAL,
                price=0.0,
                timestamp=datetime.now(),
                confidence=0.0,
                reasoning="No price data available"
            )
        
        current_price = list(self.data_handler.price_buffer)[-1]
        current_time = datetime.now()
        
        # Check if we have a recent spike detection
        if (not self.last_spike_detected or 
            not self.spike_high_price or
            (current_time - self.last_spike_detected).total_seconds() > 60):  # 1 minute timeout
            
            return SpikeSignal(
                signal_type=SpikeSignalType.NO_SIGNAL,
                price=current_price,
                timestamp=current_time,
                confidence=0.0,
                reasoning="No recent spike detected or spike timeout"
            )
        
        # Check for retracement from spike low
        recent_prices = list(self.data_handler.price_buffer)[-self.spike_time_window:]
        spike_low = min(recent_prices)
        spike_range = self.spike_high_price - spike_low
        
        # Calculate retracement percentage
        retracement_amount = current_price - spike_low
        retracement_percent = retracement_amount / spike_range if spike_range > 0 else 0
        
        # Entry condition: 30% retracement from spike low
        if retracement_percent >= self.retracement_threshold:
            # Calculate position sizing
            position_size = self._calculate_position_size(current_price)
            
            # Calculate stop loss (adaptive based on volatility)
            volatility = self.data_handler.calculate_volatility(5) if self.data_handler else 0.05
            adaptive_sl = self._calculate_adaptive_stop_loss(current_price, volatility, spike_range)
            stop_loss_price = current_price + adaptive_sl
            
            # Calculate take profit
            take_profit_price = current_price - self.take_profit_target
            
            # Calculate trailing stop
            trailing_stop_price = current_price + self.trailing_stop_distance
            
            confidence = self._calculate_entry_confidence(retracement_percent, volatility, spike_range)
            
            reasoning = (f"Momentum fade entry: {retracement_percent:.1%} retracement "
                        f"from spike low {spike_low:.1f}, range {spike_range:.1f}pts")
            
            return SpikeSignal(
                signal_type=SpikeSignalType.ENTRY_SHORT,
                price=current_price,
                timestamp=current_time,
                confidence=confidence,
                reasoning=reasoning,
                stop_loss=stop_loss_price,
                take_profit=take_profit_price,
                position_size=position_size
            )
        
        return SpikeSignal(
            signal_type=SpikeSignalType.NO_SIGNAL,
            price=current_price,
            timestamp=current_time,
            confidence=0.0,
            reasoning=f"Insufficient retracement: {retracement_percent:.1%} < {self.retracement_threshold:.1%}"
        )
    
    def spike_exit_strategy(self) -> SpikeSignal:
        """
        Generate exit signal for current spike position
        
        Returns:
            SpikeSignal: Exit signal or NO_SIGNAL
        """
        if not self.current_position:
            return SpikeSignal(
                signal_type=SpikeSignalType.NO_SIGNAL,
                price=0.0,
                timestamp=datetime.now(),
                confidence=0.0,
                reasoning="No active position"
            )
        
        if not self.data_handler or len(self.data_handler.price_buffer) == 0:
            return SpikeSignal(
                signal_type=SpikeSignalType.NO_SIGNAL,
                price=0.0,
                timestamp=datetime.now(),
                confidence=0.0,
                reasoning="No price data available"
            )
        
        current_price = list(self.data_handler.price_buffer)[-1]
        current_time = datetime.now()
        
        # Update position P/L
        self._update_position_pnl(current_price)
        
        # Check stop loss
        if current_price >= self.current_position.stop_loss:
            return SpikeSignal(
                signal_type=SpikeSignalType.STOP_LOSS,
                price=current_price,
                timestamp=current_time,
                confidence=1.0,
                reasoning=f"Stop loss hit: {current_price:.1f} >= {self.current_position.stop_loss:.1f}"
            )
        
        # Check trailing stop
        if current_price >= self.current_position.trailing_stop:
            return SpikeSignal(
                signal_type=SpikeSignalType.STOP_LOSS,
                price=current_price,
                timestamp=current_time,
                confidence=1.0,
                reasoning=f"Trailing stop hit: {current_price:.1f} >= {self.current_position.trailing_stop:.1f}"
            )
        
        # Check partial take profit (50% at target)
        profit_points = self.current_position.entry_price - current_price
        if (profit_points >= self.take_profit_target and 
            self.current_position.current_size == self.current_position.initial_size):
            
            return SpikeSignal(
                signal_type=SpikeSignalType.PARTIAL_EXIT,
                price=current_price,
                timestamp=current_time,
                confidence=1.0,
                reasoning=f"Partial TP hit: {profit_points:.1f}pts >= {self.take_profit_target}pts",
                partial_close_percent=self.partial_close_percent
            )
        
        # Update trailing stop if in profit
        if profit_points > 0:
            new_trailing_stop = current_price + self.trailing_stop_distance
            if new_trailing_stop < self.current_position.trailing_stop:
                self.current_position.trailing_stop = new_trailing_stop
                logger.debug(f"Trailing stop updated to {new_trailing_stop:.1f}")
        
        return SpikeSignal(
            signal_type=SpikeSignalType.NO_SIGNAL,
            price=current_price,
            timestamp=current_time,
            confidence=0.0,
            reasoning=f"Position held: P/L {profit_points:.1f}pts"
        )
    
    def _calculate_position_size(self, entry_price: float) -> float:
        """Calculate position size based on risk management"""
        return self.risk_per_trade
    
    def _calculate_adaptive_stop_loss(self, entry_price: float, volatility: float, spike_range: float) -> float:
        """Calculate adaptive stop loss based on market conditions"""
        # Base stop loss
        base_sl = self.stop_loss_min
        
        # Adjust for volatility (higher volatility = wider stop)
        volatility_adjustment = min(500, volatility * 10000)  # Scale volatility
        
        # Adjust for spike magnitude (larger spike = wider stop)
        spike_adjustment = min(500, spike_range * 0.3)
        
        # Calculate final stop loss
        adaptive_sl = base_sl + volatility_adjustment + spike_adjustment
        
        # Cap at maximum
        return min(adaptive_sl, self.stop_loss_max)
    
    def _calculate_entry_confidence(self, retracement_percent: float, volatility: float, spike_range: float) -> float:
        """Calculate confidence score for entry signal"""
        # Retracement confidence (optimal around 30-50%)
        retracement_confidence = 1.0 - abs(retracement_percent - 0.4) * 2
        retracement_confidence = max(0.0, min(1.0, retracement_confidence))
        
        # Volatility confidence (higher volatility = higher confidence for spike trades)
        volatility_confidence = min(1.0, volatility * 5)  # Scale volatility
        
        # Spike magnitude confidence (larger spikes = higher confidence)
        spike_confidence = min(1.0, spike_range / 1000)  # Normalize to 1000pt range
        
        # Combined confidence
        confidence = (retracement_confidence * 0.4 + 
                     volatility_confidence * 0.3 + 
                     spike_confidence * 0.3)
        
        return max(0.1, min(0.95, confidence))
    
    def _update_position_pnl(self, current_price: float) -> None:
        """Update current position P/L and tracking metrics"""
        if not self.current_position:
            return
        
        # Calculate current P/L in points (short position)
        pnl_points = self.current_position.entry_price - current_price
        self.current_position.current_pnl = pnl_points
        
        # Track maximum favorable and adverse excursions
        if pnl_points > self.current_position.max_favorable:
            self.current_position.max_favorable = pnl_points
        
        if pnl_points < self.current_position.max_adverse:
            self.current_position.max_adverse = pnl_points
    
    def open_position(self, signal: SpikeSignal) -> bool:
        """
        Open a new spike position based on entry signal
        
        Returns:
            bool: True if position opened successfully
        """
        if self.current_position:
            logger.warning("Cannot open position: existing position active")
            return False
        
        if signal.signal_type != SpikeSignalType.ENTRY_SHORT:
            logger.warning(f"Cannot open position: invalid signal type {signal.signal_type}")
            return False
        
        self.current_position = SpikePosition(
            entry_price=signal.price,
            entry_time=signal.timestamp,
            initial_size=signal.position_size or self.risk_per_trade,
            current_size=signal.position_size or self.risk_per_trade,
            stop_loss=signal.stop_loss,
            take_profit=signal.take_profit,
            trailing_stop=signal.price + self.trailing_stop_distance
        )
        
        logger.info(f"SPIKE SHORT OPENED: {signal.price:.1f} | SL: {signal.stop_loss:.1f} | TP: {signal.take_profit:.1f}")
        return True
    
    def close_position(self, signal: SpikeSignal, partial: bool = False) -> bool:
        """
        Close current position (full or partial) based on exit signal
        
        Returns:
            bool: True if position closed successfully
        """
        if not self.current_position:
            logger.warning("Cannot close position: no active position")
            return False
        
        # Update final P/L
        self._update_position_pnl(signal.price)
        
        if partial and signal.partial_close_percent:
            # Partial close
            close_size = self.current_position.current_size * signal.partial_close_percent
            self.current_position.current_size -= close_size
            
            # Record partial close
            self.current_position.partial_closes.append(
                (signal.price, close_size, signal.timestamp)
            )
            
            logger.info(f"SPIKE SHORT PARTIAL CLOSE: {signal.price:.1f} | Size: {close_size:.3f} | P/L: {self.current_position.current_pnl:.1f}pts")
            return True
        else:
            # Full close
            exit_type = {
                SpikeSignalType.PARTIAL_EXIT: "PARTIAL_TP",
                SpikeSignalType.FULL_EXIT: "FULL_TP", 
                SpikeSignalType.STOP_LOSS: "STOP_LOSS"
            }.get(signal.signal_type, "MANUAL")
            
            logger.info(f"SPIKE SHORT CLOSED ({exit_type}): {signal.price:.1f} | P/L: {self.current_position.current_pnl:.1f}pts")
            
            # Archive position
            self.position_history.append(self.current_position)
            self.current_position = None
            
            return True
    
    def get_strategy_summary(self) -> Dict[str, Any]:
        """Get comprehensive strategy status summary"""
        current_price = 0.0
        if self.data_handler and len(self.data_handler.price_buffer) > 0:
            current_price = list(self.data_handler.price_buffer)[-1]
        
        # Calculate performance metrics
        total_trades = len(self.position_history)
        winning_trades = sum(1 for pos in self.position_history if pos.current_pnl > 0)
        total_pnl = sum(pos.current_pnl for pos in self.position_history)
        
        return {
            "strategy_name": "Spike Phase Short Momentum Fade",
            "current_price": current_price,
            "active_position": self.current_position is not None,
            "position_pnl": self.current_position.current_pnl if self.current_position else 0.0,
            "position_size": self.current_position.current_size if self.current_position else 0.0,
            "total_trades": total_trades,
            "winning_trades": winning_trades,
            "win_rate": (winning_trades / total_trades * 100) if total_trades > 0 else 0.0,
            "total_pnl": total_pnl,
            "last_spike_detected": self.last_spike_detected,
            "spike_high_price": self.spike_high_price
        }
