"""
Position Manager for DEX900DN Trading System
Advanced position tracking, management, and lifecycle control
"""

import logging
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List, Tuple
from dataclasses import dataclass, field
from enum import Enum
import threading
import time

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))
from config.config import config
from src.execution.mt5_connection import mt5_connection
from src.execution.order_executor import OrderExecutor, OrderResult, Position, OrderType

# Setup logging
logging.basicConfig(level=getattr(logging, config.LOG_LEVEL))
logger = logging.getLogger(__name__)

class PositionStatus(Enum):
    """Position status"""
    OPENING = "OPENING"
    OPEN = "OPEN"
    CLOSING = "CLOSING"
    CLOSED = "CLOSED"
    ERROR = "ERROR"

class ExitReason(Enum):
    """Position exit reasons"""
    TAKE_PROFIT = "TAKE_PROFIT"
    STOP_LOSS = "STOP_LOSS"
    MANUAL_CLOSE = "MANUAL_CLOSE"
    STRATEGY_EXIT = "STRATEGY_EXIT"
    RISK_MANAGEMENT = "RISK_MANAGEMENT"
    TIME_EXIT = "TIME_EXIT"
    EMERGENCY_CLOSE = "EMERGENCY_CLOSE"

@dataclass
class ManagedPosition:
    """Enhanced position with management features"""
    # Basic position info
    ticket: int
    symbol: str
    type: str  # 'BUY' or 'SELL'
    volume: float
    entry_price: float
    entry_time: datetime
    
    # Management parameters
    strategy: str
    magic_number: int
    stop_loss: Optional[float] = None
    take_profit: Optional[float] = None
    trailing_stop: Optional[float] = None
    partial_close_levels: List[Tuple[float, float]] = field(default_factory=list)  # (price, volume_pct)
    
    # Status tracking
    status: PositionStatus = PositionStatus.OPENING
    current_price: float = 0.0
    unrealized_pnl: float = 0.0
    realized_pnl: float = 0.0
    max_profit: float = 0.0
    max_loss: float = 0.0
    
    # Exit management
    exit_time: Optional[datetime] = None
    exit_price: Optional[float] = None
    exit_reason: Optional[ExitReason] = None
    
    # Tracking
    last_update: datetime = field(default_factory=datetime.now)
    updates_count: int = 0
    
    # Risk management
    max_hold_time: Optional[timedelta] = None
    risk_amount: float = 0.0

class PositionManager:
    """
    Advanced Position Manager for DEX900DN Trading System
    
    Features:
    - Real-time position tracking and updates
    - Trailing stop management
    - Partial position closing
    - Risk-based position management
    - Performance analytics
    - Integration with trading strategies
    """
    
    def __init__(self, symbol: str = None, magic_number: int = None):
        self.symbol = symbol or config.SYMBOL
        self.magic_number = magic_number or config.MAGIC_NUMBER
        
        # Order executor for position operations
        self.order_executor = OrderExecutor(self.symbol, self.magic_number)
        
        # Position tracking
        self.managed_positions: Dict[int, ManagedPosition] = {}
        self.position_history: List[ManagedPosition] = []
        
        # Monitoring
        self.monitoring_active = False
        self.monitor_thread = None
        self.update_interval = 1.0  # seconds
        
        # Performance tracking
        self.total_realized_pnl = 0.0
        self.total_trades = 0
        self.winning_trades = 0
        self.losing_trades = 0
        
        # Thread safety
        self.position_lock = threading.Lock()
        
        logger.info(f"PositionManager initialized for {self.symbol} (Magic: {self.magic_number})")
    
    def open_position(self, strategy: str, order_type: OrderType, volume: float,
                     stop_loss: Optional[float] = None,
                     take_profit: Optional[float] = None,
                     trailing_stop: Optional[float] = None,
                     partial_close_levels: List[Tuple[float, float]] = None,
                     max_hold_time: Optional[timedelta] = None,
                     risk_amount: float = 0.0,
                     comment: str = None) -> Tuple[bool, Optional[ManagedPosition]]:
        """
        Open a new managed position
        
        Args:
            strategy: Strategy name
            order_type: BUY or SELL
            volume: Position volume
            stop_loss: Stop loss price
            take_profit: Take profit price
            trailing_stop: Trailing stop distance
            partial_close_levels: List of (price, volume_pct) for partial closes
            max_hold_time: Maximum hold time
            risk_amount: Risk amount for this position
            comment: Order comment
            
        Returns:
            Tuple[bool, Optional[ManagedPosition]]: Success flag and position object
        """
        with self.position_lock:
            logger.info(f"Opening {order_type.value} position: {volume} {self.symbol}")
            
            # Place market order
            comment = comment or f"DEX900DN {strategy} {order_type.value}"
            result = self.order_executor.place_market_order(
                order_type, volume, stop_loss, take_profit, comment
            )
            
            if not result.success:
                logger.error(f"Failed to open position: {result.error_description}")
                return False, None
            
            # Create managed position
            managed_pos = ManagedPosition(
                ticket=result.position_id or result.deal_id,
                symbol=self.symbol,
                type=order_type.value,
                volume=volume,
                entry_price=result.price,
                entry_time=datetime.now(),
                strategy=strategy,
                magic_number=self.magic_number,
                stop_loss=stop_loss,
                take_profit=take_profit,
                trailing_stop=trailing_stop,
                partial_close_levels=partial_close_levels or [],
                status=PositionStatus.OPEN,
                max_hold_time=max_hold_time,
                risk_amount=risk_amount
            )
            
            # Add to managed positions
            self.managed_positions[managed_pos.ticket] = managed_pos
            
            logger.info(f"Position opened successfully: Ticket {managed_pos.ticket}")
            
            # Start monitoring if not already active
            if not self.monitoring_active:
                self.start_monitoring()
            
            return True, managed_pos
    
    def close_position(self, ticket: int, volume: Optional[float] = None,
                      reason: ExitReason = ExitReason.MANUAL_CLOSE) -> bool:
        """
        Close a managed position
        
        Args:
            ticket: Position ticket
            volume: Volume to close (None for full close)
            reason: Exit reason
            
        Returns:
            bool: Success flag
        """
        with self.position_lock:
            if ticket not in self.managed_positions:
                logger.warning(f"Position {ticket} not found in managed positions")
                return False
            
            managed_pos = self.managed_positions[ticket]
            
            logger.info(f"Closing position {ticket} (Reason: {reason.value})")
            
            # Close position
            result = self.order_executor.close_position(
                ticket, volume, f"DEX900DN Close - {reason.value}"
            )
            
            if not result.success:
                logger.error(f"Failed to close position {ticket}: {result.error_description}")
                return False
            
            # Update managed position
            managed_pos.status = PositionStatus.CLOSED
            managed_pos.exit_time = datetime.now()
            managed_pos.exit_price = result.price
            managed_pos.exit_reason = reason
            managed_pos.realized_pnl = self._calculate_realized_pnl(managed_pos, result.price)
            
            # Update performance tracking
            self._update_performance_stats(managed_pos)
            
            # Move to history
            self.position_history.append(managed_pos)
            del self.managed_positions[ticket]
            
            logger.info(f"Position {ticket} closed successfully. P/L: {managed_pos.realized_pnl:.2f}")
            
            return True
    
    def modify_position(self, ticket: int, stop_loss: Optional[float] = None,
                       take_profit: Optional[float] = None,
                       trailing_stop: Optional[float] = None) -> bool:
        """
        Modify position parameters
        
        Args:
            ticket: Position ticket
            stop_loss: New stop loss price
            take_profit: New take profit price
            trailing_stop: New trailing stop distance
            
        Returns:
            bool: Success flag
        """
        with self.position_lock:
            if ticket not in self.managed_positions:
                logger.warning(f"Position {ticket} not found in managed positions")
                return False
            
            managed_pos = self.managed_positions[ticket]
            
            # Modify position in MT5
            if stop_loss is not None or take_profit is not None:
                result = self.order_executor.modify_position(ticket, stop_loss, take_profit)
                if not result.success:
                    logger.error(f"Failed to modify position {ticket}: {result.error_description}")
                    return False
            
            # Update managed position
            if stop_loss is not None:
                managed_pos.stop_loss = stop_loss
            if take_profit is not None:
                managed_pos.take_profit = take_profit
            if trailing_stop is not None:
                managed_pos.trailing_stop = trailing_stop
            
            managed_pos.last_update = datetime.now()
            
            logger.info(f"Position {ticket} modified: SL={stop_loss}, TP={take_profit}")
            
            return True
    
    def start_monitoring(self) -> None:
        """Start position monitoring thread"""
        if self.monitoring_active:
            return
        
        self.monitoring_active = True
        self.monitor_thread = threading.Thread(target=self._monitor_positions, daemon=True)
        self.monitor_thread.start()
        
        logger.info("Position monitoring started")
    
    def stop_monitoring(self) -> None:
        """Stop position monitoring thread"""
        self.monitoring_active = False
        if self.monitor_thread and self.monitor_thread.is_alive():
            self.monitor_thread.join(timeout=5)
        
        logger.info("Position monitoring stopped")
    
    def _monitor_positions(self) -> None:
        """Monitor all managed positions"""
        while self.monitoring_active:
            try:
                with self.position_lock:
                    if not self.managed_positions:
                        time.sleep(self.update_interval)
                        continue
                    
                    # Get current positions from MT5
                    mt5_positions = self.order_executor.get_positions()
                    mt5_positions_dict = {pos.ticket: pos for pos in mt5_positions}
                    
                    # Update managed positions
                    positions_to_close = []
                    
                    for ticket, managed_pos in self.managed_positions.items():
                        if ticket in mt5_positions_dict:
                            mt5_pos = mt5_positions_dict[ticket]
                            self._update_position_data(managed_pos, mt5_pos)
                            
                            # Check for exit conditions
                            exit_reason = self._check_exit_conditions(managed_pos, mt5_pos)
                            if exit_reason:
                                positions_to_close.append((ticket, exit_reason))
                        else:
                            # Position not found in MT5 - might be closed externally
                            logger.warning(f"Position {ticket} not found in MT5")
                            managed_pos.status = PositionStatus.CLOSED
                            managed_pos.exit_time = datetime.now()
                            managed_pos.exit_reason = ExitReason.MANUAL_CLOSE
                            positions_to_close.append((ticket, ExitReason.MANUAL_CLOSE))
                    
                    # Close positions that meet exit conditions
                    for ticket, reason in positions_to_close:
                        if reason != ExitReason.MANUAL_CLOSE:  # Don't re-close manually closed positions
                            self.close_position(ticket, reason=reason)
                        else:
                            # Just move to history
                            managed_pos = self.managed_positions[ticket]
                            self.position_history.append(managed_pos)
                            del self.managed_positions[ticket]
                
                time.sleep(self.update_interval)
                
            except Exception as e:
                logger.error(f"Position monitoring error: {str(e)}")
                time.sleep(self.update_interval)
    
    def _update_position_data(self, managed_pos: ManagedPosition, mt5_pos: Position) -> None:
        """Update managed position with current MT5 data"""
        managed_pos.current_price = mt5_pos.price_current
        managed_pos.unrealized_pnl = mt5_pos.profit
        managed_pos.last_update = datetime.now()
        managed_pos.updates_count += 1
        
        # Update max profit/loss
        if mt5_pos.profit > managed_pos.max_profit:
            managed_pos.max_profit = mt5_pos.profit
        if mt5_pos.profit < managed_pos.max_loss:
            managed_pos.max_loss = mt5_pos.profit
        
        # Update trailing stop
        if managed_pos.trailing_stop:
            self._update_trailing_stop(managed_pos, mt5_pos)
    
    def _update_trailing_stop(self, managed_pos: ManagedPosition, mt5_pos: Position) -> None:
        """Update trailing stop loss"""
        try:
            current_price = mt5_pos.price_current
            trailing_distance = managed_pos.trailing_stop
            
            if managed_pos.type == 'BUY':
                # For long positions, trail stop loss up
                new_stop = current_price - trailing_distance
                if managed_pos.stop_loss is None or new_stop > managed_pos.stop_loss:
                    self.modify_position(managed_pos.ticket, stop_loss=new_stop)
                    logger.debug(f"Trailing stop updated for {managed_pos.ticket}: {new_stop}")
            
            else:  # SELL
                # For short positions, trail stop loss down
                new_stop = current_price + trailing_distance
                if managed_pos.stop_loss is None or new_stop < managed_pos.stop_loss:
                    self.modify_position(managed_pos.ticket, stop_loss=new_stop)
                    logger.debug(f"Trailing stop updated for {managed_pos.ticket}: {new_stop}")
        
        except Exception as e:
            logger.error(f"Trailing stop update error for {managed_pos.ticket}: {str(e)}")
    
    def _check_exit_conditions(self, managed_pos: ManagedPosition, mt5_pos: Position) -> Optional[ExitReason]:
        """Check if position should be closed based on exit conditions"""
        
        # Check time-based exit
        if managed_pos.max_hold_time:
            hold_time = datetime.now() - managed_pos.entry_time
            if hold_time >= managed_pos.max_hold_time:
                return ExitReason.TIME_EXIT
        
        # Check partial close levels
        if managed_pos.partial_close_levels:
            current_price = mt5_pos.price_current
            
            for target_price, volume_pct in managed_pos.partial_close_levels:
                if managed_pos.type == 'BUY' and current_price >= target_price:
                    # Partial close for long position
                    close_volume = managed_pos.volume * volume_pct
                    self.close_position(managed_pos.ticket, close_volume, ExitReason.TAKE_PROFIT)
                    # Remove this level
                    managed_pos.partial_close_levels.remove((target_price, volume_pct))
                    break
                
                elif managed_pos.type == 'SELL' and current_price <= target_price:
                    # Partial close for short position
                    close_volume = managed_pos.volume * volume_pct
                    self.close_position(managed_pos.ticket, close_volume, ExitReason.TAKE_PROFIT)
                    # Remove this level
                    managed_pos.partial_close_levels.remove((target_price, volume_pct))
                    break
        
        return None
    
    def _calculate_realized_pnl(self, managed_pos: ManagedPosition, exit_price: float) -> float:
        """Calculate realized P/L for closed position"""
        if managed_pos.type == 'BUY':
            return (exit_price - managed_pos.entry_price) * managed_pos.volume
        else:  # SELL
            return (managed_pos.entry_price - exit_price) * managed_pos.volume
    
    def _update_performance_stats(self, managed_pos: ManagedPosition) -> None:
        """Update performance statistics"""
        self.total_realized_pnl += managed_pos.realized_pnl
        self.total_trades += 1
        
        if managed_pos.realized_pnl > 0:
            self.winning_trades += 1
        else:
            self.losing_trades += 1
    
    def get_positions(self) -> List[ManagedPosition]:
        """Get all managed positions"""
        with self.position_lock:
            return list(self.managed_positions.values())
    
    def get_position(self, ticket: int) -> Optional[ManagedPosition]:
        """Get specific managed position"""
        with self.position_lock:
            return self.managed_positions.get(ticket)
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """Get performance summary"""
        with self.position_lock:
            win_rate = self.winning_trades / self.total_trades if self.total_trades > 0 else 0
            
            return {
                'total_trades': self.total_trades,
                'winning_trades': self.winning_trades,
                'losing_trades': self.losing_trades,
                'win_rate': win_rate,
                'total_realized_pnl': self.total_realized_pnl,
                'active_positions': len(self.managed_positions),
                'total_unrealized_pnl': sum(pos.unrealized_pnl for pos in self.managed_positions.values()),
                'average_pnl_per_trade': self.total_realized_pnl / self.total_trades if self.total_trades > 0 else 0
            }
    
    def close_all_positions(self, reason: ExitReason = ExitReason.EMERGENCY_CLOSE) -> int:
        """Close all managed positions"""
        with self.position_lock:
            tickets = list(self.managed_positions.keys())
            closed_count = 0
            
            for ticket in tickets:
                if self.close_position(ticket, reason=reason):
                    closed_count += 1
            
            logger.info(f"Closed {closed_count} positions (Reason: {reason.value})")
            return closed_count
    
    def get_position_summary(self) -> Dict[str, Any]:
        """Get comprehensive position summary"""
        with self.position_lock:
            positions = list(self.managed_positions.values())
            
            if not positions:
                return {
                    'active_positions': 0,
                    'total_volume': 0.0,
                    'total_unrealized_pnl': 0.0,
                    'positions_by_strategy': {},
                    'positions_by_type': {'BUY': 0, 'SELL': 0}
                }
            
            # Aggregate data
            total_volume = sum(pos.volume for pos in positions)
            total_unrealized_pnl = sum(pos.unrealized_pnl for pos in positions)
            
            # Group by strategy
            by_strategy = {}
            for pos in positions:
                if pos.strategy not in by_strategy:
                    by_strategy[pos.strategy] = {'count': 0, 'volume': 0.0, 'pnl': 0.0}
                by_strategy[pos.strategy]['count'] += 1
                by_strategy[pos.strategy]['volume'] += pos.volume
                by_strategy[pos.strategy]['pnl'] += pos.unrealized_pnl
            
            # Group by type
            by_type = {'BUY': 0, 'SELL': 0}
            for pos in positions:
                by_type[pos.type] += 1
            
            return {
                'active_positions': len(positions),
                'total_volume': total_volume,
                'total_unrealized_pnl': total_unrealized_pnl,
                'positions_by_strategy': by_strategy,
                'positions_by_type': by_type,
                'oldest_position': min(pos.entry_time for pos in positions),
                'newest_position': max(pos.entry_time for pos in positions)
            }
