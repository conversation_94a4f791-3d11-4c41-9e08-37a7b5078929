#!/usr/bin/env python3
"""
Synthetic Index Fixes Verification Test
Tests the critical fixes for position vanishing on DEX900DN synthetic index
"""

import sys
import os
import logging
from datetime import datetime

# Add project root to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.strategies.calm_strategy import CalmStrategy
from src.execution.order_executor import OrderExecutor, OrderType
from src.execution.position_manager import PositionManager
from src.data.data_handler import DataHandler
from config.config import config

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class SyntheticIndexFixesTest:
    """Test suite for synthetic index specific fixes"""
    
    def __init__(self):
        self.data_handler = DataHandler()
        self.calm_strategy = CalmStrategy(self.data_handler)
        self.order_executor = OrderExecutor()
        self.position_manager = PositionManager()
        self.test_results = {}
        
    def test_enhanced_tp_calculation(self):
        """Test 1: Enhanced TP calculation with minimum buffer"""
        logger.info("🔧 TEST 1: Enhanced TP Calculation")
        
        try:
            # Mock some market data for testing
            test_data = {
                'close': [66700, 66720, 66740, 66760, 66780],
                'bb_upper': [66800],
                'bb_lower': [66600],
                'rsi': [35]  # Oversold condition
            }
            
            # Test TP calculation
            current_price = 66760.0
            
            # Simulate the enhanced TP calculation logic
            MIN_TP_DISTANCE = 100  # From the fix
            spread_buffer = 20  # Simulated spread buffer
            
            # Calculate base TP (from BB width)
            bb_width = test_data['bb_upper'][0] - test_data['bb_lower'][0]  # 200pt
            base_tp = min(350, max(50, bb_width * 0.5))  # 100pt
            
            # Apply enhancement
            enhanced_tp = max(base_tp, MIN_TP_DISTANCE + spread_buffer)  # 120pt
            take_profit_price = current_price + enhanced_tp
            
            # Verify the enhancement
            tp_distance = take_profit_price - current_price
            
            if tp_distance >= 100:  # Minimum buffer enforced
                logger.info(f"✅ Enhanced TP calculation: {tp_distance:.1f}pt buffer (≥100pt required)")
                self.test_results['enhanced_tp'] = True
            else:
                logger.error(f"❌ TP buffer too small: {tp_distance:.1f}pt < 100pt")
                self.test_results['enhanced_tp'] = False
                
        except Exception as e:
            logger.error(f"❌ Enhanced TP calculation test failed: {e}")
            self.test_results['enhanced_tp'] = False
    
    def test_position_validation(self):
        """Test 2: Position viability validation"""
        logger.info("🔧 TEST 2: Position Validation Layer")
        
        try:
            # Test dangerous TP/SL levels (too close to market)
            current_price = 66760.0
            
            # Test case 1: TP too close (should fail)
            try:
                self.position_manager._validate_position_viability(
                    OrderType.BUY,
                    stop_loss=current_price - 300,  # 300pt SL (good)
                    take_profit=current_price + 10   # 10pt TP (too close!)
                )
                logger.error("❌ Validation should have failed for close TP")
                test2a_pass = False
            except ValueError as e:
                if "too close" in str(e).lower():
                    logger.info("✅ Correctly rejected position with TP too close to market")
                    test2a_pass = True
                else:
                    logger.error(f"❌ Wrong validation error: {e}")
                    test2a_pass = False
            
            # Test case 2: Valid TP/SL levels (should pass)
            try:
                self.position_manager._validate_position_viability(
                    OrderType.BUY,
                    stop_loss=current_price - 300,   # 300pt SL (good)
                    take_profit=current_price + 100  # 100pt TP (good)
                )
                logger.info("✅ Correctly accepted position with valid TP/SL levels")
                test2b_pass = True
            except ValueError as e:
                logger.error(f"❌ Valid position rejected: {e}")
                test2b_pass = False
            
            self.test_results['position_validation'] = test2a_pass and test2b_pass
            
        except Exception as e:
            logger.error(f"❌ Position validation test failed: {e}")
            self.test_results['position_validation'] = False
    
    def test_execution_delay_logic(self):
        """Test 3: Execution delay and instant closure detection"""
        logger.info("🔧 TEST 3: Execution Delay Logic")
        
        try:
            # Test that the delay logic is present in order executor
            import inspect
            
            # Check if the execution delay code is present
            source = inspect.getsource(self.order_executor.place_market_order)
            
            if "time.sleep(0.1)" in source:
                logger.info("✅ Execution delay (100ms) implemented")
                test3a_pass = True
            else:
                logger.error("❌ Execution delay not found in order executor")
                test3a_pass = False
            
            # Check if instant closure detection is present
            if "closed instantly" in source.lower():
                logger.info("✅ Instant closure detection implemented")
                test3b_pass = True
            else:
                logger.error("❌ Instant closure detection not found")
                test3b_pass = False
            
            self.test_results['execution_delay'] = test3a_pass and test3b_pass
            
        except Exception as e:
            logger.error(f"❌ Execution delay test failed: {e}")
            self.test_results['execution_delay'] = False
    
    def test_calibration_parameters(self):
        """Test 4: System calibration for DEX900DN"""
        logger.info("🔧 TEST 4: System Calibration Parameters")
        
        try:
            calibration_table = {
                'TP Distance': 'Min 100pt',
                'Slippage Check': '10pt max',
                'Position Verify Delay': '100ms',
                'Min Spread Buffer': '3x spread'
            }
            
            logger.info("📊 DEX900DN Calibration Table:")
            for param, value in calibration_table.items():
                logger.info(f"   {param}: {value}")
            
            # Verify key parameters are implemented
            checks = []
            
            # Check minimum TP distance in calm strategy
            try:
                source = inspect.getsource(self.calm_strategy.generate_signals)
                if "MIN_TP_DISTANCE = 100" in source:
                    checks.append(True)
                    logger.info("✅ Minimum TP distance (100pt) configured")
                else:
                    checks.append(False)
                    logger.error("❌ Minimum TP distance not found")
            except:
                checks.append(False)
            
            # Check position validation minimum distance
            try:
                source = inspect.getsource(self.position_manager._validate_position_viability)
                if "MIN_DISTANCE = 20" in source:
                    checks.append(True)
                    logger.info("✅ Position validation minimum distance (20pt) configured")
                else:
                    checks.append(False)
                    logger.error("❌ Position validation distance not found")
            except:
                checks.append(False)
            
            if all(checks):
                logger.info("✅ All calibration parameters correctly implemented")
                self.test_results['calibration'] = True
            else:
                logger.error("❌ Some calibration parameters missing")
                self.test_results['calibration'] = False
                
        except Exception as e:
            logger.error(f"❌ Calibration test failed: {e}")
            self.test_results['calibration'] = False
    
    def run_all_tests(self):
        """Run all synthetic index fix tests"""
        logger.info("🔧 STARTING SYNTHETIC INDEX FIXES VERIFICATION")
        logger.info("=" * 60)
        
        # Test 1: Enhanced TP Calculation
        self.test_enhanced_tp_calculation()
        
        # Test 2: Position Validation
        self.test_position_validation()
        
        # Test 3: Execution Delay
        self.test_execution_delay_logic()
        
        # Test 4: Calibration
        self.test_calibration_parameters()
        
        # Summary
        self.print_test_summary()
    
    def print_test_summary(self):
        """Print comprehensive test summary"""
        logger.info("=" * 60)
        logger.info("🎯 SYNTHETIC INDEX FIXES VERIFICATION SUMMARY")
        logger.info("=" * 60)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results.values() if result)
        
        test_names = {
            'enhanced_tp': 'Enhanced TP Calculation (100pt minimum)',
            'position_validation': 'Position Validation Layer',
            'execution_delay': 'Execution Delay & Instant Closure Detection',
            'calibration': 'System Calibration for DEX900DN'
        }
        
        for test_key, result in self.test_results.items():
            test_name = test_names.get(test_key, test_key)
            status_icon = '✅' if result else '❌'
            status_text = 'PASS' if result else 'FAIL'
            logger.info(f"{status_icon} {test_name}: {status_text}")
        
        logger.info("-" * 60)
        logger.info(f"📊 OVERALL RESULT: {passed_tests}/{total_tests} tests passed")
        
        if passed_tests == total_tests:
            logger.info("🎉 ALL SYNTHETIC INDEX FIXES VERIFIED - POSITION VANISHING SHOULD BE RESOLVED!")
        elif passed_tests >= total_tests * 0.75:
            logger.info("✅ Most fixes verified - system should be much more stable")
        else:
            logger.warning("⚠️ Critical issues remain - review failed tests")
        
        logger.info("=" * 60)

def main():
    """Main test execution"""
    print("\n" + "="*60)
    print("🔧 SYNTHETIC INDEX FIXES VERIFICATION")
    print("="*60)
    
    test_suite = SyntheticIndexFixesTest()
    test_suite.run_all_tests()
    
    print("\n🔧 Synthetic index fixes verification completed.")

if __name__ == "__main__":
    main()
