@echo off
REM ============================================================================
REM DEX900DN Trading System Launcher
REM Professional trading system for DEX 900 DOWN Index
REM ============================================================================

title DEX900DN Trading System

REM Set console colors (Green on Black for professional look)
color 0A

echo.
echo ============================================================================
echo                    DEX900DN TRADING SYSTEM LAUNCHER
echo ============================================================================
echo.
echo Symbol: DEX 900 DOWN Index
echo Magic Number: 900900
echo System: Professional Time-Cycle Trading with Risk Management
echo.
echo Starting system checks...
echo.

REM Change to the script directory
cd /d "%~dp0"

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Python is not installed or not in PATH
    echo Please install Python 3.8+ and add it to your PATH
    echo.
    pause
    exit /b 1
)

echo [OK] Python installation found
python --version

REM Check if required directories exist
if not exist "src" (
    echo [ERROR] Source directory 'src' not found
    echo Please ensure you're running this from the project root directory
    echo.
    pause
    exit /b 1
)

if not exist "config" (
    echo [ERROR] Config directory 'config' not found
    echo.
    pause
    exit /b 1
)

echo [OK] Project directories found

REM Create logs directory if it doesn't exist
if not exist "logs" (
    mkdir logs
    echo [INFO] Created logs directory
)

REM Create data directory if it doesn't exist
if not exist "data" (
    mkdir data
    echo [INFO] Created data directory
)

echo [OK] Directory structure validated

REM Check if MT5 is running (optional check)
tasklist /FI "IMAGENAME eq terminal64.exe" 2>NUL | find /I /N "terminal64.exe" >NUL
if errorlevel 1 (
    echo [WARNING] MetaTrader 5 (terminal64.exe) not detected
    echo The system will run in simulation mode if MT5 is not connected
    echo Make sure MT5 is open and logged into your demo account for live trading
    echo.
) else (
    echo [OK] MetaTrader 5 detected running
)

REM Install/check dependencies
echo.
echo Checking Python dependencies...
pip install -q MetaTrader5 psutil numpy pandas ta-lib matplotlib 2>nul
if errorlevel 1 (
    echo [WARNING] Some dependencies may not be installed
    echo The system will attempt to run anyway
) else (
    echo [OK] Dependencies checked
)

echo.
echo ============================================================================
echo                           SYSTEM STARTUP OPTIONS
echo ============================================================================
echo.
echo 1. Start Production Trading System (Recommended)
echo 2. Run System Validation Only
echo 3. Start in Test Mode
echo 4. View System Status
echo 5. Exit
echo.
set /p choice="Please select an option (1-5): "

if "%choice%"=="1" goto start_production
if "%choice%"=="2" goto run_validation
if "%choice%"=="3" goto start_test
if "%choice%"=="4" goto view_status
if "%choice%"=="5" goto exit_script

echo Invalid choice. Starting production system by default...
goto start_production

:start_production
echo.
echo ============================================================================
echo                      STARTING PRODUCTION TRADING SYSTEM
echo ============================================================================
echo.
echo [INFO] Launching DEX900DN Production System...
echo [INFO] Press Ctrl+C to stop the system gracefully
echo [INFO] Logs will be saved in the 'logs' directory
echo.

REM Start the production system
python deploy/production_launcher.py

if errorlevel 1 (
    echo.
    echo [ERROR] Production system failed to start
    echo Check the logs directory for error details
    echo.
    goto error_exit
)

echo.
echo [INFO] Production system has stopped
goto normal_exit

:run_validation
echo.
echo ============================================================================
echo                         RUNNING SYSTEM VALIDATION
echo ============================================================================
echo.
echo [INFO] Running comprehensive system validation...
echo [INFO] This will test all components without starting live trading
echo.

python validate_complete_system.py

if errorlevel 1 (
    echo.
    echo [WARNING] System validation found issues
    echo Review the validation report for details
    echo.
) else (
    echo.
    echo [SUCCESS] System validation completed successfully
    echo System is ready for production trading
    echo.
)

echo.
set /p restart="Would you like to start the production system now? (y/n): "
if /i "%restart%"=="y" goto start_production
goto normal_exit

:start_test
echo.
echo ============================================================================
echo                           STARTING TEST MODE
echo ============================================================================
echo.
echo [INFO] Starting system in test mode...
echo [INFO] This mode runs with reduced monitoring and simulation data
echo.

python deploy/production_launcher.py --test-mode

if errorlevel 1 (
    echo.
    echo [ERROR] Test mode failed to start
    echo.
    goto error_exit
)

echo.
echo [INFO] Test mode has stopped
goto normal_exit

:view_status
echo.
echo ============================================================================
echo                            SYSTEM STATUS CHECK
echo ============================================================================
echo.
echo [INFO] Checking system status and configuration...
echo.

REM Run environment validation only
python deploy/production_launcher.py --validate-only

echo.
echo [INFO] Status check completed
echo.
set /p continue="Press any key to return to menu..."
goto start

:error_exit
echo.
echo ============================================================================
echo                              ERROR OCCURRED
echo ============================================================================
echo.
echo The DEX900DN trading system encountered an error.
echo.
echo Troubleshooting steps:
echo 1. Check that MetaTrader 5 is open and logged in
echo 2. Verify your internet connection
echo 3. Review the log files in the 'logs' directory
echo 4. Run system validation to identify issues
echo.
echo For support, check the validation report and log files.
echo.
pause
exit /b 1

:normal_exit
echo.
echo ============================================================================
echo                         SYSTEM SHUTDOWN COMPLETE
echo ============================================================================
echo.
echo Thank you for using the DEX900DN Trading System
echo.
echo Session Summary:
echo - All positions have been closed safely
echo - System logs have been saved
echo - Risk reports have been generated
echo.
echo The system is now stopped and ready for restart.
echo.
pause
exit /b 0

:exit_script
echo.
echo Exiting DEX900DN launcher...
exit /b 0

REM ============================================================================
REM End of DEX900DN Trading System Launcher
REM ============================================================================
