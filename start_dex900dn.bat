@echo off
title DEX900DN Trading System
color 0A

echo.
echo ============================================================================
echo                    DEX900DN TRADING SYSTEM
echo ============================================================================
echo.
echo Symbol: DEX 900 DOWN Index
echo Magic Number: 900900
echo.
echo [INFO] Starting DEX900DN Trading System...
echo.

cd /d "%~dp0"

REM Check virtual environment
if not exist "venv\Scripts\python.exe" (
    echo [ERROR] Virtual environment not found
    pause
    exit /b 1
)

REM Create directories
if not exist "logs" mkdir logs
if not exist "data" mkdir data

echo [INFO] Launching production system...
echo [INFO] Press Ctrl+C to stop gracefully
echo.

REM Start system
set PYTHONPATH=%CD%;%CD%\src
venv\Scripts\python.exe deploy/production_launcher.py

echo.
echo [INFO] System stopped
pause




