"""
DEX900DN Phase Detection System - Complete Demo
Demonstrates the full integration of cycle tracking, data handling, and phase classification
"""

import sys
import os
import time
from datetime import datetime, timedelta

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.modules.cycle_engine import TimeCycleTracker
from src.modules.data_feed import <PERSON>Handler, TickData
from src.modules.phase_classifier import PhaseClassifier, MarketPhase, create_phase_classifier
from config.config import config

def print_header(title):
    """Print a formatted header"""
    print("\n" + "="*60)
    print(f"  {title}")
    print("="*60)

def print_section(title):
    """Print a formatted section header"""
    print(f"\n--- {title} ---")

def simulate_market_scenario(scenario_name, cycle_tracker, data_handler, classifier):
    """Simulate a complete market scenario"""
    print_section(f"SCENARIO: {scenario_name}")
    
    # Get initial state
    cycle_progress = cycle_tracker.get_cycle_progress()
    market_summary = data_handler.get_market_summary()
    phase_summary = classifier.get_phase_summary()
    
    print(f"Cycle State: {cycle_tracker.get_state_summary()}")
    print(f"Market Data Points: {market_summary['data_points']['tick_buffer_size']}")
    print(f"Current Phase: {phase_summary['current_phase']}")
    print(f"Confidence: {phase_summary['confidence']:.2f}")
    print(f"Reasoning: {phase_summary['reasoning']}")
    
    # Check trading decision
    should_trade, trade_reason = classifier.should_trade()
    print(f"Trading Decision: {'✅ TRADE' if should_trade else '❌ WAIT'}")
    print(f"Reason: {trade_reason}")
    
    return phase_summary['current_phase']

def demo_calm_market():
    """Demonstrate CALM market detection"""
    print_header("CALM MARKET SCENARIO")
    
    # Setup components
    cycle_tracker = TimeCycleTracker()
    data_handler = DataHandler()
    classifier = create_phase_classifier(cycle_tracker, data_handler)
    
    print("Setting up calm market conditions...")
    
    # 1. No active cycle (outside spike window)
    cycle_tracker.reset_cycle(0.01)
    
    # 2. Add stable, low-volatility data
    base_price = 25000.0
    print("Adding 200 data points with low volatility...")
    
    for i in range(200):
        # Small, predictable price movements
        price_change = (i % 4) - 1.5  # -1.5, -0.5, 0.5, 1.5 pattern
        tick = TickData(
            timestamp=datetime.now() - timedelta(seconds=200-i),
            bid=base_price + price_change,
            ask=base_price + price_change + 5,
            volume=100 + (i % 10),  # Consistent volume
            spread=5.0
        )
        data_handler.tick_buffer.append(tick)
        data_handler.price_buffer.append(tick.mid_price)
        data_handler.volume_buffer.append(tick.volume)
    
    # 3. Simulate multiple classifications for stability
    for i in range(5):
        classifier.classify_phase()
        time.sleep(0.01)
    
    # 4. Show results
    return simulate_market_scenario("CALM MARKET", cycle_tracker, data_handler, classifier)

def demo_spike_market():
    """Demonstrate SPIKE market detection"""
    print_header("SPIKE MARKET SCENARIO")
    
    # Setup components
    cycle_tracker = TimeCycleTracker()
    data_handler = DataHandler()
    classifier = create_phase_classifier(cycle_tracker, data_handler)
    
    print("Setting up spike market conditions...")
    
    # 1. Start cycle and move to spike window
    cycle_tracker.start_spike_countdown(datetime.now() - timedelta(seconds=850))
    print(f"Cycle started, moving to spike window...")
    
    # 2. Add high-volatility data with price drops
    base_price = 25000.0
    print("Adding volatile market data with significant price movements...")
    
    for i in range(100):
        # Create high volatility with large price swings
        if i < 20:
            # Initial volatile movement
            price_change = (-1)**i * (i * 25)  # Large alternating moves
        else:
            # Major price drop
            price_change = -500 - (i - 20) * 30  # Sustained drop
        
        tick = TickData(
            timestamp=datetime.now() - timedelta(seconds=100-i),
            bid=base_price + price_change,
            ask=base_price + price_change + 5,
            volume=200 + (i * 5),  # Increasing volume
            spread=5.0
        )
        data_handler.tick_buffer.append(tick)
        data_handler.price_buffer.append(tick.mid_price)
        data_handler.volume_buffer.append(tick.volume)
    
    # 3. Simulate multiple classifications for stability
    for i in range(5):
        classifier.classify_phase()
        time.sleep(0.01)
    
    # 4. Show results
    return simulate_market_scenario("SPIKE MARKET", cycle_tracker, data_handler, classifier)

def demo_hold_market():
    """Demonstrate HOLD market detection"""
    print_header("HOLD MARKET SCENARIO")
    
    # Setup components
    cycle_tracker = TimeCycleTracker()
    data_handler = DataHandler()
    classifier = create_phase_classifier(cycle_tracker, data_handler)
    
    print("Setting up transitional market conditions...")
    
    # 1. Start cycle and move to spike window
    cycle_tracker.start_spike_countdown(datetime.now() - timedelta(seconds=850))
    
    # 2. Add moderate volatility data (not enough for spike)
    base_price = 25000.0
    print("Adding moderate volatility data...")
    
    for i in range(50):
        # Moderate price movements - not calm, not spike
        price_change = (i % 8) * 8 - 28  # -28 to 28 point range
        tick = TickData(
            timestamp=datetime.now() - timedelta(seconds=50-i),
            bid=base_price + price_change,
            ask=base_price + price_change + 5,
            volume=120 + (i % 15),  # Moderate volume variation
            spread=5.0
        )
        data_handler.tick_buffer.append(tick)
        data_handler.price_buffer.append(tick.mid_price)
        data_handler.volume_buffer.append(tick.volume)
    
    # 3. Show results
    return simulate_market_scenario("HOLD MARKET", cycle_tracker, data_handler, classifier)

def demo_phase_transitions():
    """Demonstrate phase transitions over time"""
    print_header("PHASE TRANSITIONS DEMO")
    
    # Setup components
    cycle_tracker = TimeCycleTracker()
    data_handler = DataHandler()
    classifier = create_phase_classifier(cycle_tracker, data_handler)
    
    phases_detected = []
    
    print("Simulating market evolution over time...")
    
    # Phase 1: Start in CALM
    print_section("Phase 1: CALM Market")
    cycle_tracker.reset_cycle(0.01)
    base_price = 25000.0
    
    for i in range(30):
        price_change = (i % 2) * 2 - 1  # Small movements
        tick = TickData(
            timestamp=datetime.now() - timedelta(seconds=30-i),
            bid=base_price + price_change,
            ask=base_price + price_change + 5,
            volume=100,
            spread=5.0
        )
        data_handler.price_buffer.append(tick.mid_price)
        data_handler.volume_buffer.append(tick.volume)
    
    signal = classifier.classify_phase()
    phases_detected.append(signal.phase.value)
    print(f"Phase: {signal.phase.value} (Confidence: {signal.confidence:.2f})")
    
    # Phase 2: Transition to HOLD (cycle starts)
    print_section("Phase 2: Cycle Activation")
    cycle_tracker.start_spike_countdown(datetime.now() - timedelta(seconds=850))
    
    signal = classifier.classify_phase()
    phases_detected.append(signal.phase.value)
    print(f"Phase: {signal.phase.value} (Confidence: {signal.confidence:.2f})")
    
    # Phase 3: Transition to SPIKE (add volatility)
    print_section("Phase 3: Market Spike")
    for i in range(20):
        price_change = -200 - (i * 40)  # Major price drop
        tick = TickData(
            timestamp=datetime.now() - timedelta(seconds=20-i),
            bid=base_price + price_change,
            ask=base_price + price_change + 5,
            volume=300,
            spread=5.0
        )
        data_handler.price_buffer.append(tick.mid_price)
        data_handler.volume_buffer.append(tick.volume)
    
    signal = classifier.classify_phase()
    phases_detected.append(signal.phase.value)
    print(f"Phase: {signal.phase.value} (Confidence: {signal.confidence:.2f})")
    
    print_section("TRANSITION SUMMARY")
    print(f"Phases detected: {' → '.join(phases_detected)}")
    print(f"Total transitions: {len(set(phases_detected))}")
    
    return phases_detected

def demo_system_performance():
    """Demonstrate system performance metrics"""
    print_header("SYSTEM PERFORMANCE METRICS")
    
    # Setup
    cycle_tracker = TimeCycleTracker()
    data_handler = DataHandler()
    classifier = create_phase_classifier(cycle_tracker, data_handler)
    
    # Performance test
    print("Running performance test...")
    
    start_time = time.time()
    classifications = 0
    
    # Add test data
    base_price = 25000.0
    for i in range(1000):
        tick = TickData(
            timestamp=datetime.now() - timedelta(seconds=1000-i),
            bid=base_price + (i % 100) - 50,
            ask=base_price + (i % 100) - 50 + 5,
            volume=100 + (i % 50),
            spread=5.0
        )
        data_handler.tick_buffer.append(tick)
        data_handler.price_buffer.append(tick.mid_price)
        data_handler.volume_buffer.append(tick.volume)
        
        # Classify every 10th tick
        if i % 10 == 0:
            classifier.classify_phase()
            classifications += 1
    
    end_time = time.time()
    processing_time = end_time - start_time
    
    print(f"Processed 1,000 ticks in {processing_time:.3f} seconds")
    print(f"Rate: {1000/processing_time:.0f} ticks/second")
    print(f"Classifications: {classifications}")
    print(f"Classification rate: {classifications/processing_time:.0f} classifications/second")
    
    # Memory usage
    memory_usage = {
        "Tick buffer": len(data_handler.tick_buffer),
        "Price buffer": len(data_handler.price_buffer),
        "Volume buffer": len(data_handler.volume_buffer),
        "Phase history": len(classifier.phase_history)
    }
    
    print("\nMemory Usage:")
    for component, size in memory_usage.items():
        print(f"  {component}: {size} items")

def main():
    """Run complete demo of the DEX900DN Phase Detection System"""
    print_header("DEX900DN PHASE DETECTION SYSTEM - COMPLETE DEMO")
    print("Demonstrating time-cycle driven + phase-specific execution")
    print(f"System Configuration:")
    print(f"  Spike Volatility Threshold: {config.SPIKE_VOLATILITY_THRESHOLD}")
    print(f"  Calm Volatility Threshold: {config.CALM_VOLATILITY_THRESHOLD}")
    print(f"  Minimum Spike Drop: {config.MIN_SPIKE_DROP} points")
    print(f"  Cycle Duration: {config.SPIKE_CYCLE_DURATION} seconds")
    
    # Run all demos
    calm_phase = demo_calm_market()
    spike_phase = demo_spike_market()
    hold_phase = demo_hold_market()
    transitions = demo_phase_transitions()
    demo_system_performance()
    
    # Summary
    print_header("DEMO SUMMARY")
    print("✅ All phase detection scenarios completed successfully!")
    print(f"✅ CALM phase detection: {calm_phase}")
    print(f"✅ SPIKE phase detection: {spike_phase}")
    print(f"✅ HOLD phase detection: {hold_phase}")
    print(f"✅ Phase transitions: {' → '.join(transitions)}")
    print("\n🎯 System is ready for trading strategy integration!")
    print("📊 Next steps: Implement trading strategies for each phase")

if __name__ == "__main__":
    main()
