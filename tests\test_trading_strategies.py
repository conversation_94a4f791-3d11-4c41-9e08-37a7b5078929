"""
Comprehensive Test Suite for Trading Strategies
Tests calm strategy, spike strategy, and strategy manager integration
"""

import sys
import os
import unittest
import time
from datetime import datetime, timedelta

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))

from src.modules.cycle_engine import TimeCycleTracker
from src.modules.data_feed import DataHandler, TickData
from src.modules.phase_classifier import PhaseClassifier, MarketPhase
from src.strategies.calm_strategy import CalmPhaseStrategy, SignalType
from src.strategies.spike_strategy import SpikePhaseStrategy, SpikeSignalType
from src.strategies.strategy_manager import StrategyManager, StrategyState
from config.config import config

class TestCalmStrategy(unittest.TestCase):
    """Test calm phase trading strategy"""
    
    def setUp(self):
        self.data_handler = DataHandler()
        self.strategy = CalmPhaseStrategy(self.data_handler)
        self._populate_base_data()
    
    def _populate_base_data(self):
        """Add base data for testing"""
        base_price = 25000.0
        for i in range(100):
            tick = TickData(
                timestamp=datetime.now() - timedelta(seconds=100-i),
                bid=base_price + (i % 5) - 2,
                ask=base_price + (i % 5) - 2 + 5,
                volume=100,
                spread=5.0
            )
            self.data_handler.tick_buffer.append(tick)
            self.data_handler.price_buffer.append(tick.mid_price)
            self.data_handler.volume_buffer.append(tick.volume)
    
    def test_bollinger_bands_calculation(self):
        """Test Bollinger Bands calculation"""
        middle, upper, lower = self.strategy.calculate_bollinger_bands()
        
        self.assertGreater(middle, 0)
        self.assertGreater(upper, middle)
        self.assertLess(lower, middle)
        self.assertIsInstance(middle, float)
    
    def test_rsi_calculation(self):
        """Test RSI calculation"""
        rsi = self.strategy.calculate_rsi()
        
        self.assertGreaterEqual(rsi, 0)
        self.assertLessEqual(rsi, 100)
        self.assertIsInstance(rsi, float)
    
    def test_calm_entry_conditions(self):
        """Test calm strategy entry signal generation"""
        # Add data that should trigger entry (price near lower BB + RSI > 50)
        base_price = 24950.0  # Lower than previous data
        
        # Add trending up data to get RSI > 50
        for i in range(20):
            price = base_price + i * 2  # Gradual uptrend
            tick = TickData(
                timestamp=datetime.now() - timedelta(seconds=20-i),
                bid=price,
                ask=price + 5,
                volume=100,
                spread=5.0
            )
            self.data_handler.price_buffer.append(tick.mid_price)
        
        signal = self.strategy.scalp_entry_signal()
        
        # Should generate some kind of signal (entry or no signal)
        self.assertIn(signal.signal_type, [SignalType.ENTRY_LONG, SignalType.NO_SIGNAL])
        self.assertIsInstance(signal.confidence, float)
        self.assertIsInstance(signal.reasoning, str)
    
    def test_position_lifecycle(self):
        """Test complete position lifecycle"""
        # Generate entry signal
        signal = self.strategy.scalp_entry_signal()
        
        if signal.signal_type == SignalType.ENTRY_LONG:
            # Open position
            success = self.strategy.open_position(signal)
            self.assertTrue(success)
            self.assertIsNotNone(self.strategy.current_position)
            
            # Test exit strategy
            exit_signal = self.strategy.calm_exit_strategy()
            self.assertIn(exit_signal.signal_type, [
                SignalType.NO_SIGNAL, SignalType.EXIT_PROFIT, SignalType.EXIT_LOSS
            ])
            
            # Force close for testing
            if exit_signal.signal_type != SignalType.NO_SIGNAL:
                close_success = self.strategy.close_position(exit_signal)
                self.assertTrue(close_success)
                self.assertIsNone(self.strategy.current_position)
    
    def test_strategy_summary(self):
        """Test strategy summary generation"""
        summary = self.strategy.get_strategy_summary()
        
        required_fields = [
            'strategy_name', 'current_price', 'active_position',
            'total_trades', 'winning_trades', 'win_rate', 'total_pnl'
        ]
        
        for field in required_fields:
            self.assertIn(field, summary)

class TestSpikeStrategy(unittest.TestCase):
    """Test spike phase trading strategy"""
    
    def setUp(self):
        self.data_handler = DataHandler()
        self.strategy = SpikePhaseStrategy(self.data_handler)
        self._populate_base_data()
    
    def _populate_base_data(self):
        """Add base data for testing"""
        base_price = 25000.0
        for i in range(50):
            tick = TickData(
                timestamp=datetime.now() - timedelta(seconds=50-i),
                bid=base_price,
                ask=base_price + 5,
                volume=100,
                spread=5.0
            )
            self.data_handler.tick_buffer.append(tick)
            self.data_handler.price_buffer.append(tick.mid_price)
            self.data_handler.volume_buffer.append(tick.volume)
    
    def test_spike_detection(self):
        """Test spike detection logic"""
        # Clear existing data and add spike scenario
        self.data_handler.price_buffer.clear()
        
        base_price = 25000.0
        # Add major price drop
        for i in range(10):
            drop_price = base_price - (i * 60)  # 600 point drop
            self.data_handler.price_buffer.append(drop_price)
        
        spike_detected, analysis = self.strategy.detect_spike_entry_conditions()
        
        self.assertIsInstance(spike_detected, bool)
        self.assertIsInstance(analysis, dict)
        self.assertIn('price_drop_detected', analysis)
        self.assertIn('drop_amount', analysis)
    
    def test_momentum_fade_entry(self):
        """Test momentum fade entry logic"""
        # Set up spike detection first
        self.strategy.last_spike_detected = datetime.now()
        self.strategy.spike_high_price = 25000.0
        
        # Add retracement data
        base_price = 24400.0  # After spike low
        for i in range(10):
            retrace_price = base_price + (i * 20)  # Retracing up
            self.data_handler.price_buffer.append(retrace_price)
        
        signal = self.strategy.momentum_fade_entry()
        
        self.assertIn(signal.signal_type, [SpikeSignalType.ENTRY_SHORT, SpikeSignalType.NO_SIGNAL])
        self.assertIsInstance(signal.confidence, float)
        self.assertIsInstance(signal.reasoning, str)
    
    def test_spike_position_lifecycle(self):
        """Test spike position lifecycle with partial closes"""
        # Create entry signal manually
        from src.strategies.spike_strategy import SpikeSignal
        
        entry_signal = SpikeSignal(
            signal_type=SpikeSignalType.ENTRY_SHORT,
            price=25000.0,
            timestamp=datetime.now(),
            confidence=0.8,
            reasoning="Test entry",
            stop_loss=25500.0,
            take_profit=24500.0,
            position_size=0.01
        )
        
        # Open position
        success = self.strategy.open_position(entry_signal)
        self.assertTrue(success)
        self.assertIsNotNone(self.strategy.current_position)
        
        # Test exit strategy
        exit_signal = self.strategy.spike_exit_strategy()
        self.assertIn(exit_signal.signal_type, [
            SpikeSignalType.NO_SIGNAL, SpikeSignalType.PARTIAL_EXIT,
            SpikeSignalType.FULL_EXIT, SpikeSignalType.STOP_LOSS
        ])
    
    def test_strategy_summary(self):
        """Test spike strategy summary"""
        summary = self.strategy.get_strategy_summary()
        
        required_fields = [
            'strategy_name', 'current_price', 'active_position',
            'total_trades', 'winning_trades', 'win_rate', 'total_pnl'
        ]
        
        for field in required_fields:
            self.assertIn(field, summary)

class TestStrategyManager(unittest.TestCase):
    """Test strategy manager integration"""
    
    def setUp(self):
        self.cycle_tracker = TimeCycleTracker()
        self.data_handler = DataHandler()
        self.manager = StrategyManager(self.cycle_tracker, self.data_handler)
        self._populate_test_data()
    
    def _populate_test_data(self):
        """Add test data for manager testing"""
        base_price = 25000.0
        for i in range(100):
            tick = TickData(
                timestamp=datetime.now() - timedelta(seconds=100-i),
                bid=base_price + (i % 10) - 5,
                ask=base_price + (i % 10) - 5 + 5,
                volume=100 + (i % 20),
                spread=5.0
            )
            self.data_handler.tick_buffer.append(tick)
            self.data_handler.price_buffer.append(tick.mid_price)
            self.data_handler.volume_buffer.append(tick.volume)
    
    def test_manager_initialization(self):
        """Test manager initialization"""
        self.assertEqual(self.manager.current_state, StrategyState.IDLE)
        self.assertIsNone(self.manager.active_strategy)
        self.assertEqual(self.manager.total_trades, 0)
        self.assertEqual(self.manager.total_pnl, 0.0)
    
    def test_market_update_processing(self):
        """Test market update processing"""
        actions = self.manager.process_market_update()
        
        self.assertIsInstance(actions, list)
        # Actions could be empty if no trading conditions are met
        for action in actions:
            self.assertIn(action.action_type, [
                "OPEN_POSITION", "CLOSE_POSITION", "PARTIAL_CLOSE"
            ])
            self.assertIn(action.strategy, ["CALM", "SPIKE"])
    
    def test_calm_phase_processing(self):
        """Test calm phase specific processing"""
        # Set up calm phase conditions
        self.cycle_tracker.reset_cycle(0.01)  # Outside spike window
        
        # Add low volatility data
        base_price = 25000.0
        for i in range(50):
            price_change = (i % 2) * 2 - 1  # Small movements
            tick = TickData(
                timestamp=datetime.now() - timedelta(seconds=50-i),
                bid=base_price + price_change,
                ask=base_price + price_change + 5,
                volume=100,
                spread=5.0
            )
            self.data_handler.price_buffer.append(tick.mid_price)
            self.data_handler.volume_buffer.append(tick.volume)
        
        actions = self.manager.process_market_update()
        
        # Should process without errors
        self.assertIsInstance(actions, list)
    
    def test_spike_phase_processing(self):
        """Test spike phase specific processing"""
        # Set up spike phase conditions
        self.cycle_tracker.start_spike_countdown(datetime.now() - timedelta(seconds=850))
        
        # Add volatile data with price drop
        base_price = 25000.0
        for i in range(20):
            price_change = -200 - (i * 30)  # Major price drop
            tick = TickData(
                timestamp=datetime.now() - timedelta(seconds=20-i),
                bid=base_price + price_change,
                ask=base_price + price_change + 5,
                volume=200,
                spread=5.0
            )
            self.data_handler.price_buffer.append(tick.mid_price)
            self.data_handler.volume_buffer.append(tick.volume)
        
        actions = self.manager.process_market_update()
        
        # Should process without errors
        self.assertIsInstance(actions, list)
    
    def test_action_execution(self):
        """Test action execution"""
        # Create a test action
        from src.strategies.strategy_manager import StrategyAction
        from src.strategies.calm_strategy import TradingSignal
        
        test_signal = TradingSignal(
            signal_type=SignalType.ENTRY_LONG,
            price=25000.0,
            timestamp=datetime.now(),
            confidence=0.8,
            reasoning="Test signal",
            stop_loss=24700.0,
            take_profit=25100.0,
            position_size=0.05
        )
        
        test_action = StrategyAction(
            action_type="OPEN_POSITION",
            strategy="CALM",
            signal=test_signal,
            timestamp=datetime.now(),
            reasoning="Test action"
        )
        
        # Execute action
        success = self.manager.execute_action(test_action)
        
        # Should execute without errors (success depends on conditions)
        self.assertIsInstance(success, bool)
    
    def test_manager_summary(self):
        """Test manager summary generation"""
        summary = self.manager.get_manager_summary()
        
        required_fields = [
            'manager_state', 'active_strategy', 'current_phase',
            'total_trades', 'daily_trades', 'total_pnl', 'daily_pnl',
            'active_positions', 'strategy_performance'
        ]
        
        for field in required_fields:
            self.assertIn(field, summary)
    
    def test_daily_limit_checking(self):
        """Test daily limit enforcement"""
        # Simulate reaching daily profit limit
        self.manager.daily_pnl = 300.0  # Assuming 10k account, this is 3%
        
        limit_reached = self.manager._check_daily_limits()
        
        # Should detect limit breach
        self.assertTrue(limit_reached)
        self.assertTrue(self.manager.daily_limit_reached)
        self.assertEqual(self.manager.current_state, StrategyState.RISK_HALT)
    
    def test_force_close_positions(self):
        """Test force closing all positions"""
        # First open a test position
        from src.strategies.calm_strategy import TradingSignal
        test_signal = TradingSignal(
            signal_type=SignalType.ENTRY_LONG,
            price=25000.0,
            timestamp=datetime.now(),
            confidence=0.8,
            reasoning="Test position",
            stop_loss=24700.0,
            take_profit=25100.0,
            position_size=0.05
        )
        
        self.manager.calm_strategy.open_position(test_signal)
        
        # Force close all positions
        close_actions = self.manager.force_close_all_positions("Test close")
        
        self.assertIsInstance(close_actions, list)
        if close_actions:  # If there were positions to close
            self.assertEqual(close_actions[0].action_type, "CLOSE_POSITION")

def run_integration_test():
    """Run integration test with realistic market scenario"""
    print("\n" + "="*60)
    print("RUNNING TRADING STRATEGY INTEGRATION TEST")
    print("="*60)
    
    # Setup complete system
    cycle_tracker = TimeCycleTracker()
    data_handler = DataHandler()
    manager = StrategyManager(cycle_tracker, data_handler)
    
    print("1. System initialized")
    
    # Simulate market data over time
    base_price = 25000.0
    total_actions = 0
    
    for minute in range(30):  # 30 minutes of simulation
        print(f"\n--- Minute {minute + 1} ---")
        
        # Add market data for this minute
        for second in range(60):
            # Simulate different market conditions
            if minute < 10:  # Calm market
                price_change = (second % 4) - 1.5  # Small movements
                volume = 100
            elif minute < 20:  # Transition
                price_change = (second % 8) - 4  # Medium movements
                volume = 150
            else:  # Spike conditions
                if second < 10:  # Major drop
                    price_change = -50 - (second * 20)
                else:  # Recovery
                    price_change = -500 + ((second - 10) * 15)
                volume = 300
            
            tick = TickData(
                timestamp=datetime.now() - timedelta(seconds=(30-minute)*60 + (60-second)),
                bid=base_price + price_change,
                ask=base_price + price_change + 5,
                volume=volume,
                spread=5.0
            )
            
            data_handler.tick_buffer.append(tick)
            data_handler.price_buffer.append(tick.mid_price)
            data_handler.volume_buffer.append(tick.volume)
        
        # Process market update
        actions = manager.process_market_update()
        total_actions += len(actions)
        
        # Execute actions
        for action in actions:
            success = manager.execute_action(action)
            print(f"  Action: {action.action_type} | {action.strategy} | Success: {success}")
        
        # Show summary every 10 minutes
        if (minute + 1) % 10 == 0:
            summary = manager.get_manager_summary()
            print(f"  Summary: State={summary['manager_state']}, Trades={summary['total_trades']}, P/L={summary['total_pnl']:.1f}")
    
    # Final summary
    final_summary = manager.get_manager_summary()
    print(f"\n--- FINAL RESULTS ---")
    print(f"Total Actions: {total_actions}")
    print(f"Total Trades: {final_summary['total_trades']}")
    print(f"Total P/L: {final_summary['total_pnl']:.1f} points")
    print(f"Manager State: {final_summary['manager_state']}")
    print(f"Calm Strategy: {final_summary['strategy_performance']['calm']['trades']} trades, {final_summary['strategy_performance']['calm']['win_rate']:.1f}% win rate")
    print(f"Spike Strategy: {final_summary['strategy_performance']['spike']['trades']} trades, {final_summary['strategy_performance']['spike']['win_rate']:.1f}% win rate")

def main():
    """Run all trading strategy tests"""
    print("DEX900DN TRADING STRATEGIES - COMPREHENSIVE TEST SUITE")
    print("="*60)
    
    # Run unit tests
    unittest.main(argv=[''], exit=False, verbosity=2)
    
    # Run integration test
    run_integration_test()
    
    print("\n" + "="*60)
    print("ALL TRADING STRATEGY TESTS COMPLETED!")
    print("="*60)

if __name__ == "__main__":
    main()
