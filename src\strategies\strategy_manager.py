"""
Strategy Manager for DEX900DN Trading System
Coordinates between phase detection and strategy execution
Manages position lifecycle and strategy switching
"""

import logging
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List, Union
from dataclasses import dataclass
from enum import Enum

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))
from config.config import config
from src.modules.cycle_engine import TimeCycleTracker
from src.modules.data_feed import DataHandler
from src.modules.phase_classifier import PhaseClassifier, MarketPhase, create_phase_classifier
from src.strategies.calm_strategy import CalmPhaseStrategy, TradingSignal, SignalType
from src.strategies.spike_strategy import SpikePhaseStrategy, SpikeSignal, SpikeSignalType

# Setup logging
logging.basicConfig(level=getattr(logging, config.LOG_LEVEL))
logger = logging.getLogger(__name__)

class StrategyState(Enum):
    """Strategy manager states"""
    IDLE = "IDLE"
    CALM_ACTIVE = "CALM_ACTIVE"
    SPIKE_ACTIVE = "SPIKE_ACTIVE"
    POSITION_MANAGEMENT = "POSITION_MANAGEMENT"
    RISK_HALT = "RISK_HALT"

@dataclass
class StrategyAction:
    """Action to be taken by strategy manager"""
    action_type: str
    strategy: str
    signal: Union[TradingSignal, SpikeSignal]
    timestamp: datetime
    reasoning: str

class StrategyManager:
    """
    Central Strategy Manager for DEX900DN Trading System
    
    Responsibilities:
    - Coordinate phase detection with strategy execution
    - Manage position lifecycle across strategies
    - Enforce risk management rules
    - Handle strategy switching and conflicts
    """
    
    def __init__(self, cycle_tracker: TimeCycleTracker = None,
                 data_handler: DataHandler = None,
                 phase_classifier: PhaseClassifier = None,
                 calm_strategy: CalmPhaseStrategy = None,
                 spike_strategy: SpikePhaseStrategy = None):

        # Core components (use shared instances to avoid duplication)
        self.cycle_tracker = cycle_tracker or TimeCycleTracker()
        self.data_handler = data_handler or DataHandler()
        self.phase_classifier = phase_classifier or create_phase_classifier(self.cycle_tracker, self.data_handler)

        # Strategy engines (use shared instances to avoid duplication)
        self.calm_strategy = calm_strategy or CalmPhaseStrategy(self.data_handler)
        self.spike_strategy = spike_strategy or SpikePhaseStrategy(self.data_handler)
        
        # Manager state
        self.current_state = StrategyState.IDLE
        self.active_strategy = None
        self.last_phase_change = datetime.now()
        self.strategy_switch_cooldown = 30  # 30 seconds between strategy switches
        
        # Performance tracking
        self.total_trades = 0
        self.total_pnl = 0.0
        self.daily_pnl = 0.0
        self.daily_trade_count = 0
        self.last_daily_reset = datetime.now().date()
        
        # Risk management
        self.daily_limit_reached = False
        self.max_concurrent_positions = 2  # Maximum 2 positions at a time

        # CRITICAL: Trade flood prevention
        self.last_trade_time = datetime.now()
        self.min_trade_interval = 60  # 60 seconds between trades

        # Action history
        self.action_history: List[StrategyAction] = []
        self.max_history_length = 100
        
        logger.info("StrategyManager initialized")
        logger.info(f"Daily limits: ±{config.DAILY_PROFIT_LIMIT*100}% P/L")

    def start(self):
        """Start the strategy manager"""
        logger.info("StrategyManager started")
        return True

    def stop(self):
        """Stop the strategy manager"""
        logger.info("StrategyManager stopped")
        return True

    def emergency_stop(self):
        """Emergency stop the strategy manager"""
        logger.warning("StrategyManager emergency stop")
        return True

    def process_market_update(self) -> List[StrategyAction]:
        """
        Main processing loop - analyze market and generate actions
        
        Returns:
            List[StrategyAction]: Actions to be executed
        """
        actions = []
        current_time = datetime.now()
        
        # Reset daily counters if new day
        self._check_daily_reset()
        
        # Check daily limits
        if self._check_daily_limits():
            return actions  # No actions if limits reached
        
        # Get current phase
        phase_signal = self.phase_classifier.classify_phase()
        should_trade, trade_reason = self.phase_classifier.should_trade()
        
        logger.debug(f"Phase: {phase_signal.phase.value}, Should trade: {should_trade}")
        
        # Handle existing positions first
        position_actions = self._manage_existing_positions()
        actions.extend(position_actions)
        
        # CRITICAL: Enhanced trade flood prevention with mandatory cooldown
        time_since_last_trade = (current_time - self.last_trade_time).total_seconds()

        # Implement mandatory cooldown periods (minimum 60 seconds)
        min_cooldown = 60.0  # Absolute minimum 60 seconds between trades
        effective_cooldown = max(min_cooldown, self.min_trade_interval)

        can_trade_timing = time_since_last_trade >= effective_cooldown
        position_count = self._count_open_positions()
        can_trade_positions = position_count < self.max_concurrent_positions

        if not can_trade_timing:
            logger.debug(f"TRADE COOLDOWN: {time_since_last_trade:.1f}s < {effective_cooldown:.1f}s (mandatory: {min_cooldown}s)")
            # Count rapid trade attempts with enhanced tracking
            if not hasattr(self, 'rapid_trade_attempts'):
                self.rapid_trade_attempts = 0
                self.first_rapid_attempt_time = current_time.timestamp()

            self.rapid_trade_attempts += 1
            rapid_attempt_duration = current_time.timestamp() - self.first_rapid_attempt_time

            # Log warnings at appropriate intervals
            if self.rapid_trade_attempts % 50 == 0:
                logger.warning(f"TRADE COOLDOWN: {self.rapid_trade_attempts} attempts in {rapid_attempt_duration:.1f}s")

            # Emergency stop if too many rapid attempts or sustained pressure
            if self.rapid_trade_attempts > 1000 or (self.rapid_trade_attempts > 500 and rapid_attempt_duration > 300):
                logger.critical(f"TRADE FLOOD DETECTED - {self.rapid_trade_attempts} attempts in {rapid_attempt_duration:.1f}s")
                self._trigger_emergency_stop("TRADE_FLOOD_DETECTED")
        else:
            # Reset rapid trade attempts when timing is good
            if hasattr(self, 'rapid_trade_attempts') and self.rapid_trade_attempts > 0:
                logger.info(f"Trade cooldown satisfied: {time_since_last_trade:.1f}s >= {effective_cooldown:.1f}s")
                self.rapid_trade_attempts = 0

        if not can_trade_positions:
            logger.warning(f"Position limit reached: {position_count} >= {self.max_concurrent_positions}")

        # Only consider new entries if all conditions are met
        if (should_trade and
            can_trade_timing and
            can_trade_positions and
            self._can_switch_strategy(phase_signal.phase)):
            
            # Generate strategy-specific actions
            if phase_signal.phase == MarketPhase.CALM:
                calm_actions = self._process_calm_phase()
                actions.extend(calm_actions)
                
            elif phase_signal.phase == MarketPhase.SPIKE:
                spike_actions = self._process_spike_phase()
                actions.extend(spike_actions)
        
        # Update manager state
        self._update_manager_state(phase_signal.phase)
        
        # Log actions
        for action in actions:
            self._log_action(action)
            self.action_history.append(action)
        
        # Maintain history length
        if len(self.action_history) > self.max_history_length:
            self.action_history = self.action_history[-self.max_history_length:]
        
        return actions
    
    def _manage_existing_positions(self) -> List[StrategyAction]:
        """Manage existing positions across all strategies"""
        actions = []
        
        # Check calm strategy position
        if self.calm_strategy.current_position:
            calm_exit = self.calm_strategy.calm_exit_strategy()
            if calm_exit.signal_type != SignalType.NO_SIGNAL:
                action = StrategyAction(
                    action_type="CLOSE_POSITION",
                    strategy="CALM",
                    signal=calm_exit,
                    timestamp=datetime.now(),
                    reasoning=calm_exit.reasoning
                )
                actions.append(action)
        
        # Check spike strategy position
        if self.spike_strategy.current_position:
            spike_exit = self.spike_strategy.spike_exit_strategy()
            if spike_exit.signal_type != SpikeSignalType.NO_SIGNAL:
                action_type = "PARTIAL_CLOSE" if spike_exit.signal_type == SpikeSignalType.PARTIAL_EXIT else "CLOSE_POSITION"
                action = StrategyAction(
                    action_type=action_type,
                    strategy="SPIKE",
                    signal=spike_exit,
                    timestamp=datetime.now(),
                    reasoning=spike_exit.reasoning
                )
                actions.append(action)
        
        return actions
    
    def _process_calm_phase(self) -> List[StrategyAction]:
        """Process calm phase strategy logic"""
        actions = []
        
        # Check for entry signal
        entry_signal = self.calm_strategy.scalp_entry_signal()
        if entry_signal.signal_type == SignalType.ENTRY_LONG:
            action = StrategyAction(
                action_type="OPEN_POSITION",
                strategy="CALM",
                signal=entry_signal,
                timestamp=datetime.now(),
                reasoning=entry_signal.reasoning
            )
            actions.append(action)
        
        return actions
    
    def _process_spike_phase(self) -> List[StrategyAction]:
        """Process spike phase strategy logic"""
        actions = []
        
        # First check if spike conditions are detected
        spike_detected, analysis = self.spike_strategy.detect_spike_entry_conditions()
        
        if spike_detected:
            # Check for momentum fade entry
            entry_signal = self.spike_strategy.momentum_fade_entry()
            if entry_signal.signal_type == SpikeSignalType.ENTRY_SHORT:
                action = StrategyAction(
                    action_type="OPEN_POSITION",
                    strategy="SPIKE",
                    signal=entry_signal,
                    timestamp=datetime.now(),
                    reasoning=entry_signal.reasoning
                )
                actions.append(action)
        
        return actions
    
    def execute_action(self, action: StrategyAction) -> bool:
        """
        Execute a strategy action
        
        Returns:
            bool: True if action executed successfully
        """
        try:
            if action.strategy == "CALM":
                return self._execute_calm_action(action)
            elif action.strategy == "SPIKE":
                return self._execute_spike_action(action)
            else:
                logger.error(f"Unknown strategy: {action.strategy}")
                return False
                
        except Exception as e:
            logger.error(f"Error executing action: {e}")
            return False
    
    def _execute_calm_action(self, action: StrategyAction) -> bool:
        """Execute calm strategy action"""
        if action.action_type == "OPEN_POSITION":
            success = self.calm_strategy.open_position(action.signal)
            if success:
                self.total_trades += 1
                self.daily_trade_count += 1
                self.current_state = StrategyState.CALM_ACTIVE
                self.active_strategy = "CALM"
                # CRITICAL: Update last trade time for flood prevention
                self.last_trade_time = datetime.now()
                logger.info(f"Trade executed - next trade allowed after {self.min_trade_interval}s")
            return success
            
        elif action.action_type == "CLOSE_POSITION":
            success = self.calm_strategy.close_position(action.signal)
            if success:
                # Update P/L tracking
                if self.calm_strategy.position_history:
                    last_position = self.calm_strategy.position_history[-1]
                    self.total_pnl += last_position.current_pnl
                    self.daily_pnl += last_position.current_pnl
                
                self.current_state = StrategyState.IDLE
                self.active_strategy = None
            return success
        
        return False
    
    def _execute_spike_action(self, action: StrategyAction) -> bool:
        """Execute spike strategy action"""
        if action.action_type == "OPEN_POSITION":
            success = self.spike_strategy.open_position(action.signal)
            if success:
                self.total_trades += 1
                self.daily_trade_count += 1
                self.current_state = StrategyState.SPIKE_ACTIVE
                self.active_strategy = "SPIKE"
                # CRITICAL: Update last trade time for flood prevention
                self.last_trade_time = datetime.now()
                logger.info(f"Trade executed - next trade allowed after {self.min_trade_interval}s")
            return success
            
        elif action.action_type == "PARTIAL_CLOSE":
            success = self.spike_strategy.close_position(action.signal, partial=True)
            return success
            
        elif action.action_type == "CLOSE_POSITION":
            success = self.spike_strategy.close_position(action.signal, partial=False)
            if success:
                # Update P/L tracking
                if self.spike_strategy.position_history:
                    last_position = self.spike_strategy.position_history[-1]
                    self.total_pnl += last_position.current_pnl
                    self.daily_pnl += last_position.current_pnl
                
                self.current_state = StrategyState.IDLE
                self.active_strategy = None
            return success
        
        return False
    
    def _has_active_positions(self) -> bool:
        """Check if any strategy has active positions"""
        return (self.calm_strategy.current_position is not None or
                self.spike_strategy.current_position is not None)

    def _count_open_positions(self) -> int:
        """Count total open positions across all strategies"""
        count = 0
        if self.calm_strategy.current_position is not None:
            count += 1
        if self.spike_strategy.current_position is not None:
            count += 1
        return count

    def _trigger_emergency_stop(self, reason: str):
        """Trigger emergency system shutdown"""
        try:
            logger.critical(f"TRIGGERING EMERGENCY STOP: {reason}")
            # Set global emergency flag
            import os
            from datetime import datetime
            emergency_file = "EMERGENCY_STOP.flag"
            with open(emergency_file, "w") as f:
                f.write(f"EMERGENCY_STOP:{reason}:{datetime.now().isoformat()}")
            logger.critical(f"Emergency stop flag created: {emergency_file}")
        except Exception as e:
            logger.critical(f"Failed to create emergency stop flag: {str(e)}")
    
    def _can_switch_strategy(self, new_phase: MarketPhase) -> bool:
        """Check if strategy switching is allowed"""
        current_time = datetime.now()
        time_since_last_switch = (current_time - self.last_phase_change).total_seconds()
        
        # Allow immediate switching if no active positions
        if not self._has_active_positions():
            return True
        
        # Require cooldown period if switching with active positions
        return time_since_last_switch >= self.strategy_switch_cooldown
    
    def _check_daily_limits(self) -> bool:
        """Check if daily P/L limits are reached"""
        profit_limit = config.DAILY_PROFIT_LIMIT  # 0.02 (2%)
        loss_limit = config.DAILY_LOSS_LIMIT      # -0.02 (-2%)
        
        # For demo purposes, assume account balance of 10000
        account_balance = 10000
        daily_pnl_percent = self.daily_pnl / account_balance
        
        if daily_pnl_percent >= profit_limit:
            if not self.daily_limit_reached:
                logger.warning(f"Daily profit limit reached: {daily_pnl_percent:.2%}")
                self.daily_limit_reached = True
                self.current_state = StrategyState.RISK_HALT
            return True
            
        elif daily_pnl_percent <= loss_limit:
            if not self.daily_limit_reached:
                logger.warning(f"Daily loss limit reached: {daily_pnl_percent:.2%}")
                self.daily_limit_reached = True
                self.current_state = StrategyState.RISK_HALT
            return True
        
        return False
    
    def _check_daily_reset(self) -> None:
        """Reset daily counters if new day"""
        current_date = datetime.now().date()
        if current_date > self.last_daily_reset:
            self.daily_pnl = 0.0
            self.daily_trade_count = 0
            self.daily_limit_reached = False
            self.last_daily_reset = current_date
            logger.info("Daily counters reset")
    
    def _update_manager_state(self, current_phase: MarketPhase) -> None:
        """Update manager state based on current conditions"""
        if self.daily_limit_reached:
            self.current_state = StrategyState.RISK_HALT
        elif self._has_active_positions():
            self.current_state = StrategyState.POSITION_MANAGEMENT
        elif current_phase == MarketPhase.CALM:
            self.current_state = StrategyState.CALM_ACTIVE
        elif current_phase == MarketPhase.SPIKE:
            self.current_state = StrategyState.SPIKE_ACTIVE
        else:
            self.current_state = StrategyState.IDLE
    
    def _log_action(self, action: StrategyAction) -> None:
        """Log strategy action"""
        logger.info(f"ACTION: {action.action_type} | {action.strategy} | {action.reasoning}")
    
    def get_manager_summary(self) -> Dict[str, Any]:
        """Get comprehensive manager status summary"""
        calm_summary = self.calm_strategy.get_strategy_summary()
        spike_summary = self.spike_strategy.get_strategy_summary()
        phase_summary = self.phase_classifier.get_phase_summary()
        
        return {
            "manager_state": self.current_state.value,
            "active_strategy": self.active_strategy,
            "current_phase": phase_summary["current_phase"],
            "phase_confidence": phase_summary["confidence"],
            "total_trades": self.total_trades,
            "daily_trades": self.daily_trade_count,
            "total_pnl": self.total_pnl,
            "daily_pnl": self.daily_pnl,
            "daily_limit_reached": self.daily_limit_reached,
            "active_positions": {
                "calm": calm_summary["active_position"],
                "spike": spike_summary["active_position"]
            },
            "position_pnl": {
                "calm": calm_summary["position_pnl"],
                "spike": spike_summary["position_pnl"]
            },
            "strategy_performance": {
                "calm": {
                    "trades": calm_summary["total_trades"],
                    "win_rate": calm_summary["win_rate"],
                    "total_pnl": calm_summary["total_pnl"]
                },
                "spike": {
                    "trades": spike_summary["total_trades"],
                    "win_rate": spike_summary["win_rate"],
                    "total_pnl": spike_summary["total_pnl"]
                }
            },
            "recent_actions": len(self.action_history),
            "last_action": self.action_history[-1].action_type if self.action_history else None
        }
    
    def force_close_all_positions(self, reason: str = "Manual close") -> List[StrategyAction]:
        """Force close all active positions"""
        actions = []
        current_time = datetime.now()
        
        if self.calm_strategy.current_position:
            # Create manual exit signal
            current_price = list(self.data_handler.price_buffer)[-1] if self.data_handler.price_buffer else 0
            exit_signal = TradingSignal(
                signal_type=SignalType.EXIT_LOSS,
                price=current_price,
                timestamp=current_time,
                confidence=1.0,
                reasoning=reason
            )
            
            action = StrategyAction(
                action_type="CLOSE_POSITION",
                strategy="CALM",
                signal=exit_signal,
                timestamp=current_time,
                reasoning=reason
            )
            actions.append(action)
        
        if self.spike_strategy.current_position:
            # Create manual exit signal
            current_price = list(self.data_handler.price_buffer)[-1] if self.data_handler.price_buffer else 0
            exit_signal = SpikeSignal(
                signal_type=SpikeSignalType.STOP_LOSS,
                price=current_price,
                timestamp=current_time,
                confidence=1.0,
                reasoning=reason
            )
            
            action = StrategyAction(
                action_type="CLOSE_POSITION",
                strategy="SPIKE",
                signal=exit_signal,
                timestamp=current_time,
                reasoning=reason
            )
            actions.append(action)
        
        return actions
