#!/usr/bin/env python3
"""
Test script to verify Take Profit / Stop Loss fix
"""

import sys
import os
import logging
from datetime import datetime

# Add project root to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.strategies.calm_strategy import CalmPhaseStrategy, TradingSignal, SignalType
from src.strategies.strategy_manager import StrategyAction
from src.execution.trading_executor import TradeSignal
from src.modules.data_feed import DataHandler
from config.config import config

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_tp_sl_signal_flow():
    """Test the complete TP/SL signal flow from strategy to execution"""
    
    print("🔧 TESTING TAKE PROFIT / STOP LOSS FIX")
    print("=" * 50)
    
    # 1. Create a mock calm strategy signal with TP/SL
    print("1. Creating mock strategy signal with TP/SL...")
    
    test_signal = TradingSignal(
        signal_type=SignalType.ENTRY_LONG,
        price=65600.0,
        timestamp=datetime.now(),
        confidence=0.85,
        reasoning="Test BB + RSI signal",
        stop_loss=65300.0,  # 300 points SL
        take_profit=65700.0,  # 100 points TP
        position_size=0.05
    )
    
    print(f"   ✅ Original signal created:")
    print(f"      Price: {test_signal.price}")
    print(f"      Stop Loss: {test_signal.stop_loss}")
    print(f"      Take Profit: {test_signal.take_profit}")
    print(f"      Position Size: {test_signal.position_size}")
    
    # 2. Create StrategyAction (as strategy manager would)
    print("\n2. Creating StrategyAction...")
    
    strategy_action = StrategyAction(
        action_type="OPEN_POSITION",
        strategy="CALM",
        signal=test_signal,
        timestamp=datetime.now(),
        reasoning=test_signal.reasoning
    )
    
    print(f"   ✅ StrategyAction created with signal")
    
    # 3. Test the conversion logic (as main controller would)
    print("\n3. Testing signal conversion (main controller logic)...")
    
    # Extract values from the original strategy signal
    original_signal = strategy_action.signal
    
    # Map action types based on signal type
    action_map = {
        'OPEN_POSITION': 'BUY',
        'CLOSE_POSITION': 'CLOSE',
        'MODIFY_POSITION': 'MODIFY'
    }
    
    action = action_map.get(strategy_action.action_type, 'BUY')
    if hasattr(original_signal, 'signal_type'):
        if hasattr(SignalType, 'ENTRY_LONG') and original_signal.signal_type == SignalType.ENTRY_LONG:
            action = 'BUY'
        elif hasattr(SignalType, 'ENTRY_SHORT') and original_signal.signal_type == SignalType.ENTRY_SHORT:
            action = 'SELL'

    # Extract TP/SL values
    stop_loss_value = getattr(original_signal, 'stop_loss', None)
    take_profit_value = getattr(original_signal, 'take_profit', None)
    
    trade_signal = TradeSignal(
        strategy=strategy_action.strategy,
        action=action,
        symbol=config.SYMBOL,
        volume=getattr(original_signal, 'position_size', 0.01),
        entry_price=getattr(original_signal, 'price', None),
        stop_loss=stop_loss_value,
        take_profit=take_profit_value,
        confidence=getattr(original_signal, 'confidence', 0.8),
        reasoning=getattr(original_signal, 'reasoning', strategy_action.reasoning)
    )
    
    print(f"   ✅ TradeSignal converted:")
    print(f"      Strategy: {trade_signal.strategy}")
    print(f"      Action: {trade_signal.action}")
    print(f"      Symbol: {trade_signal.symbol}")
    print(f"      Volume: {trade_signal.volume}")
    print(f"      Entry Price: {trade_signal.entry_price}")
    print(f"      Stop Loss: {trade_signal.stop_loss}")
    print(f"      Take Profit: {trade_signal.take_profit}")
    print(f"      Confidence: {trade_signal.confidence}")
    
    # 4. Verify the fix
    print("\n4. Verifying the fix...")
    
    success = True
    issues = []
    
    if trade_signal.stop_loss is None:
        success = False
        issues.append("Stop Loss is None")
    elif trade_signal.stop_loss != test_signal.stop_loss:
        success = False
        issues.append(f"Stop Loss mismatch: {trade_signal.stop_loss} != {test_signal.stop_loss}")
    
    if trade_signal.take_profit is None:
        success = False
        issues.append("Take Profit is None")
    elif trade_signal.take_profit != test_signal.take_profit:
        success = False
        issues.append(f"Take Profit mismatch: {trade_signal.take_profit} != {test_signal.take_profit}")
    
    if trade_signal.volume != test_signal.position_size:
        success = False
        issues.append(f"Volume mismatch: {trade_signal.volume} != {test_signal.position_size}")
    
    if trade_signal.entry_price != test_signal.price:
        success = False
        issues.append(f"Entry price mismatch: {trade_signal.entry_price} != {test_signal.price}")
    
    print("=" * 50)
    if success:
        print("🎉 SUCCESS: Take Profit / Stop Loss fix is working!")
        print("   ✅ All TP/SL values properly transferred")
        print("   ✅ Volume correctly mapped from position_size")
        print("   ✅ Entry price correctly mapped")
        print("   ✅ Signal conversion working perfectly")
    else:
        print("❌ FAILURE: Issues found with TP/SL fix:")
        for issue in issues:
            print(f"   ❌ {issue}")
    
    print("=" * 50)
    return success

def test_order_executor_integration():
    """Test that order executor will receive TP/SL correctly"""
    print("\n🔧 TESTING ORDER EXECUTOR INTEGRATION")
    print("=" * 50)
    
    # Create a TradeSignal with TP/SL
    trade_signal = TradeSignal(
        strategy="CALM",
        action="BUY",
        symbol=config.SYMBOL,
        volume=0.05,
        entry_price=65600.0,
        stop_loss=65300.0,
        take_profit=65700.0,
        confidence=0.85,
        reasoning="Test signal with TP/SL"
    )
    
    print(f"   📊 Test TradeSignal:")
    print(f"      Stop Loss: {trade_signal.stop_loss}")
    print(f"      Take Profit: {trade_signal.take_profit}")
    
    # Simulate what order executor would do
    if trade_signal.stop_loss is not None and trade_signal.stop_loss > 0:
        print(f"   ✅ Order executor would set SL: {trade_signal.stop_loss}")
    else:
        print(f"   ❌ Order executor would warn: No stop loss specified")
        
    if trade_signal.take_profit is not None and trade_signal.take_profit > 0:
        print(f"   ✅ Order executor would set TP: {trade_signal.take_profit}")
    else:
        print(f"   ❌ Order executor would warn: No take profit specified")
    
    print("=" * 50)

if __name__ == "__main__":
    print("\n🔧 TAKE PROFIT / STOP LOSS FIX VERIFICATION")
    print("=" * 60)
    
    # Test 1: Signal flow
    success1 = test_tp_sl_signal_flow()
    
    # Test 2: Order executor integration
    test_order_executor_integration()
    
    print(f"\n🎯 OVERALL RESULT: {'SUCCESS' if success1 else 'NEEDS ATTENTION'}")
    print("=" * 60)
