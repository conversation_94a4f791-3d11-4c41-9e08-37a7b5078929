"""
Data Handler for DEX900DN Trading System
Manages tick data, volatility calculation, and volume spike detection
"""

import logging
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional, Tuple
from collections import deque
from dataclasses import dataclass
import MetaTrader5 as mt5

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))
from config.config import config

# Setup logging
logging.basicConfig(level=getattr(logging, config.LOG_LEVEL))
logger = logging.getLogger(__name__)

class DataIntegrityError(Exception):
    """Raised when data integrity checks fail"""
    pass

class CycleSyncError(Exception):
    """Raised when cycle synchronization fails"""
    pass

@dataclass
class TickData:
    """Data class for individual tick information"""
    timestamp: datetime
    bid: float
    ask: float
    volume: float
    spread: float
    
    @property
    def mid_price(self) -> float:
        """Calculate mid price"""
        return (self.bid + self.ask) / 2

@dataclass
class MarketState:
    """Current market state information"""
    current_price: float
    volatility_5min: float
    volume_baseline: float
    volume_spike_detected: bool
    price_change_10s: float
    last_update: datetime

class DataHandler:
    """
    Handles all data operations for DEX900DN trading system
    
    Key Functions:
    - fetch_tick_data(): Pulls 1-sec resolution DEX900DN data via MT5
    - calculate_volatility(window=5): Volatility (std dev) over last 5 mins
    - detect_volume_spike(): Flags volume > 200% of 30-min average
    """
    
    def __init__(self, symbol: str = None):
        self.symbol = symbol or config.SYMBOL
        self.tick_buffer = deque(maxlen=config.LIVE_DATA_BUFFER_SIZE)
        self.price_buffer = deque(maxlen=3600)  # 1 hour of 1-second data
        self.volume_buffer = deque(maxlen=1800)  # 30 minutes for volume baseline
        
        self.market_state = MarketState(
            current_price=0.0,
            volatility_5min=0.0,
            volume_baseline=0.0,
            volume_spike_detected=False,
            price_change_10s=0.0,
            last_update=datetime.now()
        )
        
        # Initialize MT5 connection
        self._initialize_mt5()
        
        logger.info(f"DataHandler initialized for {self.symbol}")
    
    def _initialize_mt5(self) -> bool:
        """Initialize MetaTrader 5 connection"""
        try:
            if not mt5.initialize():
                logger.error(f"MT5 initialization failed: {mt5.last_error()}")
                return False
            
            # Check if symbol is available
            symbol_info = mt5.symbol_info(self.symbol)
            if symbol_info is None:
                logger.error(f"Symbol {self.symbol} not found")
                return False
            
            # Enable symbol if not visible
            if not symbol_info.visible:
                if not mt5.symbol_select(self.symbol, True):
                    logger.error(f"Failed to select symbol {self.symbol}")
                    return False
            
            logger.info(f"MT5 connected successfully for {self.symbol}")
            return True
            
        except Exception as e:
            logger.error(f"MT5 initialization error: {e}")
            return False
    
    def fetch_tick_data(self, count: int = 1) -> List[TickData]:
        """
        Pulls 1-sec resolution DEX900DN data via MT5
        
        Args:
            count: Number of recent ticks to fetch
            
        Returns:
            List of TickData objects
        """
        try:
            # Get latest ticks
            ticks = mt5.copy_ticks_from(self.symbol, datetime.now(), count, mt5.COPY_TICKS_ALL)
            
            if ticks is None or len(ticks) == 0:
                logger.warning(f"No tick data received for {self.symbol}")
                return []
            
            tick_data_list = []
            for tick in ticks:
                # Handle numpy array structure
                tick_time = tick['time'] if hasattr(tick, '__getitem__') else tick.time
                tick_bid = tick['bid'] if hasattr(tick, '__getitem__') else tick.bid
                tick_ask = tick['ask'] if hasattr(tick, '__getitem__') else tick.ask
                tick_volume = tick['volume'] if hasattr(tick, '__getitem__') else tick.volume

                tick_data = TickData(
                    timestamp=datetime.fromtimestamp(tick_time),
                    bid=tick_bid,
                    ask=tick_ask,
                    volume=tick_volume,
                    spread=tick_ask - tick_bid
                )
                tick_data_list.append(tick_data)
                
                # Add to buffer
                self.tick_buffer.append(tick_data)
                self.price_buffer.append(tick_data.mid_price)
                self.volume_buffer.append(tick_data.volume)
            
            # Update market state
            if tick_data_list:
                self._update_market_state(tick_data_list[-1])
            
            return tick_data_list
            
        except Exception as e:
            logger.error(f"Error fetching tick data: {e}")
            return []
    
    def calculate_volatility(self, window: int = 5) -> float:
        """
        Calculate volatility (standard deviation) over specified window in minutes
        
        Args:
            window: Time window in minutes (default 5)
            
        Returns:
            float: Volatility as standard deviation of returns
        """
        if len(self.price_buffer) < 2:
            return 0.0
        
        # Convert window from minutes to number of data points (1-second data)
        window_size = window * 60
        
        # Get recent prices
        recent_prices = list(self.price_buffer)[-window_size:]
        
        if len(recent_prices) < 2:
            return 0.0
        
        # Calculate returns
        prices = np.array(recent_prices)
        returns = np.diff(prices) / prices[:-1]
        
        # Calculate volatility (standard deviation)
        volatility = np.std(returns)
        
        logger.debug(f"Volatility ({window}min): {volatility:.6f}")
        return volatility
    
    def detect_volume_spike(self) -> bool:
        """
        Detects volume spike > 200% of 30-minute average
        
        Returns:
            bool: True if volume spike detected
        """
        if len(self.volume_buffer) < 10:  # Need minimum data
            return False
        
        # Calculate 30-minute volume baseline
        volume_array = np.array(list(self.volume_buffer))
        baseline_volume = np.mean(volume_array)
        
        # Get current volume (latest tick)
        if not self.tick_buffer:
            return False
        
        current_volume = self.tick_buffer[-1].volume
        
        # Check for spike (>200% of baseline)
        spike_threshold = baseline_volume * config.VOLUME_SPIKE_MULTIPLIER
        volume_spike = current_volume > spike_threshold
        
        if volume_spike:
            logger.warning(f"VOLUME SPIKE DETECTED: {current_volume:.2f} > {spike_threshold:.2f}")

        return volume_spike

    def detect_volume_spike_detailed(self) -> tuple:
        """Detect volume spikes with detailed information for logging"""
        if len(self.volume_buffer) < 10:
            return False, 0, 0

        volume_array = np.array(list(self.volume_buffer))
        baseline_volume = np.mean(volume_array)

        if not self.tick_buffer:
            return False, 0, baseline_volume

        current_volume = self.tick_buffer[-1].volume
        spike_threshold = baseline_volume * config.VOLUME_SPIKE_MULTIPLIER
        volume_spike = current_volume > spike_threshold

        return volume_spike, current_volume, baseline_volume

    def validate_indicators(self, volatility: float, rsi: float, cycle_time: int = None) -> None:
        """Validate indicator values for data integrity"""

        # Check for invalid volatility
        if volatility == 0.0:
            raise DataIntegrityError("Invalid volatility: 0.0000 indicates data feed failure")

        if volatility < 0 or volatility > 1.0:  # Volatility should be 0-100%
            raise DataIntegrityError(f"Invalid volatility: {volatility:.4f} outside valid range [0, 1.0]")

        # Check for invalid RSI
        if rsi >= 100.0:
            raise DataIntegrityError(f"Invalid RSI: {rsi:.1f} is mathematically impossible (max 99.99)")

        if rsi < 0 or rsi > 100:
            raise DataIntegrityError(f"Invalid RSI: {rsi:.1f} outside valid range [0, 100]")

        # Check cycle synchronization if provided
        if cycle_time is not None:
            if not (840 <= cycle_time <= 960):
                raise CycleSyncError(f"Cycle desynchronization: {cycle_time}s outside spike window [840, 960]")

        logger.debug(f"✅ Data validation passed: volatility={volatility:.4f}, RSI={rsi:.1f}")

    def get_validated_market_state(self) -> MarketState:
        """Get market state with data validation"""
        try:
            # Get raw market state
            market_state = self.get_market_state()

            # Validate the data
            if market_state:
                # Calculate RSI for validation (simplified check)
                rsi_value = 50.0  # Default safe value if calculation fails
                try:
                    if len(self.price_buffer) >= 14:
                        prices = np.array(list(self.price_buffer))
                        deltas = np.diff(prices)
                        gains = np.where(deltas > 0, deltas, 0)
                        losses = np.where(deltas < 0, -deltas, 0)
                        avg_gain = np.mean(gains[-14:]) if len(gains) >= 14 else 0
                        avg_loss = np.mean(losses[-14:]) if len(losses) >= 14 else 0
                        if avg_loss > 0:
                            rs = avg_gain / avg_loss
                            rsi_value = 100 - (100 / (1 + rs))
                except:
                    rsi_value = 50.0  # Safe default

                # Validate indicators
                self.validate_indicators(
                    volatility=market_state.volatility_5min,
                    rsi=rsi_value
                )

            return market_state

        except (DataIntegrityError, CycleSyncError) as e:
            logger.error(f"🚨 Data validation failed: {str(e)}")
            raise
    
    def detect_price_drop(self, drop_threshold: int = None, time_window: int = None) -> Tuple[bool, float]:
        """
        Detects significant price drops within specified time window
        
        Args:
            drop_threshold: Minimum drop in points (default from config)
            time_window: Time window in seconds (default from config)
            
        Returns:
            Tuple[bool, float]: (spike_detected, actual_drop)
        """
        drop_threshold = drop_threshold or config.MIN_SPIKE_DROP
        time_window = time_window or config.SPIKE_TIME_WINDOW
        
        if len(self.price_buffer) < time_window:
            return False, 0.0
        
        # Get prices from the last time_window seconds
        recent_prices = list(self.price_buffer)[-time_window:]
        
        if len(recent_prices) < 2:
            return False, 0.0
        
        # Calculate maximum drop in the window
        max_price = max(recent_prices)
        min_price = min(recent_prices)
        price_drop = max_price - min_price
        
        spike_detected = price_drop >= drop_threshold
        
        if spike_detected:
            logger.warning(f"PRICE SPIKE DETECTED: {price_drop:.1f} points drop in {time_window}s")
        
        return spike_detected, price_drop
    
    def _update_market_state(self, latest_tick: TickData) -> None:
        """Update the current market state with latest data"""
        self.market_state.current_price = latest_tick.mid_price
        self.market_state.volatility_5min = self.calculate_volatility(5)
        self.market_state.volume_spike_detected = self.detect_volume_spike()
        self.market_state.last_update = latest_tick.timestamp
        
        # Calculate 10-second price change
        if len(self.price_buffer) >= 10:
            price_10s_ago = list(self.price_buffer)[-10]
            self.market_state.price_change_10s = latest_tick.mid_price - price_10s_ago
        
        # Update volume baseline
        if self.volume_buffer:
            self.market_state.volume_baseline = np.mean(list(self.volume_buffer))
    
    def get_latest_data(self) -> Dict[str, Any]:
        """
        Fetch latest tick data and return updated market summary

        Returns:
            Dict containing current market information with fresh data
        """
        # Fetch latest tick data
        latest_ticks = self.fetch_tick_data(1)

        if latest_ticks:
            # Update market state with latest data
            self._update_market_state(latest_ticks[0])

        # Return updated market summary
        return self.get_market_summary()

    def get_market_summary(self) -> Dict[str, Any]:
        """
        Returns comprehensive market state summary

        Returns:
            Dict containing current market information
        """
        return {
            "symbol": self.symbol,
            "current_price": self.market_state.current_price,
            "volatility_5min": self.market_state.volatility_5min,
            "volume_baseline": self.market_state.volume_baseline,
            "volume_spike_detected": self.market_state.volume_spike_detected,
            "price_change_10s": self.market_state.price_change_10s,
            "last_update": self.market_state.last_update,
            "timestamp": self.market_state.last_update,  # Add timestamp for compatibility
            "data_points": {
                "tick_buffer_size": len(self.tick_buffer),
                "price_buffer_size": len(self.price_buffer),
                "volume_buffer_size": len(self.volume_buffer)
            }
        }
    
    def save_historical_data(self, filename: str = None) -> bool:
        """Save current buffer data to CSV file"""
        filename = filename or config.HISTORICAL_DATA_FILE
        
        try:
            if not self.tick_buffer:
                logger.warning("No data to save")
                return False
            
            # Convert tick buffer to DataFrame
            data = []
            for tick in self.tick_buffer:
                data.append({
                    'timestamp': tick.timestamp,
                    'bid': tick.bid,
                    'ask': tick.ask,
                    'mid_price': tick.mid_price,
                    'volume': tick.volume,
                    'spread': tick.spread
                })
            
            df = pd.DataFrame(data)
            df.to_csv(filename, index=False)
            
            logger.info(f"Saved {len(data)} data points to {filename}")
            return True
            
        except Exception as e:
            logger.error(f"Error saving historical data: {e}")
            return False
    
    def cleanup(self) -> None:
        """Cleanup resources"""
        try:
            mt5.shutdown()
            logger.info("MT5 connection closed")
        except Exception as e:
            logger.error(f"Error during cleanup: {e}")

# Global data handler instance
data_handler = DataHandler()
