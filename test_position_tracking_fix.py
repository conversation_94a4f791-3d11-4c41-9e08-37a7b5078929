#!/usr/bin/env python3
"""
Test script to verify position tracking fix
Tests the corrected position counting logic
"""

import sys
import os
import logging
from datetime import datetime

# Add project root to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.strategies.strategy_manager import StrategyManager
from src.execution.order_executor import OrderExecutor
from src.execution.position_manager import PositionManager
from src.execution.mt5_connection import mt5_connection
from config.config import config

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_position_counting():
    """Test the corrected position counting logic"""
    
    print("🔧 TESTING POSITION TRACKING FIX")
    print("=" * 50)
    
    try:
        # Test 1: Strategy Manager position counting
        print("1. Testing Strategy Manager position counting...")
        strategy_manager = StrategyManager()
        
        # Get position count using new method
        position_count = strategy_manager._count_open_positions()
        print(f"   📊 Strategy Manager reports: {position_count} open positions")
        
        # Test 2: Direct MT5 position query
        print("\n2. Testing direct MT5 position query...")
        order_executor = OrderExecutor()
        mt5_positions = order_executor.get_positions()
        print(f"   📊 Direct MT5 query reports: {len(mt5_positions)} open positions")
        
        # Test 3: Position Manager tracking
        print("\n3. Testing Position Manager tracking...")
        position_manager = PositionManager()
        managed_positions = position_manager.get_positions()
        print(f"   📊 Position Manager tracks: {len(managed_positions)} positions")
        
        # Test 4: Detailed position information
        print("\n4. Detailed position information...")
        if mt5_positions:
            print("   📋 Current MT5 positions:")
            for i, pos in enumerate(mt5_positions, 1):
                print(f"      {i}. Ticket: {pos.ticket}, Magic: {pos.magic}, Comment: {pos.comment}")
                print(f"         Volume: {pos.volume}, Type: {pos.type}, Profit: {pos.profit:.2f}")
        else:
            print("   ✅ No open positions found")
        
        # Test 5: Magic number filtering
        print(f"\n5. Testing magic number filtering (Magic: {config.MAGIC_NUMBER})...")
        if mt5_connection.connect():
            import MetaTrader5 as mt5
            all_positions = mt5.positions_get()
            if all_positions:
                our_positions = [pos for pos in all_positions if pos.magic == config.MAGIC_NUMBER]
                other_positions = [pos for pos in all_positions if pos.magic != config.MAGIC_NUMBER]
                print(f"   📊 Total positions: {len(all_positions)}")
                print(f"   📊 Our positions (magic {config.MAGIC_NUMBER}): {len(our_positions)}")
                print(f"   📊 Other positions: {len(other_positions)}")
            else:
                print("   ✅ No positions found in MT5")
        
        # Test 6: Consistency check
        print("\n6. Consistency check...")
        counts = {
            'strategy_manager': position_count,
            'direct_mt5': len(mt5_positions),
            'position_manager': len(managed_positions)
        }
        
        print(f"   📊 Count comparison:")
        for method, count in counts.items():
            print(f"      {method}: {count}")
        
        # Check if counts are consistent
        unique_counts = set(counts.values())
        if len(unique_counts) == 1:
            print("   ✅ All counting methods agree!")
            return True
        else:
            print("   ⚠️ Counting methods disagree - this indicates tracking issues")
            return False
            
    except Exception as e:
        print(f"   ❌ Error during testing: {e}")
        return False

def test_comment_uniqueness():
    """Test that comments are unique for different trades"""
    print("\n🔧 TESTING COMMENT UNIQUENESS")
    print("=" * 50)
    
    try:
        # Simulate creating multiple comments
        from datetime import datetime
        
        comments = []
        for i in range(5):
            timestamp = datetime.now().strftime("%H%M%S")
            comment = f"DEX900DN CALM BUY {timestamp}"
            comments.append(comment)
            print(f"   Comment {i+1}: {comment}")
            # Small delay to ensure different timestamps
            import time
            time.sleep(0.1)
        
        # Check uniqueness
        unique_comments = set(comments)
        if len(unique_comments) == len(comments):
            print("   ✅ All comments are unique!")
            return True
        else:
            print(f"   ⚠️ Some comments are duplicated: {len(unique_comments)}/{len(comments)} unique")
            return False
            
    except Exception as e:
        print(f"   ❌ Error testing comments: {e}")
        return False

def main():
    """Main test execution"""
    print("\n" + "="*60)
    print("🔧 POSITION TRACKING FIX VERIFICATION")
    print("="*60)
    
    # Test 1: Position counting
    test1_result = test_position_counting()
    
    # Test 2: Comment uniqueness
    test2_result = test_comment_uniqueness()
    
    # Summary
    print("\n" + "="*60)
    print("📊 TEST SUMMARY")
    print("="*60)
    
    results = {
        'Position Counting Fix': test1_result,
        'Comment Uniqueness': test2_result
    }
    
    passed = sum(results.values())
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
    
    print("-" * 60)
    print(f"📊 OVERALL RESULT: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 ALL TESTS PASSED - Position tracking fix should work!")
    else:
        print("⚠️ Some tests failed - review the issues above")
    
    print("="*60)

if __name__ == "__main__":
    main()
