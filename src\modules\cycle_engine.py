"""
Time-Cycle Tracker for DEX900DN Trading System
Manages the 900-second spike cycles and timing windows
"""

import time
import logging
from datetime import datetime, timedelta
from typing import Optional, Dict, Any
from dataclasses import dataclass

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))
from config.config import config

# Setup logging
logging.basicConfig(level=getattr(logging, config.LOG_LEVEL))
logger = logging.getLogger(__name__)

@dataclass
class CycleState:
    """Data class to hold cycle state information"""
    cycle_start_time: Optional[datetime] = None
    last_spike_time: Optional[datetime] = None
    current_cycle_duration: float = 0.0
    is_in_spike_window: bool = False
    cycle_count: int = 0
    volatility_peak: float = 0.0

class TimeCycleTracker:
    """
    Manages the time-cycle logic for DEX900DN trading system
    
    Key Functions:
    - start_spike_countdown(): Initiates 900-sec timer after last spike
    - spike_window_alert(): Returns True at 14m-16m of cycle 
    - reset_cycle(): Activates after volatility drops to <10% of spike peak
    """
    
    def __init__(self):
        self.state = CycleState()
        self.cycle_duration = config.SPIKE_CYCLE_DURATION  # 900 seconds
        self.spike_window_start = config.SPIKE_WINDOW_START  # 840 seconds (14 min)
        self.spike_window_end = config.SPIKE_WINDOW_END  # 960 seconds (16 min)
        
        logger.info(f"TimeCycleTracker initialized with {self.cycle_duration}s cycles")
        logger.info(f"Spike window: {self.spike_window_start}s - {self.spike_window_end}s")

    def update(self, timestamp: datetime = None) -> None:
        """
        Update the cycle tracker with current timestamp

        Args:
            timestamp: Current timestamp (defaults to now)
        """
        if timestamp is None:
            timestamp = datetime.now()

        # Update current cycle duration if cycle is active
        if self.state.cycle_start_time:
            self.state.current_cycle_duration = (timestamp - self.state.cycle_start_time).total_seconds()

        # Check if we need to start a new cycle
        if self.state.cycle_start_time is None:
            self.start_spike_countdown(timestamp)

    def start_spike_countdown(self, spike_time: Optional[datetime] = None) -> bool:
        """
        Initiates 900-second timer after last spike
        
        Args:
            spike_time: Time of the spike (defaults to current time)
            
        Returns:
            bool: True if countdown started successfully
        """
        if spike_time is None:
            spike_time = datetime.now()
        
        self.state.last_spike_time = spike_time
        self.state.cycle_start_time = spike_time
        self.state.current_cycle_duration = 0.0
        self.state.cycle_count += 1
        self.state.is_in_spike_window = False
        
        logger.info(f"Spike countdown started at {spike_time}")
        logger.info(f"Cycle #{self.state.cycle_count} initiated")
        
        return True
    
    def spike_window_alert(self) -> bool:
        """
        Returns True when in the 14m-16m spike window of current cycle
        
        Returns:
            bool: True if currently in spike alert window
        """
        if self.state.cycle_start_time is None:
            return False
        
        current_time = datetime.now()
        elapsed_seconds = (current_time - self.state.cycle_start_time).total_seconds()
        self.state.current_cycle_duration = elapsed_seconds
        
        # Check if we're in the spike window (14-16 minutes)
        in_window = (self.spike_window_start <= elapsed_seconds <= self.spike_window_end)
        
        # Log window entry/exit
        if in_window and not self.state.is_in_spike_window:
            logger.warning(f"ENTERING SPIKE WINDOW at {elapsed_seconds:.1f}s into cycle")
            self.state.is_in_spike_window = True
        elif not in_window and self.state.is_in_spike_window:
            logger.info(f"Exiting spike window at {elapsed_seconds:.1f}s into cycle")
            self.state.is_in_spike_window = False
        
        return in_window
    
    def reset_cycle(self, current_volatility: float) -> bool:
        """
        Resets cycle when volatility drops to <10% of spike peak
        
        Args:
            current_volatility: Current market volatility
            
        Returns:
            bool: True if cycle was reset
        """
        if self.state.volatility_peak == 0:
            return False
        
        # Check if volatility has dropped to <10% of peak
        volatility_threshold = self.state.volatility_peak * 0.1
        
        if current_volatility < volatility_threshold:
            logger.info(f"Cycle reset triggered: volatility {current_volatility:.4f} < {volatility_threshold:.4f}")
            logger.info(f"Previous cycle duration: {self.state.current_cycle_duration:.1f}s")
            
            # Reset cycle state
            self.state.cycle_start_time = None
            self.state.last_spike_time = None
            self.state.current_cycle_duration = 0.0
            self.state.is_in_spike_window = False
            self.state.volatility_peak = 0.0
            
            return True
        
        return False
    
    def update_volatility_peak(self, volatility: float) -> None:
        """
        Updates the peak volatility for the current cycle
        
        Args:
            volatility: Current volatility reading
        """
        if volatility > self.state.volatility_peak:
            self.state.volatility_peak = volatility
            logger.debug(f"New volatility peak: {volatility:.4f}")
    
    def get_cycle_progress(self) -> Dict[str, Any]:
        """
        Returns current cycle progress information
        
        Returns:
            Dict containing cycle progress data
        """
        if self.state.cycle_start_time is None:
            return {
                "cycle_active": False,
                "progress_percent": 0.0,
                "time_to_spike_window": None,
                "in_spike_window": False
            }
        
        current_time = datetime.now()
        elapsed = (current_time - self.state.cycle_start_time).total_seconds()
        progress_percent = (elapsed / self.cycle_duration) * 100
        
        # Calculate time to spike window
        time_to_window = None
        if elapsed < self.spike_window_start:
            time_to_window = self.spike_window_start - elapsed
        
        return {
            "cycle_active": True,
            "cycle_number": self.state.cycle_count,
            "elapsed_seconds": elapsed,
            "progress_percent": min(progress_percent, 100.0),
            "time_to_spike_window": time_to_window,
            "in_spike_window": self.state.is_in_spike_window,
            "volatility_peak": self.state.volatility_peak,
            "last_spike_time": self.state.last_spike_time
        }
    
    def is_cycle_complete(self) -> bool:
        """
        Checks if current cycle has completed (>900 seconds)
        
        Returns:
            bool: True if cycle is complete
        """
        if self.state.cycle_start_time is None:
            return False
        
        elapsed = (datetime.now() - self.state.cycle_start_time).total_seconds()
        return elapsed >= self.cycle_duration
    
    def force_reset(self) -> None:
        """Force reset the cycle (for testing or manual intervention)"""
        logger.warning("Force resetting cycle")
        self.state = CycleState()
    
    def get_state_summary(self) -> str:
        """Returns a human-readable summary of current state"""
        if self.state.cycle_start_time is None:
            return "No active cycle"
        
        progress = self.get_cycle_progress()
        return (f"Cycle #{progress['cycle_number']}: "
                f"{progress['elapsed_seconds']:.1f}s elapsed "
                f"({progress['progress_percent']:.1f}%) - "
                f"{'IN SPIKE WINDOW' if progress['in_spike_window'] else 'Normal'}")

# Global cycle tracker instance
cycle_tracker = TimeCycleTracker()
