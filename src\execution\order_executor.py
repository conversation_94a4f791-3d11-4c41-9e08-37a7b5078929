"""
Order Execution Engine for DEX900DN Trading System
Handles order placement, modification, and closing with MT5 integration
"""

import logging
import time
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List, Tuple
from dataclasses import dataclass
from enum import Enum

try:
    import MetaTrader5 as mt5
    MT5_AVAILABLE = True
except ImportError:
    MT5_AVAILABLE = False
    logging.warning("MetaTrader5 module not available - running in simulation mode")

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))
from config.config import config
from src.execution.mt5_connection import mt5_connection, ConnectionStatus

# Setup logging
logging.basicConfig(level=getattr(logging, config.LOG_LEVEL))
logger = logging.getLogger(__name__)

class OrderType(Enum):
    """Order types"""
    BUY = "BUY"
    SELL = "SELL"
    BUY_LIMIT = "BUY_LIMIT"
    SELL_LIMIT = "SELL_LIMIT"
    BUY_STOP = "BUY_STOP"
    SELL_STOP = "SELL_STOP"

class OrderStatus(Enum):
    """Order status"""
    PENDING = "PENDING"
    FILLED = "FILLED"
    CANCELLED = "CANCELLED"
    REJECTED = "REJECTED"
    PARTIAL = "PARTIAL"
    ERROR = "ERROR"

@dataclass
class OrderRequest:
    """Order request structure"""
    action: str  # TRADE_ACTION_DEAL, TRADE_ACTION_PENDING, etc.
    symbol: str
    volume: float
    type: int  # ORDER_TYPE_BUY, ORDER_TYPE_SELL, etc.
    price: Optional[float] = None
    stoploss: Optional[float] = None
    takeprofit: Optional[float] = None
    deviation: int = 20
    magic: int = 0
    comment: str = "DEX900DN"
    type_time: int = None  # ORDER_TIME_GTC
    expiration: int = 0
    type_filling: int = None  # ORDER_FILLING_IOC

@dataclass
class OrderResult:
    """Order execution result"""
    success: bool
    order_id: Optional[int] = None
    position_id: Optional[int] = None
    deal_id: Optional[int] = None
    volume: float = 0.0
    price: float = 0.0
    error_code: int = 0
    error_description: str = ""
    comment: str = ""
    request_id: int = 0
    retcode: int = 0
    retcode_external: int = 0

@dataclass
class Position:
    """Position information"""
    ticket: int
    time: int
    time_msc: int
    time_update: int
    time_update_msc: int
    type: int
    magic: int
    identifier: int
    reason: int
    volume: float
    price_open: float
    sl: float
    tp: float
    price_current: float
    swap: float
    profit: float
    symbol: str
    comment: str
    external_id: str

class OrderExecutor:
    """
    Order Execution Engine for DEX900DN Trading System
    
    Features:
    - Market and pending order execution
    - Position management and modification
    - Stop loss and take profit handling
    - Error handling and retry logic
    - Integration with risk management
    """
    
    def __init__(self, symbol: str = None, magic_number: int = None):
        self.symbol = symbol or config.SYMBOL
        self.magic_number = magic_number or config.MAGIC_NUMBER
        
        # Order execution settings
        self.max_slippage = config.MAX_SLIPPAGE
        self.max_retries = 3
        self.retry_delay = 1.0  # seconds
        
        # Order tracking
        self.active_orders = {}
        self.order_history = []
        
        logger.info(f"OrderExecutor initialized for {self.symbol} (Magic: {self.magic_number})")
    
    def place_market_order(self, order_type: OrderType, volume: float,
                          stop_loss: Optional[float] = None,
                          take_profit: Optional[float] = None,
                          comment: str = "DEX900DN") -> OrderResult:
        """
        Place a market order
        
        Args:
            order_type: BUY or SELL
            volume: Order volume
            stop_loss: Stop loss price
            take_profit: Take profit price
            comment: Order comment
            
        Returns:
            OrderResult with execution details
        """
        if not mt5_connection.is_connected():
            return OrderResult(
                success=False,
                error_description="Not connected to MT5"
            )
        
        logger.info(f"Placing market order: {order_type.value} {volume} {self.symbol}")
        
        try:
            # Get current prices
            symbol_info = mt5_connection.get_symbol_info(force_update=True)
            if not symbol_info:
                return OrderResult(
                    success=False,
                    error_description="Failed to get symbol information"
                )
            
            # Determine order parameters
            if order_type == OrderType.BUY:
                action = mt5.TRADE_ACTION_DEAL
                type_order = mt5.ORDER_TYPE_BUY
                price = symbol_info.ask
            elif order_type == OrderType.SELL:
                action = mt5.TRADE_ACTION_DEAL
                type_order = mt5.ORDER_TYPE_SELL
                price = symbol_info.bid
            else:
                return OrderResult(
                    success=False,
                    error_description=f"Invalid market order type: {order_type}"
                )
            
            # Validate and sanitize comment (MT5 has strict requirements)
            sanitized_comment = self._sanitize_comment(comment)

            # Create order request (ensure all numeric values are native Python types)
            request = {
                "action": action,
                "symbol": self.symbol,
                "volume": float(volume),
                "type": type_order,
                "price": float(price),
                "deviation": int(self.max_slippage),
                "magic": int(self.magic_number),
                "comment": sanitized_comment,
                "type_time": mt5.ORDER_TIME_GTC,
                # Remove type_filling to use broker default
            }
            
            # Add stop loss and take profit if specified (convert to native Python types)
            # CRITICAL: Ensure TP/SL are properly set for trading strategy
            if stop_loss is not None and stop_loss > 0:
                request["sl"] = float(stop_loss)
                logger.debug(f"Stop loss set: {stop_loss}")
            else:
                logger.warning("No stop loss specified - high risk!")

            if take_profit is not None and take_profit > 0:
                request["tp"] = float(take_profit)
                logger.debug(f"Take profit set: {take_profit}")
            else:
                logger.warning("No take profit specified - position may run indefinitely!")

            # Validate TP/SL levels are reasonable
            if stop_loss is not None and take_profit is not None:
                if order_type == OrderType.BUY:
                    if stop_loss >= price:
                        logger.error(f"Invalid SL for BUY: {stop_loss} >= {price}")
                        return OrderResult(success=False, error_description="Stop loss must be below entry price for BUY orders")
                    if take_profit <= price:
                        logger.error(f"Invalid TP for BUY: {take_profit} <= {price}")
                        return OrderResult(success=False, error_description="Take profit must be above entry price for BUY orders")
                elif order_type == OrderType.SELL:
                    if stop_loss <= price:
                        logger.error(f"Invalid SL for SELL: {stop_loss} <= {price}")
                        return OrderResult(success=False, error_description="Stop loss must be above entry price for SELL orders")
                    if take_profit >= price:
                        logger.error(f"Invalid TP for SELL: {take_profit} >= {price}")
                        return OrderResult(success=False, error_description="Take profit must be below entry price for SELL orders")
            
            # Validate request before execution
            validation_error = self._validate_order_request(request)
            if validation_error:
                return OrderResult(
                    success=False,
                    error_description=f"Order validation failed: {validation_error}"
                )

            # Execute order with retry logic
            return self._execute_order_with_retry(request)
            
        except Exception as e:
            logger.error(f"Market order execution error: {str(e)}")
            return OrderResult(
                success=False,
                error_description=f"Execution error: {str(e)}"
            )
    
    def place_pending_order(self, order_type: OrderType, volume: float, price: float,
                           stop_loss: Optional[float] = None,
                           take_profit: Optional[float] = None,
                           expiration: Optional[datetime] = None,
                           comment: str = "DEX900DN") -> OrderResult:
        """
        Place a pending order
        
        Args:
            order_type: BUY_LIMIT, SELL_LIMIT, BUY_STOP, SELL_STOP
            volume: Order volume
            price: Order price
            stop_loss: Stop loss price
            take_profit: Take profit price
            expiration: Order expiration time
            comment: Order comment
            
        Returns:
            OrderResult with execution details
        """
        if not mt5_connection.is_connected():
            return OrderResult(
                success=False,
                error_description="Not connected to MT5"
            )
        
        logger.info(f"Placing pending order: {order_type.value} {volume} {self.symbol} @ {price}")
        
        try:
            # Determine order type
            type_mapping = {
                OrderType.BUY_LIMIT: mt5.ORDER_TYPE_BUY_LIMIT,
                OrderType.SELL_LIMIT: mt5.ORDER_TYPE_SELL_LIMIT,
                OrderType.BUY_STOP: mt5.ORDER_TYPE_BUY_STOP,
                OrderType.SELL_STOP: mt5.ORDER_TYPE_SELL_STOP
            }
            
            if order_type not in type_mapping:
                return OrderResult(
                    success=False,
                    error_description=f"Invalid pending order type: {order_type}"
                )
            
            # Validate and sanitize comment (MT5 has strict requirements)
            sanitized_comment = self._sanitize_comment(comment)

            # Create order request (ensure all numeric values are native Python types)
            request = {
                "action": mt5.TRADE_ACTION_PENDING,
                "symbol": self.symbol,
                "volume": float(volume),
                "type": type_mapping[order_type],
                "price": float(price),
                "magic": int(self.magic_number),
                "comment": sanitized_comment,
                "type_time": mt5.ORDER_TIME_GTC,
            }
            
            # Add stop loss and take profit if specified (convert to native Python types)
            if stop_loss:
                request["sl"] = float(stop_loss)
            if take_profit:
                request["tp"] = float(take_profit)
            
            # Add expiration if specified
            if expiration:
                request["type_time"] = mt5.ORDER_TIME_SPECIFIED
                request["expiration"] = int(expiration.timestamp())
            
            # Validate request before execution
            validation_error = self._validate_order_request(request)
            if validation_error:
                return OrderResult(
                    success=False,
                    error_description=f"Order validation failed: {validation_error}"
                )

            # Execute order with retry logic
            return self._execute_order_with_retry(request)
            
        except Exception as e:
            logger.error(f"Pending order execution error: {str(e)}")
            return OrderResult(
                success=False,
                error_description=f"Execution error: {str(e)}"
            )
    
    def modify_position(self, position_ticket: int,
                       stop_loss: Optional[float] = None,
                       take_profit: Optional[float] = None) -> OrderResult:
        """
        Modify position stop loss and take profit
        
        Args:
            position_ticket: Position ticket number
            stop_loss: New stop loss price
            take_profit: New take profit price
            
        Returns:
            OrderResult with modification details
        """
        if not mt5_connection.is_connected():
            return OrderResult(
                success=False,
                error_description="Not connected to MT5"
            )
        
        logger.info(f"Modifying position {position_ticket}: SL={stop_loss}, TP={take_profit}")
        
        try:
            # Get position information
            position = self.get_position_by_ticket(position_ticket)
            if not position:
                return OrderResult(
                    success=False,
                    error_description=f"Position {position_ticket} not found"
                )
            
            # Create modification request
            request = {
                "action": mt5.TRADE_ACTION_SLTP,
                "symbol": position.symbol,
                "position": position_ticket,
                "magic": self.magic_number,
            }
            
            # Set new stop loss and take profit
            if stop_loss is not None:
                request["sl"] = stop_loss
            else:
                request["sl"] = position.sl
                
            if take_profit is not None:
                request["tp"] = take_profit
            else:
                request["tp"] = position.tp
            
            # Execute modification with retry logic
            return self._execute_order_with_retry(request)
            
        except Exception as e:
            logger.error(f"Position modification error: {str(e)}")
            return OrderResult(
                success=False,
                error_description=f"Modification error: {str(e)}"
            )
    
    def close_position(self, position_ticket: int, volume: Optional[float] = None,
                      comment: str = "DEX900DN Close") -> OrderResult:
        """
        Close position (full or partial)
        
        Args:
            position_ticket: Position ticket number
            volume: Volume to close (None for full close)
            comment: Close comment
            
        Returns:
            OrderResult with close details
        """
        if not mt5_connection.is_connected():
            return OrderResult(
                success=False,
                error_description="Not connected to MT5"
            )
        
        logger.info(f"Closing position {position_ticket} (Volume: {volume or 'Full'})")
        
        try:
            # Get position information
            position = self.get_position_by_ticket(position_ticket)
            if not position:
                return OrderResult(
                    success=False,
                    error_description=f"Position {position_ticket} not found"
                )
            
            # Determine close volume
            close_volume = volume if volume is not None else position.volume
            
            # Determine close type (opposite of position type)
            if position.type == mt5.POSITION_TYPE_BUY:
                close_type = mt5.ORDER_TYPE_SELL
            else:
                close_type = mt5.ORDER_TYPE_BUY
            
            # Get current price
            symbol_info = mt5_connection.get_symbol_info(force_update=True)
            if not symbol_info:
                return OrderResult(
                    success=False,
                    error_description="Failed to get symbol information"
                )
            
            close_price = symbol_info.bid if close_type == mt5.ORDER_TYPE_SELL else symbol_info.ask
            
            # Create close request
            request = {
                "action": mt5.TRADE_ACTION_DEAL,
                "symbol": position.symbol,
                "volume": close_volume,
                "type": close_type,
                "position": position_ticket,
                "price": close_price,
                "deviation": self.max_slippage,
                "magic": self.magic_number,
                "comment": comment,
                "type_time": mt5.ORDER_TIME_GTC,
                "type_filling": mt5.ORDER_FILLING_IOC,
            }
            
            # Execute close with retry logic
            return self._execute_order_with_retry(request)
            
        except Exception as e:
            logger.error(f"Position close error: {str(e)}")
            return OrderResult(
                success=False,
                error_description=f"Close error: {str(e)}"
            )
    
    def cancel_order(self, order_ticket: int) -> OrderResult:
        """
        Cancel pending order
        
        Args:
            order_ticket: Order ticket number
            
        Returns:
            OrderResult with cancellation details
        """
        if not mt5_connection.is_connected():
            return OrderResult(
                success=False,
                error_description="Not connected to MT5"
            )
        
        logger.info(f"Cancelling order {order_ticket}")
        
        try:
            # Create cancellation request
            request = {
                "action": mt5.TRADE_ACTION_REMOVE,
                "order": order_ticket,
            }
            
            # Execute cancellation with retry logic
            return self._execute_order_with_retry(request)
            
        except Exception as e:
            logger.error(f"Order cancellation error: {str(e)}")
            return OrderResult(
                success=False,
                error_description=f"Cancellation error: {str(e)}"
            )
    
    def _execute_order_with_retry(self, request: Dict[str, Any]) -> OrderResult:
        """Execute order with retry logic"""
        last_error = None
        
        for attempt in range(self.max_retries):
            try:
                if not MT5_AVAILABLE:
                    # Simulation mode
                    return self._simulate_order_execution(request)
                
                # Execute order
                result = mt5.order_send(request)
                
                if result is None:
                    error_code, error_desc = mt5.last_error()
                    last_error = f"Order execution failed: {error_code} - {error_desc}"
                    logger.warning(f"Attempt {attempt + 1}: {last_error}")

                    # Special handling for common errors
                    if error_code == -2:
                        logger.error(f"Invalid argument error (-2). Request: {request}")
                        # Try with minimal comment
                        if 'comment' in request and len(request['comment']) > 8:
                            request['comment'] = "DEX900DN"
                            logger.info("Retrying with simplified comment")

                    if attempt < self.max_retries - 1:
                        time.sleep(self.retry_delay)
                    continue
                
                # Convert result to OrderResult
                order_result = OrderResult(
                    success=result.retcode == mt5.TRADE_RETCODE_DONE,
                    order_id=getattr(result, 'order', None),
                    position_id=getattr(result, 'position', None),
                    deal_id=getattr(result, 'deal', None),
                    volume=getattr(result, 'volume', 0.0),
                    price=getattr(result, 'price', 0.0),
                    error_code=result.retcode,
                    error_description=self._get_retcode_description(result.retcode),
                    comment=getattr(result, 'comment', ''),
                    request_id=getattr(result, 'request_id', 0),
                    retcode=result.retcode,
                    retcode_external=getattr(result, 'retcode_external', 0)
                )
                
                if order_result.success:
                    logger.info(f"Order executed successfully: {order_result.order_id or order_result.deal_id}")
                    self._track_order(request, order_result)
                else:
                    logger.error(f"Order execution failed: {order_result.error_description}")
                
                return order_result
                
            except Exception as e:
                last_error = f"Execution exception: {str(e)}"
                logger.error(f"Attempt {attempt + 1}: {last_error}")
                
                if attempt < self.max_retries - 1:
                    time.sleep(self.retry_delay)
        
        # All attempts failed
        return OrderResult(
            success=False,
            error_description=last_error or "Order execution failed after all retries"
        )
    
    def _simulate_order_execution(self, request: Dict[str, Any]) -> OrderResult:
        """Simulate order execution when MT5 is not available"""
        import random
        
        # Simulate successful execution
        return OrderResult(
            success=True,
            order_id=random.randint(100000, 999999),
            deal_id=random.randint(100000, 999999),
            volume=request.get('volume', 0.0),
            price=request.get('price', 25000.0),
            error_code=0,
            error_description="Simulated execution",
            comment="SIMULATION MODE"
        )
    
    def _get_retcode_description(self, retcode: int) -> str:
        """Get description for MT5 return code"""
        retcode_descriptions = {
            mt5.TRADE_RETCODE_DONE: "Request completed",
            mt5.TRADE_RETCODE_REQUOTE: "Requote",
            mt5.TRADE_RETCODE_REJECT: "Request rejected",
            mt5.TRADE_RETCODE_CANCEL: "Request cancelled",
            mt5.TRADE_RETCODE_PLACED: "Order placed",
            mt5.TRADE_RETCODE_DONE_PARTIAL: "Request completed partially",
            mt5.TRADE_RETCODE_ERROR: "Request processing error",
            mt5.TRADE_RETCODE_TIMEOUT: "Request cancelled by timeout",
            mt5.TRADE_RETCODE_INVALID: "Invalid request",
            mt5.TRADE_RETCODE_INVALID_VOLUME: "Invalid volume",
            mt5.TRADE_RETCODE_INVALID_PRICE: "Invalid price",
            mt5.TRADE_RETCODE_INVALID_STOPS: "Invalid stops",
            mt5.TRADE_RETCODE_TRADE_DISABLED: "Trade disabled",
            mt5.TRADE_RETCODE_MARKET_CLOSED: "Market closed",
            mt5.TRADE_RETCODE_NO_MONEY: "No money",
            mt5.TRADE_RETCODE_PRICE_CHANGED: "Price changed",
            mt5.TRADE_RETCODE_PRICE_OFF: "Off quotes",
            mt5.TRADE_RETCODE_INVALID_EXPIRATION: "Invalid expiration",
            mt5.TRADE_RETCODE_ORDER_CHANGED: "Order state changed",
            mt5.TRADE_RETCODE_TOO_MANY_REQUESTS: "Too many requests",
            mt5.TRADE_RETCODE_NO_CHANGES: "No changes",
            mt5.TRADE_RETCODE_SERVER_DISABLES_AT: "Autotrading disabled by server",
            mt5.TRADE_RETCODE_CLIENT_DISABLES_AT: "Autotrading disabled by client",
            mt5.TRADE_RETCODE_LOCKED: "Request locked",
            mt5.TRADE_RETCODE_FROZEN: "Order or position frozen",
            mt5.TRADE_RETCODE_INVALID_FILL: "Invalid fill",
            mt5.TRADE_RETCODE_CONNECTION: "No connection",
            mt5.TRADE_RETCODE_ONLY_REAL: "Only real accounts allowed",
            mt5.TRADE_RETCODE_LIMIT_ORDERS: "Limit orders limit reached",
            mt5.TRADE_RETCODE_LIMIT_VOLUME: "Volume limit reached",
            mt5.TRADE_RETCODE_INVALID_ORDER: "Invalid order",
            mt5.TRADE_RETCODE_POSITION_CLOSED: "Position closed",
        }
        
        return retcode_descriptions.get(retcode, f"Unknown return code: {retcode}")

    def _sanitize_comment(self, comment: str) -> str:
        """
        Sanitize comment field for MT5 compatibility

        MT5 comment field requirements:
        - Maximum length: 31 characters for some brokers
        - No special characters that might cause issues
        - ASCII characters only

        Args:
            comment: Original comment string

        Returns:
            Sanitized comment string
        """
        if not comment:
            return "DEX900DN"

        # Remove non-ASCII characters and limit length
        sanitized = ''.join(char for char in comment if ord(char) < 128)

        # Limit to 31 characters (safe for most brokers)
        sanitized = sanitized[:31]

        # Remove potentially problematic characters
        problematic_chars = ['\\', '/', ':', '*', '?', '"', '<', '>', '|', '\n', '\r', '\t']
        for char in problematic_chars:
            sanitized = sanitized.replace(char, '')

        # Ensure we have a valid comment
        if not sanitized.strip():
            sanitized = "DEX900DN"

        return sanitized.strip()

    def _validate_order_request(self, request: Dict[str, Any]) -> Optional[str]:
        """
        Validate order request for MT5 compatibility

        Args:
            request: Order request dictionary

        Returns:
            Error message if validation fails, None if valid
        """
        # Check required fields
        required_fields = ['action', 'symbol', 'volume', 'type']
        for field in required_fields:
            if field not in request:
                return f"Missing required field: {field}"

        # Validate volume
        volume = request.get('volume', 0)
        if volume <= 0:
            return f"Invalid volume: {volume}"

        # Validate symbol
        symbol = request.get('symbol', '')
        if not symbol:
            return "Empty symbol"

        # Validate comment length and content
        comment = request.get('comment', '')
        if len(comment) > 31:
            return f"Comment too long: {len(comment)} chars (max 31)"

        # Check for invalid characters in comment
        if comment and not all(ord(char) < 128 for char in comment):
            return "Comment contains non-ASCII characters"

        # Validate magic number
        magic = request.get('magic', 0)
        if not isinstance(magic, int) or magic < 0:
            return f"Invalid magic number: {magic}"

        # Validate price for market orders
        if request.get('action') == mt5.TRADE_ACTION_DEAL:
            price = request.get('price', 0)
            if price <= 0:
                return f"Invalid price for market order: {price}"

        return None  # Validation passed
    
    def _track_order(self, request: Dict[str, Any], result: OrderResult) -> None:
        """Track order for monitoring purposes"""
        order_record = {
            'timestamp': datetime.now(),
            'request': request.copy(),
            'result': result,
            'symbol': self.symbol,
            'magic': self.magic_number
        }
        
        self.order_history.append(order_record)
        
        # Keep only last 1000 orders
        if len(self.order_history) > 1000:
            self.order_history = self.order_history[-1000:]
    
    def get_positions(self, symbol: str = None) -> List[Position]:
        """Get all open positions"""
        if not mt5_connection.is_connected():
            return []
        
        try:
            symbol_filter = symbol or self.symbol
            positions_data = mt5.positions_get(symbol=symbol_filter)
            
            if positions_data is None:
                return []
            
            positions = []
            for pos_data in positions_data:
                if pos_data.magic == self.magic_number:  # Filter by magic number
                    position = Position(**pos_data._asdict())
                    positions.append(position)
            
            return positions
            
        except Exception as e:
            logger.error(f"Error getting positions: {str(e)}")
            return []
    
    def get_position_by_ticket(self, ticket: int) -> Optional[Position]:
        """Get position by ticket number"""
        positions = self.get_positions()
        for position in positions:
            if position.ticket == ticket:
                return position
        return None
    
    def get_orders(self, symbol: str = None) -> List[Dict[str, Any]]:
        """Get all pending orders"""
        if not mt5_connection.is_connected():
            return []
        
        try:
            symbol_filter = symbol or self.symbol
            orders_data = mt5.orders_get(symbol=symbol_filter)
            
            if orders_data is None:
                return []
            
            orders = []
            for order_data in orders_data:
                if order_data.magic == self.magic_number:  # Filter by magic number
                    orders.append(order_data._asdict())
            
            return orders
            
        except Exception as e:
            logger.error(f"Error getting orders: {str(e)}")
            return []
