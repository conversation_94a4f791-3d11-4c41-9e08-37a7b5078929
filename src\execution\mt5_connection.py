"""
MT5 Connection Manager for DEX900DN Trading System
Handles MetaTrader 5 API connection, monitoring, and error recovery
"""

import logging
import time
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List, Tuple
from dataclasses import dataclass
from enum import Enum
import threading

try:
    import MetaTrader5 as mt5
    MT5_AVAILABLE = True
except ImportError:
    MT5_AVAILABLE = False
    logging.warning("MetaTrader5 module not available - running in simulation mode")

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))
from config.config import config

# Setup logging
logging.basicConfig(level=getattr(logging, config.LOG_LEVEL))
logger = logging.getLogger(__name__)

class ConnectionStatus(Enum):
    """MT5 connection status"""
    DISCONNECTED = "DISCONNECTED"
    CONNECTING = "CONNECTING"
    CONNECTED = "CONNECTED"
    ERROR = "ERROR"
    RECONNECTING = "RECONNECTING"

@dataclass
class AccountInfo:
    """MT5 account information"""
    login: int
    trade_mode: int
    leverage: int
    limit_orders: int
    margin_so_mode: int
    trade_allowed: bool
    trade_expert: bool
    margin_mode: int
    currency_digits: int
    fifo_close: bool
    balance: float
    credit: float
    profit: float
    equity: float
    margin: float
    margin_free: float
    margin_level: float
    margin_so_call: float
    margin_so_so: float
    margin_initial: float
    margin_maintenance: float
    assets: float
    liabilities: float
    commission_blocked: float
    name: str
    server: str
    currency: str
    company: str

@dataclass
class SymbolInfo:
    """MT5 symbol information"""
    custom: bool
    chart_mode: int
    select: bool
    visible: bool
    session_deals: int
    session_buy_orders: int
    session_sell_orders: int
    volume: int
    volumehigh: int
    volumelow: int
    time: int
    digits: int
    spread: int
    spread_float: bool
    ticks_bookdepth: int
    trade_calc_mode: int
    trade_mode: int
    start_time: int
    expiration_time: int
    trade_stops_level: int
    trade_freeze_level: int
    trade_exemode: int
    swap_mode: int
    swap_rollover3days: int
    margin_hedged_use_leg: bool
    expiration_mode: int
    filling_mode: int
    order_mode: int
    order_gtc_mode: int
    option_mode: int
    option_right: int
    bid: float
    bidlow: float
    bidhigh: float
    ask: float
    asklow: float
    askhigh: float
    last: float
    lastlow: float
    lasthigh: float
    volume_real: float
    volumehigh_real: float
    volumelow_real: float
    option_strike: float
    point: float
    trade_tick_value: float
    trade_tick_value_profit: float
    trade_tick_value_loss: float
    trade_tick_size: float
    trade_contract_size: float
    trade_accrued_interest: float
    trade_face_value: float
    trade_liquidity_rate: float
    volume_min: float
    volume_max: float
    volume_step: float
    volume_limit: float
    swap_long: float
    swap_short: float
    margin_initial: float
    margin_maintenance: float
    session_volume: float
    session_turnover: float
    session_interest: float
    session_buy_orders_volume: float
    session_sell_orders_volume: float
    session_open: float
    session_close: float
    session_aw: float
    session_price_settlement: float
    session_price_limit_min: float
    session_price_limit_max: float
    margin_hedged: float
    price_change: float
    price_volatility: float
    price_theoretical: float
    price_greeks_delta: float
    price_greeks_theta: float
    price_greeks_gamma: float
    price_greeks_vega: float
    price_greeks_rho: float
    price_greeks_omega: float
    price_sensitivity: float
    basis: str
    category: str
    currency_base: str
    currency_profit: str
    currency_margin: str
    bank: str
    description: str
    exchange: str
    formula: str
    isin: str
    name: str
    page: str
    path: str

class MT5ConnectionManager:
    """
    MetaTrader 5 Connection Manager for DEX900DN Trading System
    
    Features:
    - Automatic connection management
    - Connection monitoring and recovery
    - Account and symbol information retrieval
    - Error handling and logging
    - Thread-safe operations
    """
    
    def __init__(self, symbol: str = None, auto_connect: bool = True):
        self.symbol = symbol or config.SYMBOL
        self.status = ConnectionStatus.DISCONNECTED
        self.last_error = None
        self.connection_attempts = 0
        self.max_connection_attempts = 5
        self.reconnect_delay = 5  # seconds
        
        # Connection monitoring
        self.last_heartbeat = None
        self.heartbeat_interval = 30  # seconds
        self.monitoring_active = False
        self.monitor_thread = None
        
        # Account and symbol info cache
        self.account_info = None
        self.symbol_info = None
        self.last_info_update = None
        self.info_cache_duration = 60  # seconds
        
        # Thread safety
        self.connection_lock = threading.Lock()
        
        # Deriv-specific configuration
        self.is_deriv = False
        self.broker_name = ""

        logger.info(f"MT5ConnectionManager initialized for {self.symbol}")

        if auto_connect and MT5_AVAILABLE:
            self.connect()
            # Apply broker-specific settings after connection
            self._apply_broker_specific_settings()
    
    def connect(self) -> bool:
        """
        Establish connection to MetaTrader 5
        
        Returns:
            bool: True if connection successful
        """
        with self.connection_lock:
            if not MT5_AVAILABLE:
                logger.error("MetaTrader5 module not available")
                self.status = ConnectionStatus.ERROR
                return False
            
            self.status = ConnectionStatus.CONNECTING
            logger.info("Connecting to MetaTrader 5...")
            
            try:
                # Initialize MT5 connection
                if not mt5.initialize():
                    error_code, error_desc = mt5.last_error()
                    self.last_error = f"MT5 initialization failed: {error_code} - {error_desc}"
                    logger.error(self.last_error)
                    self.status = ConnectionStatus.ERROR
                    return False
                
                # Verify symbol availability
                if not self._verify_symbol():
                    self.status = ConnectionStatus.ERROR
                    return False
                
                # Update account and symbol information
                self._update_account_info()
                self._update_symbol_info()
                
                self.status = ConnectionStatus.CONNECTED
                self.last_heartbeat = datetime.now()
                self.connection_attempts = 0
                
                logger.info("Successfully connected to MetaTrader 5")
                logger.info(f"Account: {self.account_info.login if self.account_info else 'Unknown'}")
                logger.info(f"Server: {self.account_info.server if self.account_info else 'Unknown'}")
                logger.info(f"Symbol: {self.symbol} verified")
                
                # Start connection monitoring
                self._start_monitoring()
                
                return True
                
            except Exception as e:
                self.last_error = f"Connection error: {str(e)}"
                logger.error(self.last_error)
                self.status = ConnectionStatus.ERROR
                return False
    
    def disconnect(self) -> bool:
        """
        Disconnect from MetaTrader 5
        
        Returns:
            bool: True if disconnection successful
        """
        with self.connection_lock:
            logger.info("Disconnecting from MetaTrader 5...")
            
            # Stop monitoring
            self._stop_monitoring()
            
            try:
                if MT5_AVAILABLE:
                    mt5.shutdown()
                
                self.status = ConnectionStatus.DISCONNECTED
                self.last_heartbeat = None
                
                logger.info("Successfully disconnected from MetaTrader 5")
                return True
                
            except Exception as e:
                logger.error(f"Disconnection error: {str(e)}")
                return False
    
    def is_connected(self) -> bool:
        """Check if currently connected to MT5"""
        return self.status == ConnectionStatus.CONNECTED
    
    def _verify_symbol(self) -> bool:
        """Verify that the trading symbol is available"""
        try:
            symbol_info = mt5.symbol_info(self.symbol)
            if symbol_info is None:
                self.last_error = f"Symbol {self.symbol} not found"
                logger.error(self.last_error)
                return False
            
            # Enable symbol if not visible
            if not symbol_info.visible:
                if not mt5.symbol_select(self.symbol, True):
                    self.last_error = f"Failed to select symbol {self.symbol}"
                    logger.error(self.last_error)
                    return False
                
                logger.info(f"Symbol {self.symbol} enabled for trading")
            
            return True
            
        except Exception as e:
            self.last_error = f"Symbol verification error: {str(e)}"
            logger.error(self.last_error)
            return False
    
    def _update_account_info(self) -> bool:
        """Update cached account information"""
        try:
            account_data = mt5.account_info()
            if account_data is None:
                logger.warning("Failed to retrieve account information")
                return False
            
            # Convert to our AccountInfo dataclass
            self.account_info = AccountInfo(**account_data._asdict())
            self.last_info_update = datetime.now()
            
            logger.debug(f"Account info updated: Balance ${self.account_info.balance:.2f}")
            return True
            
        except Exception as e:
            logger.error(f"Error updating account info: {str(e)}")
            return False
    
    def _update_symbol_info(self) -> bool:
        """Update cached symbol information"""
        try:
            symbol_data = mt5.symbol_info(self.symbol)
            if symbol_data is None:
                logger.warning(f"Failed to retrieve symbol information for {self.symbol}")
                return False
            
            # Convert to our SymbolInfo dataclass
            self.symbol_info = SymbolInfo(**symbol_data._asdict())
            
            logger.debug(f"Symbol info updated: {self.symbol} Bid={self.symbol_info.bid} Ask={self.symbol_info.ask}")
            return True
            
        except Exception as e:
            logger.error(f"Error updating symbol info: {str(e)}")
            return False
    
    def get_account_info(self, force_update: bool = False) -> Optional[AccountInfo]:
        """
        Get account information (cached or fresh)
        
        Args:
            force_update: Force refresh from MT5
            
        Returns:
            AccountInfo object or None if error
        """
        if not self.is_connected():
            logger.warning("Not connected to MT5")
            return None
        
        # Check if cache is still valid
        if (not force_update and 
            self.account_info and 
            self.last_info_update and
            (datetime.now() - self.last_info_update).total_seconds() < self.info_cache_duration):
            return self.account_info
        
        # Update from MT5
        if self._update_account_info():
            return self.account_info
        
        return None
    
    def get_symbol_info(self, force_update: bool = False) -> Optional[SymbolInfo]:
        """
        Get symbol information (cached or fresh)
        
        Args:
            force_update: Force refresh from MT5
            
        Returns:
            SymbolInfo object or None if error
        """
        if not self.is_connected():
            logger.warning("Not connected to MT5")
            return None
        
        # Always update symbol info as prices change frequently
        if force_update or not self.symbol_info:
            self._update_symbol_info()
        
        return self.symbol_info
    
    def _start_monitoring(self) -> None:
        """Start connection monitoring thread"""
        if self.monitoring_active:
            return
        
        self.monitoring_active = True
        self.monitor_thread = threading.Thread(target=self._monitor_connection, daemon=True)
        self.monitor_thread.start()
        
        logger.debug("Connection monitoring started")
    
    def _stop_monitoring(self) -> None:
        """Stop connection monitoring thread"""
        self.monitoring_active = False
        if self.monitor_thread and self.monitor_thread.is_alive():
            self.monitor_thread.join(timeout=5)
        
        logger.debug("Connection monitoring stopped")
    
    def _monitor_connection(self) -> None:
        """Monitor connection health and attempt reconnection if needed"""
        while self.monitoring_active:
            try:
                if self.status == ConnectionStatus.CONNECTED:
                    # Check connection health
                    if self._check_connection_health():
                        self.last_heartbeat = datetime.now()
                    else:
                        logger.warning("Connection health check failed")
                        self._attempt_reconnection()
                
                time.sleep(self.heartbeat_interval)
                
            except Exception as e:
                logger.error(f"Connection monitoring error: {str(e)}")
                time.sleep(self.heartbeat_interval)
    
    def _check_connection_health(self) -> bool:
        """Check if MT5 connection is healthy"""
        try:
            # Simple health check - try to get account info
            account_data = mt5.account_info()
            return account_data is not None
            
        except Exception as e:
            logger.error(f"Connection health check error: {str(e)}")
            return False
    
    def _attempt_reconnection(self) -> None:
        """Attempt to reconnect to MT5"""
        if self.connection_attempts >= self.max_connection_attempts:
            logger.error("Maximum reconnection attempts reached")
            self.status = ConnectionStatus.ERROR
            return
        
        self.status = ConnectionStatus.RECONNECTING
        self.connection_attempts += 1
        
        logger.info(f"Attempting reconnection ({self.connection_attempts}/{self.max_connection_attempts})...")
        
        # Disconnect first
        try:
            mt5.shutdown()
        except:
            pass
        
        # Wait before reconnecting
        time.sleep(self.reconnect_delay)
        
        # Attempt reconnection
        if self.connect():
            logger.info("Reconnection successful")
        else:
            logger.error(f"Reconnection attempt {self.connection_attempts} failed")
    
    def get_connection_status(self) -> Dict[str, Any]:
        """Get comprehensive connection status"""
        return {
            "status": self.status.value,
            "connected": self.is_connected(),
            "symbol": self.symbol,
            "last_error": self.last_error,
            "connection_attempts": self.connection_attempts,
            "last_heartbeat": self.last_heartbeat.isoformat() if self.last_heartbeat else None,
            "monitoring_active": self.monitoring_active,
            "account_login": self.account_info.login if self.account_info else None,
            "account_server": self.account_info.server if self.account_info else None,
            "account_balance": self.account_info.balance if self.account_info else None
        }

    def _apply_broker_specific_settings(self) -> None:
        """Apply broker-specific settings and optimizations"""
        try:
            if not self.is_connected():
                return

            # Get account info to determine broker
            account_info = self.get_account_info()
            if account_info:
                self.broker_name = getattr(account_info, 'server', '')

                # Check if this is Deriv
                if 'Deriv' in self.broker_name:
                    self.is_deriv = True
                    logger.info("Deriv broker detected - applying specific settings")

                    # Deriv-specific optimizations
                    self.max_connection_attempts = 3  # Reduce connection attempts
                    self.reconnect_delay = 2  # Faster reconnection
                    self.info_cache_duration = 30  # Shorter cache duration

                    logger.info("Deriv-specific settings applied")
                else:
                    logger.info(f"Broker detected: {self.broker_name}")

        except Exception as e:
            logger.error(f"Error applying broker-specific settings: {e}")

# Global connection manager instance
mt5_connection = MT5ConnectionManager(auto_connect=False)  # Don't auto-connect on import
