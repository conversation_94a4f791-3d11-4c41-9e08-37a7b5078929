"""
DEX900DN Production Launcher
Production deployment and management script for the trading system
"""

import os
import sys
import time
import signal
import logging
import argparse
import subprocess
from datetime import datetime
from pathlib import Path

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))

from src.main_controller import DEX900D<PERSON>ontroller, SystemState
from src.monitoring.system_monitor import SystemHealthMonitor
from config.config import config

class ProductionLauncher:
    """
    Production launcher for DEX900DN Trading System
    
    Features:
    - Production environment setup
    - System startup and monitoring
    - Graceful shutdown handling
    - Error recovery and restart
    - Performance monitoring
    - Log management
    """
    
    def __init__(self, config_file: str = None):
        self.config_file = config_file
        self.controller = None
        self.health_monitor = None
        self.running = False
        self.restart_count = 0
        self.max_restarts = 5
        
        # Setup production logging
        self.setup_production_logging()
        
        # Setup signal handlers
        signal.signal(signal.SIGINT, self.signal_handler)
        signal.signal(signal.SIGTERM, self.signal_handler)
        
        self.logger = logging.getLogger(__name__)
        self.logger.info("ProductionLauncher initialized")
    
    def setup_production_logging(self):
        """Setup production-grade logging"""
        log_dir = Path("logs")
        log_dir.mkdir(exist_ok=True)
        
        # Create log filename with timestamp
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        log_file = log_dir / f"dex900dn_production_{timestamp}.log"
        
        # Configure logging
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file),
                logging.StreamHandler(sys.stdout)
            ]
        )
        
        print(f"📝 Production logging configured: {log_file}")
    
    def validate_environment(self) -> bool:
        """Validate production environment"""
        print("🔍 Validating production environment...")
        
        checks = []
        
        # Check Python version
        python_version = sys.version_info
        if python_version.major >= 3 and python_version.minor >= 8:
            checks.append(("Python version", True, f"{python_version.major}.{python_version.minor}"))
        else:
            checks.append(("Python version", False, f"Requires Python 3.8+, got {python_version.major}.{python_version.minor}"))
        
        # Check required directories
        required_dirs = ["src", "config", "logs", "data"]
        for dir_name in required_dirs:
            dir_path = Path(dir_name)
            if dir_path.exists():
                checks.append((f"Directory {dir_name}", True, "Exists"))
            else:
                dir_path.mkdir(exist_ok=True)
                checks.append((f"Directory {dir_name}", True, "Created"))
        
        # Check configuration
        try:
            symbol = config.SYMBOL
            magic_number = config.MAGIC_NUMBER
            checks.append(("Configuration", True, f"Symbol: {symbol}, Magic: {magic_number}"))
        except Exception as e:
            checks.append(("Configuration", False, f"Error: {str(e)}"))
        
        # Check dependencies
        try:
            import MetaTrader5
            checks.append(("MetaTrader5", True, "Available"))
        except ImportError:
            checks.append(("MetaTrader5", False, "Not available - will run in simulation mode"))
        
        try:
            import psutil
            checks.append(("psutil", True, "Available"))
        except ImportError:
            checks.append(("psutil", False, "Required for system monitoring"))
        
        # Print validation results
        all_passed = True
        for check_name, passed, message in checks:
            status = "✅" if passed else "❌"
            print(f"   {status} {check_name}: {message}")
            if not passed and check_name in ["Python version", "Configuration", "psutil"]:
                all_passed = False
        
        return all_passed
    
    def start_production_system(self) -> bool:
        """Start the production trading system"""
        print("🚀 Starting DEX900DN Production System...")
        
        try:
            # Initialize system controller
            self.controller = DEX900DNController()
            
            # Initialize health monitor
            self.health_monitor = SystemHealthMonitor(check_interval=5.0)
            
            # Setup health monitoring alerts
            self.health_monitor.add_alert_callback(self.handle_health_alert)
            
            # Start health monitoring
            self.health_monitor.start_monitoring()
            
            # Start trading system
            success = self.controller.start_system()
            if not success:
                print("❌ Failed to start trading system")
                return False
            
            self.running = True
            print("✅ Production system started successfully")
            
            # Log startup
            self.logger.info("DEX900DN Production System started")
            self.logger.info(f"Symbol: {config.SYMBOL}")
            self.logger.info(f"Magic Number: {config.MAGIC_NUMBER}")
            
            return True
            
        except Exception as e:
            print(f"❌ Error starting production system: {str(e)}")
            self.logger.error(f"Startup error: {str(e)}")
            return False
    
    def monitor_system(self):
        """Monitor system operation"""
        print("👁️  Starting system monitoring...")
        
        last_status_time = time.time()
        status_interval = 60  # Print status every minute
        
        while self.running:
            try:
                # Check system health
                if self.controller.system_state not in [SystemState.RUNNING, SystemState.PAUSED]:
                    print(f"⚠️  System state changed to: {self.controller.system_state}")
                    
                    if self.controller.system_state == SystemState.ERROR:
                        print("🔄 Attempting system restart...")
                        if self.restart_system():
                            continue
                        else:
                            break
                
                # Print periodic status
                current_time = time.time()
                if current_time - last_status_time >= status_interval:
                    self.print_system_status()
                    last_status_time = current_time
                
                # Check for emergency conditions
                if self.check_emergency_conditions():
                    print("🚨 Emergency conditions detected - stopping system")
                    break
                
                time.sleep(5)  # Check every 5 seconds
                
            except KeyboardInterrupt:
                print("\n⏹️  Shutdown requested by user")
                break
            except Exception as e:
                print(f"❌ Error in monitoring loop: {str(e)}")
                self.logger.error(f"Monitoring error: {str(e)}")
                time.sleep(10)
    
    def print_system_status(self):
        """Print current system status"""
        try:
            status = self.controller.get_system_status()
            health = self.health_monitor.get_system_health()
            
            print(f"\n📊 System Status - {datetime.now().strftime('%H:%M:%S')}")
            print(f"   State: {status.state.value}")
            print(f"   Uptime: {status.uptime}")
            print(f"   Cycles: {status.cycles_processed}")
            print(f"   Trades: {status.trades_executed}")
            print(f"   Phase: {status.current_phase}")
            print(f"   Balance: ${status.account_balance:.2f}")
            print(f"   Daily P/L: ${status.daily_pnl:.2f}")
            print(f"   Positions: {status.active_positions}")
            print(f"   Health: {health['overall_status']}")
            print(f"   CPU: {health['system_resources'].get('cpu_percent', 0):.1f}%")
            print(f"   Memory: {health['system_resources'].get('memory_percent', 0):.1f}%")
            
            # Log status
            self.logger.info(f"Status: {status.state.value}, Cycles: {status.cycles_processed}, "
                           f"Trades: {status.trades_executed}, P/L: ${status.daily_pnl:.2f}")
            
        except Exception as e:
            print(f"❌ Error getting system status: {str(e)}")
    
    def check_emergency_conditions(self) -> bool:
        """Check for emergency stop conditions"""
        try:
            status = self.controller.get_system_status()
            
            # Check for excessive errors
            if len(status.errors) > 20:
                print(f"🚨 Too many errors: {len(status.errors)}")
                return True
            
            # Check daily loss limit
            if status.daily_pnl < -1000:  # $1000 daily loss limit
                print(f"🚨 Daily loss limit exceeded: ${status.daily_pnl:.2f}")
                return True
            
            # Check system health
            health = self.health_monitor.get_system_health()
            if health['overall_status'] == 'UNHEALTHY':
                print("🚨 System health is unhealthy")
                return True
            
            return False
            
        except Exception as e:
            print(f"❌ Error checking emergency conditions: {str(e)}")
            return True  # Err on the side of caution
    
    def restart_system(self) -> bool:
        """Restart the trading system"""
        if self.restart_count >= self.max_restarts:
            print(f"❌ Maximum restart attempts reached: {self.restart_count}")
            return False
        
        self.restart_count += 1
        print(f"🔄 Restarting system (attempt {self.restart_count}/{self.max_restarts})...")
        
        try:
            # Stop current system
            if self.controller:
                self.controller.stop_system()
            
            # Wait a moment
            time.sleep(5)
            
            # Start new system
            return self.start_production_system()
            
        except Exception as e:
            print(f"❌ Restart failed: {str(e)}")
            self.logger.error(f"Restart failed: {str(e)}")
            return False
    
    def handle_health_alert(self, alert):
        """Handle health monitoring alerts"""
        alert_msg = f"ALERT [{alert.level.value}] {alert.component}: {alert.message}"
        print(f"🚨 {alert_msg}")
        self.logger.warning(alert_msg)
        
        # Take action based on alert level
        if alert.level.value == "CRITICAL":
            print("🚨 Critical alert - considering emergency stop")
            # Could implement automatic emergency stop here
    
    def shutdown_system(self):
        """Gracefully shutdown the system"""
        print("🛑 Shutting down production system...")
        self.running = False
        
        try:
            # Stop trading system
            if self.controller:
                self.controller.stop_system()
                print("✅ Trading system stopped")
            
            # Stop health monitoring
            if self.health_monitor:
                self.health_monitor.stop_monitoring()
                print("✅ Health monitoring stopped")
            
            # Export final health report
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            report_file = f"logs/final_health_report_{timestamp}.json"
            if self.health_monitor:
                self.health_monitor.export_health_report(report_file)
                print(f"✅ Health report exported: {report_file}")
            
            self.logger.info("Production system shutdown complete")
            print("✅ Production system shutdown complete")
            
        except Exception as e:
            print(f"❌ Error during shutdown: {str(e)}")
            self.logger.error(f"Shutdown error: {str(e)}")
    
    def signal_handler(self, signum, frame):
        """Handle system signals"""
        print(f"\n📡 Received signal {signum}")
        self.shutdown_system()
        sys.exit(0)
    
    def run(self):
        """Main production run method"""
        print("="*70)
        print("🎯 DEX900DN PRODUCTION TRADING SYSTEM")
        print("="*70)
        
        # Validate environment
        if not self.validate_environment():
            print("❌ Environment validation failed")
            return False
        
        # Start system
        if not self.start_production_system():
            print("❌ Failed to start production system")
            return False
        
        try:
            # Monitor system
            self.monitor_system()
        finally:
            # Ensure clean shutdown
            self.shutdown_system()
        
        return True

def main():
    """Main entry point"""
    parser = argparse.ArgumentParser(description="DEX900DN Production Launcher")
    parser.add_argument("--config", help="Configuration file path")
    parser.add_argument("--validate-only", action="store_true", 
                       help="Only validate environment, don't start system")
    parser.add_argument("--test-mode", action="store_true",
                       help="Run in test mode with reduced monitoring")
    
    args = parser.parse_args()
    
    launcher = ProductionLauncher(args.config)
    
    if args.validate_only:
        success = launcher.validate_environment()
        print(f"\n{'✅ Environment validation passed' if success else '❌ Environment validation failed'}")
        return 0 if success else 1
    
    if args.test_mode:
        print("🧪 Running in test mode")
        # Could modify behavior for testing
    
    try:
        success = launcher.run()
        return 0 if success else 1
    except KeyboardInterrupt:
        print("\n⏹️  Production launcher interrupted")
        return 0
    except Exception as e:
        print(f"❌ Production launcher error: {str(e)}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
