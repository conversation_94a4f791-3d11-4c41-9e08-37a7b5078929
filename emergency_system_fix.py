#!/usr/bin/env python3
"""
Emergency System Fix for DEX900DN Trading System
Implements critical safety mechanisms to prevent system failures
"""

import os
import sys
import time
import logging
from datetime import datetime

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def create_emergency_stop():
    """Create emergency stop flag to halt the system"""
    try:
        emergency_file = "EMERGENCY_STOP.flag"
        with open(emergency_file, "w") as f:
            f.write(f"EMERGENCY_STOP:MANUAL_INTERVENTION:{datetime.now().isoformat()}")
        logger.critical(f"Emergency stop flag created: {emergency_file}")
        return True
    except Exception as e:
        logger.error(f"Failed to create emergency stop flag: {str(e)}")
        return False

def remove_emergency_stop():
    """Remove emergency stop flag to allow system restart"""
    try:
        emergency_file = "EMERGENCY_STOP.flag"
        if os.path.exists(emergency_file):
            os.remove(emergency_file)
            logger.info(f"Emergency stop flag removed: {emergency_file}")
            return True
        else:
            logger.info("No emergency stop flag found")
            return True
    except Exception as e:
        logger.error(f"Failed to remove emergency stop flag: {str(e)}")
        return False

def kill_all_trading_processes():
    """Kill all running trading processes"""
    try:
        import psutil
        
        killed_processes = []
        for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
            try:
                cmdline = ' '.join(proc.info['cmdline']) if proc.info['cmdline'] else ''
                if any(keyword in cmdline.lower() for keyword in ['dex900dn', 'trading', 'mt5']):
                    proc.terminate()
                    killed_processes.append(f"PID {proc.info['pid']}: {proc.info['name']}")
                    logger.info(f"Terminated process: PID {proc.info['pid']}")
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
        
        # Wait for processes to terminate
        time.sleep(2)
        
        # Force kill if still running
        for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
            try:
                cmdline = ' '.join(proc.info['cmdline']) if proc.info['cmdline'] else ''
                if any(keyword in cmdline.lower() for keyword in ['dex900dn', 'trading', 'mt5']):
                    proc.kill()
                    logger.warning(f"Force killed process: PID {proc.info['pid']}")
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
        
        return killed_processes
    except ImportError:
        logger.warning("psutil not available - cannot kill processes automatically")
        return []
    except Exception as e:
        logger.error(f"Error killing processes: {str(e)}")
        return []

def validate_system_state():
    """Validate current system state and identify issues"""
    issues = []
    
    # Check for emergency stop flag
    if os.path.exists("EMERGENCY_STOP.flag"):
        issues.append("Emergency stop flag exists")
    
    # Check for log files with errors
    log_dir = "logs"
    if os.path.exists(log_dir):
        for log_file in os.listdir(log_dir):
            if log_file.endswith('.log'):
                try:
                    with open(os.path.join(log_dir, log_file), 'r') as f:
                        content = f.read()
                        if "IMPOSSIBLE VOLATILITY DETECTED" in content:
                            issues.append(f"Data feed failure detected in {log_file}")
                        if "TRADE FLOOD" in content:
                            issues.append(f"Trade flooding detected in {log_file}")
                        if "POSITION TRACKING FAILURE" in content:
                            issues.append(f"Position tracking failure in {log_file}")
                except Exception:
                    continue
    
    return issues

def emergency_system_shutdown():
    """Complete emergency system shutdown"""
    logger.critical("🚨 INITIATING EMERGENCY SYSTEM SHUTDOWN")
    
    # Step 1: Create emergency stop flag
    logger.info("Step 1: Creating emergency stop flag...")
    create_emergency_stop()
    
    # Step 2: Kill all trading processes
    logger.info("Step 2: Terminating all trading processes...")
    killed = kill_all_trading_processes()
    if killed:
        logger.info(f"Terminated {len(killed)} processes")
        for proc in killed:
            logger.info(f"  - {proc}")
    
    # Step 3: Validate shutdown
    logger.info("Step 3: Validating system shutdown...")
    time.sleep(3)
    
    issues = validate_system_state()
    if issues:
        logger.warning("System issues detected:")
        for issue in issues:
            logger.warning(f"  - {issue}")
    
    logger.critical("🛑 EMERGENCY SHUTDOWN COMPLETE")
    return True

def emergency_system_restart():
    """Prepare system for safe restart"""
    logger.info("🔧 PREPARING SYSTEM FOR SAFE RESTART")
    
    # Step 1: Remove emergency stop flag
    logger.info("Step 1: Removing emergency stop flag...")
    remove_emergency_stop()
    
    # Step 2: Clear any problematic state files
    logger.info("Step 2: Clearing problematic state files...")
    state_files = ["trading_state.json", "position_cache.json", "risk_state.json"]
    for state_file in state_files:
        if os.path.exists(state_file):
            try:
                os.remove(state_file)
                logger.info(f"Removed state file: {state_file}")
            except Exception as e:
                logger.warning(f"Could not remove {state_file}: {str(e)}")
    
    # Step 3: Validate system readiness
    logger.info("Step 3: Validating system readiness...")
    issues = validate_system_state()
    if issues:
        logger.error("System still has issues:")
        for issue in issues:
            logger.error(f"  - {issue}")
        return False
    
    logger.info("✅ SYSTEM READY FOR SAFE RESTART")
    return True

def main():
    """Main emergency fix function"""
    if len(sys.argv) < 2:
        print("Usage: python emergency_system_fix.py [shutdown|restart|status]")
        sys.exit(1)
    
    command = sys.argv[1].lower()
    
    if command == "shutdown":
        emergency_system_shutdown()
    elif command == "restart":
        emergency_system_restart()
    elif command == "status":
        issues = validate_system_state()
        if issues:
            logger.warning("System issues detected:")
            for issue in issues:
                logger.warning(f"  - {issue}")
        else:
            logger.info("✅ No system issues detected")
    else:
        logger.error(f"Unknown command: {command}")
        sys.exit(1)

if __name__ == "__main__":
    main()
