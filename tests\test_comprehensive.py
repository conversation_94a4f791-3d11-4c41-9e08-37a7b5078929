"""
Comprehensive Test Suite for DEX900DN Trading System
Tests all current modules with edge cases and stress scenarios
"""

import sys
import os
import time
import numpy as np
from datetime import datetime, timedelta
import unittest

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))

from src.modules.cycle_engine import TimeCycleTracker, CycleState
from src.modules.data_feed import DataHandler, TickData, MarketState
from config.config import config

class TestCycleEngine(unittest.TestCase):
    """Comprehensive tests for cycle engine"""
    
    def setUp(self):
        self.tracker = TimeCycleTracker()
    
    def test_initial_state(self):
        """Test initial state is correct"""
        self.assertIsNone(self.tracker.state.cycle_start_time)
        self.assertIsNone(self.tracker.state.last_spike_time)
        self.assertEqual(self.tracker.state.current_cycle_duration, 0.0)
        self.assertFalse(self.tracker.state.is_in_spike_window)
        self.assertEqual(self.tracker.state.cycle_count, 0)
    
    def test_spike_countdown_start(self):
        """Test spike countdown initialization"""
        test_time = datetime.now()
        result = self.tracker.start_spike_countdown(test_time)
        
        self.assertTrue(result)
        self.assertEqual(self.tracker.state.last_spike_time, test_time)
        self.assertEqual(self.tracker.state.cycle_start_time, test_time)
        self.assertEqual(self.tracker.state.cycle_count, 1)
        self.assertFalse(self.tracker.state.is_in_spike_window)
    
    def test_spike_window_detection(self):
        """Test spike window detection at various times"""
        # Start cycle
        start_time = datetime.now() - timedelta(seconds=850)  # 14m 10s ago
        self.tracker.start_spike_countdown(start_time)
        
        # Should be in spike window
        in_window = self.tracker.spike_window_alert()
        self.assertTrue(in_window)
        self.assertTrue(self.tracker.state.is_in_spike_window)
        
        # Test outside window (too early)
        start_time = datetime.now() - timedelta(seconds=600)  # 10 minutes ago
        self.tracker.start_spike_countdown(start_time)
        in_window = self.tracker.spike_window_alert()
        self.assertFalse(in_window)
        
        # Test outside window (too late)
        start_time = datetime.now() - timedelta(seconds=1000)  # 16m 40s ago
        self.tracker.start_spike_countdown(start_time)
        in_window = self.tracker.spike_window_alert()
        self.assertFalse(in_window)
    
    def test_volatility_peak_tracking(self):
        """Test volatility peak tracking"""
        self.tracker.update_volatility_peak(0.05)
        self.assertEqual(self.tracker.state.volatility_peak, 0.05)
        
        # Higher volatility should update peak
        self.tracker.update_volatility_peak(0.15)
        self.assertEqual(self.tracker.state.volatility_peak, 0.15)
        
        # Lower volatility should not update peak
        self.tracker.update_volatility_peak(0.10)
        self.assertEqual(self.tracker.state.volatility_peak, 0.15)
    
    def test_cycle_reset_conditions(self):
        """Test cycle reset under various conditions"""
        # Set up cycle with volatility peak
        self.tracker.start_spike_countdown()
        self.tracker.update_volatility_peak(0.20)
        
        # High volatility should not reset
        reset_result = self.tracker.reset_cycle(0.15)
        self.assertFalse(reset_result)
        
        # Low volatility (<10% of peak) should reset
        reset_result = self.tracker.reset_cycle(0.01)  # 1% < 2% (10% of 20%)
        self.assertTrue(reset_result)
        
        # State should be reset
        self.assertIsNone(self.tracker.state.cycle_start_time)
        self.assertEqual(self.tracker.state.volatility_peak, 0.0)
    
    def test_cycle_progress_calculation(self):
        """Test cycle progress calculations"""
        # No active cycle
        progress = self.tracker.get_cycle_progress()
        self.assertFalse(progress['cycle_active'])
        
        # Active cycle
        start_time = datetime.now() - timedelta(seconds=450)  # 7.5 minutes ago
        self.tracker.start_spike_countdown(start_time)
        
        progress = self.tracker.get_cycle_progress()
        self.assertTrue(progress['cycle_active'])
        self.assertAlmostEqual(progress['progress_percent'], 50.0, delta=1.0)
        self.assertIsNotNone(progress['time_to_spike_window'])
    
    def test_multiple_cycles(self):
        """Test multiple cycle iterations"""
        for i in range(3):
            self.tracker.start_spike_countdown()
            self.assertEqual(self.tracker.state.cycle_count, i + 1)
            
            # Reset cycle
            self.tracker.update_volatility_peak(0.20)
            self.tracker.reset_cycle(0.01)

class TestDataHandler(unittest.TestCase):
    """Comprehensive tests for data handler"""
    
    def setUp(self):
        self.handler = DataHandler()
        self._populate_test_data()
    
    def _populate_test_data(self):
        """Populate handler with test data"""
        base_price = 25000.0
        base_time = datetime.now()
        
        for i in range(500):  # 500 data points
            price_change = np.sin(i * 0.1) * 50  # Sinusoidal price movement
            volume_change = 100 + (i % 50) * 2   # Varying volume
            
            tick = TickData(
                timestamp=base_time - timedelta(seconds=500-i),
                bid=base_price + price_change - 2.5,
                ask=base_price + price_change + 2.5,
                volume=volume_change,
                spread=5.0
            )
            
            self.handler.tick_buffer.append(tick)
            self.handler.price_buffer.append(tick.mid_price)
            self.handler.volume_buffer.append(tick.volume)
    
    def test_volatility_calculation_edge_cases(self):
        """Test volatility calculation with edge cases"""
        # Empty buffer
        empty_handler = DataHandler()
        volatility = empty_handler.calculate_volatility()
        self.assertEqual(volatility, 0.0)
        
        # Single data point
        tick = TickData(datetime.now(), 25000, 25005, 100, 5.0)
        empty_handler.price_buffer.append(tick.mid_price)
        volatility = empty_handler.calculate_volatility()
        self.assertEqual(volatility, 0.0)
        
        # Normal calculation
        volatility = self.handler.calculate_volatility(5)
        self.assertGreater(volatility, 0.0)
        self.assertIsInstance(volatility, float)
    
    def test_volume_spike_detection_scenarios(self):
        """Test volume spike detection in various scenarios"""
        # Clear existing data and add controlled test data
        self.handler.volume_buffer.clear()
        self.handler.tick_buffer.clear()
        
        # Add baseline volume data
        base_volume = 100
        for i in range(100):
            tick = TickData(
                timestamp=datetime.now() - timedelta(seconds=100-i),
                bid=25000, ask=25005, volume=base_volume, spread=5.0
            )
            self.handler.volume_buffer.append(tick.volume)
            self.handler.tick_buffer.append(tick)
        
        # Normal volume should not trigger spike
        normal_spike = self.handler.detect_volume_spike()
        self.assertFalse(normal_spike)
        
        # Add high volume tick
        high_volume_tick = TickData(
            timestamp=datetime.now(),
            bid=25000, ask=25005, volume=base_volume * 3, spread=5.0  # 3x baseline
        )
        self.handler.tick_buffer.append(high_volume_tick)
        
        # Should detect volume spike
        spike_detected = self.handler.detect_volume_spike()
        self.assertTrue(spike_detected)
    
    def test_price_drop_detection_accuracy(self):
        """Test price drop detection accuracy"""
        # Clear existing data
        self.handler.price_buffer.clear()
        
        # Add gradual price decline (should not trigger)
        base_price = 25000
        for i in range(20):
            price = base_price - (i * 10)  # 10 points per second decline
            self.handler.price_buffer.append(price)
        
        spike_detected, drop_amount = self.handler.detect_price_drop(500, 10)
        self.assertFalse(spike_detected)  # Only 100 point drop in 10 seconds
        
        # Add sharp price drop (should trigger)
        for i in range(10):
            price = base_price - 200 - (i * 60)  # 60 points per second decline
            self.handler.price_buffer.append(price)
        
        spike_detected, drop_amount = self.handler.detect_price_drop(500, 10)
        self.assertTrue(spike_detected)
        self.assertGreaterEqual(drop_amount, 500)
    
    def test_market_state_updates(self):
        """Test market state update functionality"""
        latest_tick = TickData(
            timestamp=datetime.now(),
            bid=25000, ask=25005, volume=150, spread=5.0
        )
        
        self.handler._update_market_state(latest_tick)
        
        self.assertEqual(self.handler.market_state.current_price, 25002.5)
        self.assertGreater(self.handler.market_state.volatility_5min, 0)
        self.assertIsInstance(self.handler.market_state.last_update, datetime)
    
    def test_data_buffer_limits(self):
        """Test data buffer size limits"""
        # Fill beyond buffer limit
        for i in range(config.LIVE_DATA_BUFFER_SIZE + 100):
            tick = TickData(datetime.now(), 25000, 25005, 100, 5.0)
            self.handler.tick_buffer.append(tick)
        
        # Should not exceed max size
        self.assertEqual(len(self.handler.tick_buffer), config.LIVE_DATA_BUFFER_SIZE)

class TestSystemIntegration(unittest.TestCase):
    """Integration tests between modules"""
    
    def setUp(self):
        self.tracker = TimeCycleTracker()
        self.handler = DataHandler()
    
    def test_spike_detection_cycle_integration(self):
        """Test integration between spike detection and cycle management"""
        # Simulate spike detection
        self.handler.price_buffer.clear()
        base_price = 25000
        
        # Add sharp price drop
        for i in range(10):
            price = base_price - (i * 60)  # 600 point drop in 10 seconds
            self.handler.price_buffer.append(price)
        
        spike_detected, drop_amount = self.handler.detect_price_drop()
        
        if spike_detected:
            # Start cycle based on spike
            self.tracker.start_spike_countdown()
            self.tracker.update_volatility_peak(0.20)  # High volatility during spike
            
            # Verify cycle started
            self.assertEqual(self.tracker.state.cycle_count, 1)
            self.assertIsNotNone(self.tracker.state.cycle_start_time)
            
            # Simulate volatility decline and cycle reset
            reset_result = self.tracker.reset_cycle(0.01)
            self.assertTrue(reset_result)
    
    def test_concurrent_data_processing(self):
        """Test handling multiple data updates"""
        # Simulate rapid data updates
        for i in range(100):
            # Add tick data
            tick = TickData(
                timestamp=datetime.now(),
                bid=25000 + (i % 10),
                ask=25005 + (i % 10),
                volume=100 + (i % 20),
                spread=5.0
            )
            
            self.handler.tick_buffer.append(tick)
            self.handler.price_buffer.append(tick.mid_price)
            self.handler.volume_buffer.append(tick.volume)
            
            # Update market state
            self.handler._update_market_state(tick)
            
            # Update cycle tracker
            if i == 50:  # Simulate spike at midpoint
                self.tracker.start_spike_countdown()
                self.tracker.update_volatility_peak(0.15)
        
        # Verify system state
        self.assertGreater(len(self.handler.tick_buffer), 0)
        self.assertEqual(self.tracker.state.cycle_count, 1)
        
        # Test system summary
        market_summary = self.handler.get_market_summary()
        cycle_progress = self.tracker.get_cycle_progress()
        
        self.assertIn('current_price', market_summary)
        self.assertIn('cycle_active', cycle_progress)

def run_stress_tests():
    """Run stress tests on the system"""
    print("\n" + "="*60)
    print("RUNNING STRESS TESTS")
    print("="*60)
    
    # Test 1: High-frequency data processing
    print("\n1. High-frequency data processing test...")
    handler = DataHandler()
    start_time = time.time()
    
    for i in range(10000):  # 10,000 rapid updates
        tick = TickData(
            timestamp=datetime.now(),
            bid=25000 + np.random.normal(0, 10),
            ask=25005 + np.random.normal(0, 10),
            volume=100 + np.random.randint(0, 50),
            spread=5.0
        )
        handler.tick_buffer.append(tick)
        handler.price_buffer.append(tick.mid_price)
        
        if i % 1000 == 0:
            volatility = handler.calculate_volatility()
            volume_spike = handler.detect_volume_spike()
    
    processing_time = time.time() - start_time
    print(f"   Processed 10,000 ticks in {processing_time:.2f} seconds")
    print(f"   Rate: {10000/processing_time:.0f} ticks/second")
    
    # Test 2: Memory usage with large datasets
    print("\n2. Memory usage test...")
    tracker = TimeCycleTracker()
    
    for i in range(1000):  # 1000 cycles
        tracker.start_spike_countdown()
        tracker.update_volatility_peak(np.random.uniform(0.05, 0.25))
        tracker.reset_cycle(0.01)
    
    print(f"   Completed 1000 cycle iterations")
    print(f"   Final cycle count: {tracker.state.cycle_count}")
    
    print("\nStress tests completed successfully!")

def main():
    """Run all comprehensive tests"""
    print("DEX900DN TRADING SYSTEM - COMPREHENSIVE TEST SUITE")
    print("="*60)
    
    # Run unit tests
    unittest.main(argv=[''], exit=False, verbosity=2)
    
    # Run stress tests
    run_stress_tests()
    
    print("\n" + "="*60)
    print("ALL COMPREHENSIVE TESTS COMPLETED!")
    print("="*60)

if __name__ == "__main__":
    main()
