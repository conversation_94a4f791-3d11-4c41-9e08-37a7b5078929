"""
Calm Phase Trading Strategy for DEX900DN
Long-only scalping strategy using Bollinger Bands + RSI
Entry: BB(20, σ=1) + RSI(14)>50
Exit: 50-100pt TP / 300pt SL
"""

import logging
import numpy as np
import pandas as pd
from datetime import datetime, timed<PERSON><PERSON>
from typing import Dict, Any, Optional, Tuple, List
from dataclasses import dataclass
from enum import Enum

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))
from config.config import config
from src.modules.data_feed import DataHandler, TickData

# Setup logging
logging.basicConfig(level=getattr(logging, config.LOG_LEVEL))
logger = logging.getLogger(__name__)

class SignalType(Enum):
    """Trading signal types"""
    NO_SIGNAL = "NO_SIGNAL"
    ENTRY_LONG = "ENTRY_LONG"
    EXIT_PROFIT = "EXIT_PROFIT"
    EXIT_LOSS = "EXIT_LOSS"

@dataclass
class TradingSignal:
    """Trading signal data structure"""
    signal_type: SignalType
    price: float
    timestamp: datetime
    confidence: float
    reasoning: str
    stop_loss: Optional[float] = None
    take_profit: Optional[float] = None
    position_size: Optional[float] = None

@dataclass
class Position:
    """Active position tracking"""
    entry_price: float
    entry_time: datetime
    position_size: float
    stop_loss: float
    take_profit: float
    current_pnl: float = 0.0
    max_favorable: float = 0.0
    max_adverse: float = 0.0

class CalmPhaseStrategy:
    """
    Calm Phase Engine for DEX900DN Trading System
    
    Strategy Specifications:
    - Entry Trigger: Bollinger Bands (20-period, σ=1) + RSI(14)>50
    - Position: Long-only
    - Exit: 50-100pt TP / 300pt SL
    - Risk: 5% per trade (from config.CALM_PHASE_RISK)
    """
    
    def __init__(self, data_handler: DataHandler = None):
        self.data_handler = data_handler
        
        # Strategy parameters from config
        self.bb_period = config.BB_PERIOD  # 20
        self.bb_deviation = config.BB_DEVIATION  # 1.0
        self.rsi_period = config.RSI_PERIOD  # 14
        self.rsi_threshold = config.RSI_THRESHOLD  # 50
        
        # P/L parameters
        self.take_profit_min = config.CALM_TAKE_PROFIT_MIN  # 50 points
        self.take_profit_max = config.CALM_TAKE_PROFIT_MAX  # 100 points
        self.stop_loss = config.CALM_STOP_LOSS  # 300 points
        
        # Position management
        self.current_position: Optional[Position] = None
        self.position_history: List[Position] = []
        self.risk_per_trade = config.CALM_PHASE_RISK  # 0.05 (5%)
        
        # Technical indicators cache
        self.last_bb_calculation = None
        self.last_rsi_calculation = None
        self.indicator_cache_time = None
        
        logger.info("CalmPhaseStrategy initialized")
        logger.info(f"BB Parameters: {self.bb_period} period, {self.bb_deviation} deviation")
        logger.info(f"RSI Parameters: {self.rsi_period} period, >{self.rsi_threshold} threshold")
        logger.info(f"P/L: {self.take_profit_min}-{self.take_profit_max}pt TP, {self.stop_loss}pt SL")
    
    def calculate_bollinger_bands(self, window: int = None) -> Tuple[float, float, float]:
        """
        Calculate Bollinger Bands (Middle, Upper, Lower)
        
        Returns:
            Tuple[middle_band, upper_band, lower_band]
        """
        if not self.data_handler or len(self.data_handler.price_buffer) < self.bb_period:
            return 0.0, 0.0, 0.0
        
        window = window or self.bb_period
        prices = list(self.data_handler.price_buffer)[-window:]
        
        if len(prices) < window:
            return 0.0, 0.0, 0.0
        
        # Calculate moving average (middle band)
        middle_band = np.mean(prices)
        
        # Calculate standard deviation
        std_dev = np.std(prices)
        
        # Calculate upper and lower bands
        upper_band = middle_band + (self.bb_deviation * std_dev)
        lower_band = middle_band - (self.bb_deviation * std_dev)
        
        return middle_band, upper_band, lower_band
    
    def calculate_rsi(self, window: int = None) -> float:
        """
        Calculate RSI (Relative Strength Index)

        Returns:
            float: RSI value (0-100)
        """
        if not self.data_handler or len(self.data_handler.price_buffer) < self.rsi_period + 1:
            return 50.0  # Neutral RSI

        window = window or self.rsi_period
        prices = list(self.data_handler.price_buffer)[-(window + 1):]

        if len(prices) < window + 1:
            return 50.0

        # Calculate price changes
        price_changes = np.diff(prices)

        # Separate gains and losses
        gains = np.where(price_changes > 0, price_changes, 0)
        losses = np.where(price_changes < 0, -price_changes, 0)

        # Calculate average gains and losses
        avg_gain = np.mean(gains)
        avg_loss = np.mean(losses)

        # CRITICAL: Fix impossible RSI=100.0 calculation
        if avg_loss == 0:
            # If no losses, RSI should be high but not exactly 100
            logger.warning("No losses detected in RSI calculation - using 95.0 instead of impossible 100.0")
            return 95.0  # High but realistic RSI

        # Calculate RSI
        rs = avg_gain / avg_loss
        rsi = 100 - (100 / (1 + rs))

        # CRITICAL: Validate RSI range (impossible to reach exactly 100.0)
        if rsi >= 100.0:
            logger.critical(f"IMPOSSIBLE RSI DETECTED: {rsi:.1f} - Clamping to 99.9")
            rsi = 99.9  # Maximum realistic RSI
        elif rsi < 0:
            logger.critical(f"INVALID RSI DETECTED: {rsi:.1f} - Clamping to 0.1")
            rsi = 0.1  # Minimum realistic RSI

        return rsi
    
    def scalp_entry_signal(self) -> TradingSignal:
        """
        Generate entry signal based on BB + RSI criteria
        
        Returns:
            TradingSignal: Entry signal or NO_SIGNAL
        """
        if not self.data_handler or len(self.data_handler.price_buffer) < max(self.bb_period, self.rsi_period + 1):
            return TradingSignal(
                signal_type=SignalType.NO_SIGNAL,
                price=0.0,
                timestamp=datetime.now(),
                confidence=0.0,
                reasoning="Insufficient data for analysis"
            )
        
        # Get current price
        current_price = list(self.data_handler.price_buffer)[-1]
        current_time = datetime.now()
        
        # Calculate technical indicators
        middle_band, upper_band, lower_band = self.calculate_bollinger_bands()
        rsi = self.calculate_rsi()
        
        # Cache indicators
        self.last_bb_calculation = (middle_band, upper_band, lower_band)
        self.last_rsi_calculation = rsi
        self.indicator_cache_time = current_time
        
        # Entry criteria: Price near lower BB + RSI > 50
        bb_signal = current_price <= lower_band * 1.001  # Allow 0.1% tolerance
        rsi_signal = rsi > self.rsi_threshold
        
        if bb_signal and rsi_signal:
            # Calculate position sizing
            position_size = self._calculate_position_size(current_price)
            
            # Calculate stop loss and take profit
            stop_loss_price = current_price - self.stop_loss

            # CRITICAL FIX: Enhanced TP calculation for synthetic indices
            # Minimum buffer to prevent instant TP hits on high-frequency ticks
            MIN_TP_DISTANCE = 100  # Minimum 100pt buffer for DEX900DN

            # Get current spread for additional buffer
            try:
                symbol_info = self.data_handler.mt5_connection.get_symbol_info()
                current_spread = symbol_info.spread if symbol_info else 10
                spread_buffer = max(current_spread * 3, 20)  # 3x spread or 20pt minimum
            except:
                spread_buffer = 20  # Fallback buffer

            # Dynamic take profit with enhanced minimum distance
            bb_width = upper_band - lower_band
            base_tp = max(self.take_profit_min, min(self.take_profit_max, bb_width * 0.5))

            # Apply minimum distance + spread buffer
            enhanced_tp = max(base_tp, MIN_TP_DISTANCE + spread_buffer)
            take_profit_price = current_price + enhanced_tp

            logger.debug(f"TP calculation: base={base_tp:.1f}pt, enhanced={enhanced_tp:.1f}pt, spread_buffer={spread_buffer:.1f}pt")
            
            confidence = self._calculate_entry_confidence(current_price, lower_band, rsi)
            
            reasoning = (f"BB Entry: Price {current_price:.1f} near lower band {lower_band:.1f}, "
                        f"RSI {rsi:.1f} > {self.rsi_threshold}, "
                        f"TP: {enhanced_tp:.1f}pts")
            
            return TradingSignal(
                signal_type=SignalType.ENTRY_LONG,
                price=current_price,
                timestamp=current_time,
                confidence=confidence,
                reasoning=reasoning,
                stop_loss=stop_loss_price,
                take_profit=take_profit_price,
                position_size=position_size
            )
        
        # No entry signal
        reasoning_parts = []
        if not bb_signal:
            reasoning_parts.append(f"Price {current_price:.1f} not near lower BB {lower_band:.1f}")
        if not rsi_signal:
            reasoning_parts.append(f"RSI {rsi:.1f} <= {self.rsi_threshold}")
        
        return TradingSignal(
            signal_type=SignalType.NO_SIGNAL,
            price=current_price,
            timestamp=current_time,
            confidence=0.0,
            reasoning=" + ".join(reasoning_parts)
        )
    
    def calm_exit_strategy(self) -> TradingSignal:
        """
        Generate exit signal for current position
        
        Returns:
            TradingSignal: Exit signal or NO_SIGNAL
        """
        if not self.current_position:
            return TradingSignal(
                signal_type=SignalType.NO_SIGNAL,
                price=0.0,
                timestamp=datetime.now(),
                confidence=0.0,
                reasoning="No active position"
            )
        
        if not self.data_handler or len(self.data_handler.price_buffer) == 0:
            return TradingSignal(
                signal_type=SignalType.NO_SIGNAL,
                price=0.0,
                timestamp=datetime.now(),
                confidence=0.0,
                reasoning="No price data available"
            )
        
        current_price = list(self.data_handler.price_buffer)[-1]
        current_time = datetime.now()
        
        # Update position P/L
        self._update_position_pnl(current_price)
        
        # Check stop loss
        if current_price <= self.current_position.stop_loss:
            return TradingSignal(
                signal_type=SignalType.EXIT_LOSS,
                price=current_price,
                timestamp=current_time,
                confidence=1.0,
                reasoning=f"Stop loss hit: {current_price:.1f} <= {self.current_position.stop_loss:.1f}"
            )
        
        # Check take profit
        if current_price >= self.current_position.take_profit:
            return TradingSignal(
                signal_type=SignalType.EXIT_PROFIT,
                price=current_price,
                timestamp=current_time,
                confidence=1.0,
                reasoning=f"Take profit hit: {current_price:.1f} >= {self.current_position.take_profit:.1f}"
            )
        
        # Check for early exit conditions (optional)
        # Could add RSI overbought, BB upper band touch, etc.
        
        return TradingSignal(
            signal_type=SignalType.NO_SIGNAL,
            price=current_price,
            timestamp=current_time,
            confidence=0.0,
            reasoning=f"Position held: P/L {self.current_position.current_pnl:.1f}pts"
        )
    
    def _calculate_position_size(self, entry_price: float) -> float:
        """Calculate position size based on risk management"""
        # For demo purposes, return fixed size
        # In real implementation, this would calculate based on account balance
        return self.risk_per_trade
    
    def _calculate_entry_confidence(self, current_price: float, lower_band: float, rsi: float) -> float:
        """Calculate confidence score for entry signal"""
        # Distance from lower band (closer = higher confidence)
        bb_distance = abs(current_price - lower_band) / lower_band
        bb_confidence = max(0.0, 1.0 - (bb_distance * 10))  # Normalize
        
        # RSI strength (higher RSI = higher confidence, but cap at reasonable level)
        rsi_confidence = min(1.0, (rsi - self.rsi_threshold) / 30.0)  # Normalize 50-80 range
        
        # Combined confidence
        confidence = (bb_confidence * 0.6) + (rsi_confidence * 0.4)
        return max(0.1, min(0.95, confidence))
    
    def _update_position_pnl(self, current_price: float) -> None:
        """Update current position P/L and tracking metrics"""
        if not self.current_position:
            return
        
        # Calculate current P/L in points
        pnl_points = current_price - self.current_position.entry_price
        self.current_position.current_pnl = pnl_points
        
        # Track maximum favorable and adverse excursions
        if pnl_points > self.current_position.max_favorable:
            self.current_position.max_favorable = pnl_points
        
        if pnl_points < self.current_position.max_adverse:
            self.current_position.max_adverse = pnl_points
    
    def open_position(self, signal: TradingSignal) -> bool:
        """
        Open a new position based on entry signal
        
        Returns:
            bool: True if position opened successfully
        """
        if self.current_position:
            logger.warning("Cannot open position: existing position active")
            return False
        
        if signal.signal_type != SignalType.ENTRY_LONG:
            logger.warning(f"Cannot open position: invalid signal type {signal.signal_type}")
            return False
        
        self.current_position = Position(
            entry_price=signal.price,
            entry_time=signal.timestamp,
            position_size=signal.position_size or self.risk_per_trade,
            stop_loss=signal.stop_loss,
            take_profit=signal.take_profit
        )
        
        logger.info(f"CALM LONG OPENED: {signal.price:.1f} | SL: {signal.stop_loss:.1f} | TP: {signal.take_profit:.1f}")
        return True
    
    def close_position(self, signal: TradingSignal) -> bool:
        """
        Close current position based on exit signal
        
        Returns:
            bool: True if position closed successfully
        """
        if not self.current_position:
            logger.warning("Cannot close position: no active position")
            return False
        
        # Update final P/L
        self._update_position_pnl(signal.price)
        
        # Log position closure
        exit_type = "PROFIT" if signal.signal_type == SignalType.EXIT_PROFIT else "LOSS"
        logger.info(f"CALM LONG CLOSED ({exit_type}): {signal.price:.1f} | P/L: {self.current_position.current_pnl:.1f}pts")
        
        # Archive position
        self.position_history.append(self.current_position)
        self.current_position = None
        
        return True
    
    def get_strategy_summary(self) -> Dict[str, Any]:
        """Get comprehensive strategy status summary"""
        current_price = 0.0
        if self.data_handler and len(self.data_handler.price_buffer) > 0:
            current_price = list(self.data_handler.price_buffer)[-1]
        
        # Calculate performance metrics
        total_trades = len(self.position_history)
        winning_trades = sum(1 for pos in self.position_history if pos.current_pnl > 0)
        total_pnl = sum(pos.current_pnl for pos in self.position_history)
        
        return {
            "strategy_name": "Calm Phase Long Scalping",
            "current_price": current_price,
            "active_position": self.current_position is not None,
            "position_pnl": self.current_position.current_pnl if self.current_position else 0.0,
            "total_trades": total_trades,
            "winning_trades": winning_trades,
            "win_rate": (winning_trades / total_trades * 100) if total_trades > 0 else 0.0,
            "total_pnl": total_pnl,
            "last_bb": self.last_bb_calculation,
            "last_rsi": self.last_rsi_calculation,
            "indicator_cache_time": self.indicator_cache_time
        }
