#!/usr/bin/env python3
"""
Emergency Verification Test for DEX900DN Trading System
Tests all critical fixes before system relaunch
"""

import sys
import os
import time
import numpy as np
from datetime import datetime, timedelta

# Add src to path
sys.path.append('src')

from modules.data_feed import <PERSON><PERSON><PERSON><PERSON>, DataIntegrityError
from strategies.strategy_manager import StrategyManager
from strategies.calm_strategy import CalmPhaseStrategy
from risk.monte_carlo import MonteCarloSimulator
from execution.position_manager import PositionManager

def test_data_feed_validation():
    """Test data feed validation fixes"""
    print("🔍 Testing Data Feed Validation...")
    
    handler = DataHandler()
    
    # Test 1: Volatility validation
    try:
        handler.validate_indicators(0.0, 50.0)  # Should fail
        print("❌ FAILED: Volatility 0.0 not detected")
        return False
    except DataIntegrityError:
        print("✅ PASSED: Volatility 0.0 correctly detected")
    
    # Test 2: RSI validation
    try:
        handler.validate_indicators(0.05, 100.0)  # Should fail
        print("❌ FAILED: RSI 100.0 not detected")
        return False
    except DataIntegrityError:
        print("✅ PASSED: RSI 100.0 correctly detected")
    
    # Test 3: Valid values
    try:
        handler.validate_indicators(0.05, 75.5)  # Should pass
        print("✅ PASSED: Valid values accepted")
    except DataIntegrityError:
        print("❌ FAILED: Valid values rejected")
        return False
    
    return True

def test_trade_flood_prevention():
    """Test trade flood prevention"""
    print("🚫 Testing Trade Flood Prevention...")
    
    manager = StrategyManager()
    
    # Test timing constraint
    manager.last_trade_time = datetime.now()
    time_since = (datetime.now() - manager.last_trade_time).total_seconds()
    
    if time_since < manager.min_trade_interval:
        print(f"✅ PASSED: Trade interval check working ({time_since:.1f}s < {manager.min_trade_interval}s)")
    else:
        print("❌ FAILED: Trade interval check not working")
        return False
    
    # Test position limit
    if manager.max_concurrent_positions == 2:
        print("✅ PASSED: Position limit set to 2")
    else:
        print(f"❌ FAILED: Position limit is {manager.max_concurrent_positions}, should be 2")
        return False
    
    return True

def test_monte_carlo_optimization():
    """Test Monte Carlo optimization"""
    print("⚡ Testing Monte Carlo Optimization...")
    
    simulator = MonteCarloSimulator()
    
    # Test simulation counts
    if simulator.calm_simulation_count == 20:
        print("✅ PASSED: CALM simulations reduced to 20")
    else:
        print(f"❌ FAILED: CALM simulations is {simulator.calm_simulation_count}, should be 20")
        return False
    
    if simulator.spike_simulation_count == 200:
        print("✅ PASSED: SPIKE simulations maintained at 200")
    else:
        print(f"❌ FAILED: SPIKE simulations is {simulator.spike_simulation_count}, should be 200")
        return False
    
    return True

def test_rsi_calculation_fix():
    """Test RSI calculation fix"""
    print("📊 Testing RSI Calculation Fix...")
    
    strategy = CalmPhaseStrategy()
    
    # Create mock data handler with price buffer
    class MockDataHandler:
        def __init__(self):
            # Create price data that would normally give RSI=100
            self.price_buffer = [100.0] * 15  # All same prices (no losses)
    
    strategy.data_handler = MockDataHandler()
    
    rsi = strategy.calculate_rsi()
    
    if rsi < 100.0:
        print(f"✅ PASSED: RSI capped at {rsi:.1f} (was 100.0)")
    else:
        print(f"❌ FAILED: RSI still shows impossible {rsi:.1f}")
        return False
    
    return True

def test_position_tracking_fix():
    """Test position tracking improvements"""
    print("📍 Testing Position Tracking Fix...")
    
    manager = PositionManager()
    
    # Test emergency reconnect method exists
    if hasattr(manager, '_emergency_mt5_reconnect'):
        print("✅ PASSED: Emergency reconnect method added")
    else:
        print("❌ FAILED: Emergency reconnect method missing")
        return False
    
    return True

def run_performance_test():
    """Run a quick performance test"""
    print("🏃 Running Performance Test...")
    
    simulator = MonteCarloSimulator()
    
    start_time = time.time()
    
    # Run CALM phase simulation (should be fast)
    risk_metrics = simulator.run_comprehensive_risk_assessment(
        "CALM", 67000.0, 0.05
    )
    
    end_time = time.time()
    duration = end_time - start_time
    
    if duration < 5.0:  # Should complete in under 5 seconds
        print(f"✅ PASSED: CALM simulation completed in {duration:.2f}s")
    else:
        print(f"❌ FAILED: CALM simulation took {duration:.2f}s (too slow)")
        return False
    
    return True

def main():
    """Run all emergency verification tests"""
    print("🚨 EMERGENCY VERIFICATION TEST SUITE")
    print("=" * 50)
    
    tests = [
        ("Data Feed Validation", test_data_feed_validation),
        ("Trade Flood Prevention", test_trade_flood_prevention),
        ("Monte Carlo Optimization", test_monte_carlo_optimization),
        ("RSI Calculation Fix", test_rsi_calculation_fix),
        ("Position Tracking Fix", test_position_tracking_fix),
        ("Performance Test", run_performance_test),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🧪 {test_name}")
        print("-" * 30)
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name}: PASSED")
            else:
                print(f"❌ {test_name}: FAILED")
        except Exception as e:
            print(f"❌ {test_name}: ERROR - {str(e)}")
    
    print("\n" + "=" * 50)
    print(f"📊 VERIFICATION RESULTS: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 ALL TESTS PASSED - SYSTEM READY FOR RELAUNCH")
        return True
    else:
        print("🚨 SOME TESTS FAILED - DO NOT RELAUNCH SYSTEM")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
