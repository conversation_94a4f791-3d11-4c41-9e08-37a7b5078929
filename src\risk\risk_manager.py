"""
Integrated Risk Manager for DEX900DN Trading System
Central risk management hub integrating Monte Carlo, position sizing, and risk metrics
"""

import logging
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))
from config.config import config
from src.risk.monte_carlo import MonteCarloSimulator, RiskMetrics
from src.risk.position_sizing import AdvancedPositionSizer, SizingMethod, AccountInfo, PositionSizeResult
from src.risk.risk_metrics import RiskMetricsCalculator, PerformanceMetrics, RiskAlert

# Setup logging
logging.basicConfig(level=getattr(logging, config.LOG_LEVEL))
logger = logging.getLogger(__name__)

@dataclass
class RiskDecision:
    """Risk management decision"""
    allow_trade: bool
    position_size: float
    reasoning: str
    risk_level: str
    confidence: float
    recommended_stop_loss: Optional[float] = None
    recommended_take_profit: Optional[float] = None
    alerts: List[RiskAlert] = None

class IntegratedRiskManager:
    """
    Central Risk Management System for DEX900DN Trading
    
    Integrates:
    - Monte Carlo simulation for risk assessment
    - Advanced position sizing algorithms
    - Real-time risk metrics monitoring
    - Risk-based trading decisions
    """
    
    def __init__(self, account_info: AccountInfo = None):
        # Initialize components
        self.monte_carlo = MonteCarloSimulator()
        self.position_sizer = AdvancedPositionSizer(self.monte_carlo)
        self.risk_calculator = RiskMetricsCalculator()
        
        # Account information
        self.account_info = account_info or AccountInfo(
            balance=10000.0,
            equity=10000.0,
            margin_used=0.0,
            margin_available=10000.0,
            max_risk_per_trade=0.02,
            max_portfolio_risk=0.06
        )
        
        # Risk state tracking
        self.active_positions = []
        self.daily_trades = 0
        self.daily_pnl = 0.0
        self.last_risk_assessment = None
        
        # Risk limits
        self.max_daily_trades = 10
        self.max_consecutive_losses = 5
        self.emergency_stop_loss = 0.05  # 5% account emergency stop
        
        # Performance tracking
        self.trade_history = []
        
        logger.info("IntegratedRiskManager initialized")
    
    def assess_trade_risk(self, strategy_type: str, entry_price: float,
                         stop_loss: float, take_profit: float,
                         current_volatility: float,
                         market_conditions: Dict[str, Any] = None) -> RiskDecision:
        """
        Comprehensive trade risk assessment
        
        Args:
            strategy_type: 'CALM' or 'SPIKE'
            entry_price: Proposed entry price
            stop_loss: Proposed stop loss
            take_profit: Proposed take profit
            current_volatility: Current market volatility
            market_conditions: Additional market data
            
        Returns:
            RiskDecision with trade approval and sizing
        """
        market_conditions = market_conditions or {}
        
        logger.debug(f"Assessing trade risk: {strategy_type} at {entry_price}")
        
        # Step 1: Check basic risk limits
        basic_check = self._check_basic_limits()
        if not basic_check['allow']:
            return RiskDecision(
                allow_trade=False,
                position_size=0.0,
                reasoning=basic_check['reason'],
                risk_level="HIGH",
                confidence=1.0
            )
        
        # Step 2: Run Monte Carlo risk assessment
        mc_strategy_type = 'LONG' if strategy_type == 'CALM' else 'SHORT'
        try:
            risk_metrics = self.monte_carlo.run_comprehensive_risk_assessment(
                mc_strategy_type, entry_price, current_volatility, num_simulations=1000
            )
            
            # Step 3: Optimize position sizing
            recommended_method = self.position_sizer.get_recommended_sizing_method(
                strategy_type, market_conditions
            )
            
            position_result = self.position_sizer.calculate_position_size(
                strategy_type, entry_price, stop_loss, take_profit,
                current_volatility, self.account_info, recommended_method
            )
            
            # Step 4: Validate position size
            is_valid, validation_reason = self.position_sizer.validate_position_size(
                position_result.position_size, self.account_info, self.active_positions
            )
            
            if not is_valid:
                return RiskDecision(
                    allow_trade=False,
                    position_size=0.0,
                    reasoning=f"Position validation failed: {validation_reason}",
                    risk_level="HIGH",
                    confidence=1.0
                )
            
            # Step 5: Calculate overall risk level
            risk_level = self._calculate_risk_level(risk_metrics, position_result, current_volatility)
            
            # Step 6: Make final decision
            allow_trade = self._make_trade_decision(risk_level, risk_metrics, position_result)
            
            # Step 7: Generate reasoning
            reasoning = self._generate_reasoning(
                risk_metrics, position_result, risk_level, allow_trade
            )
            
            # Step 8: Optimize stop loss if needed
            optimized_sl = self._optimize_stop_loss(
                mc_strategy_type, entry_price, take_profit, current_volatility
            )
            
            return RiskDecision(
                allow_trade=allow_trade,
                position_size=position_result.position_size if allow_trade else 0.0,
                reasoning=reasoning,
                risk_level=risk_level,
                confidence=position_result.confidence,
                recommended_stop_loss=optimized_sl,
                recommended_take_profit=take_profit
            )
            
        except Exception as e:
            logger.error(f"Risk assessment failed: {e}")
            return RiskDecision(
                allow_trade=False,
                position_size=0.0,
                reasoning=f"Risk assessment error: {str(e)}",
                risk_level="HIGH",
                confidence=0.0
            )
    
    def _check_basic_limits(self) -> Dict[str, Any]:
        """Check basic risk limits before detailed analysis"""
        
        # Daily trade limit
        if self.daily_trades >= self.max_daily_trades:
            return {'allow': False, 'reason': f"Daily trade limit reached: {self.daily_trades}"}
        
        # Daily P/L limit
        daily_pnl_percent = self.daily_pnl / self.account_info.balance
        if daily_pnl_percent <= -self.account_info.max_risk_per_trade * 3:  # 3x daily risk
            return {'allow': False, 'reason': f"Daily loss limit exceeded: {daily_pnl_percent:.2%}"}
        
        # Emergency stop loss
        if daily_pnl_percent <= -self.emergency_stop_loss:
            return {'allow': False, 'reason': f"Emergency stop triggered: {daily_pnl_percent:.2%}"}
        
        # Maximum positions
        if len(self.active_positions) >= 2:  # Max 2 concurrent positions
            return {'allow': False, 'reason': "Maximum concurrent positions reached"}
        
        # Consecutive losses check
        recent_trades = self.trade_history[-self.max_consecutive_losses:]
        if (len(recent_trades) >= self.max_consecutive_losses and 
            all(trade.get('pnl', 0) < 0 for trade in recent_trades)):
            return {'allow': False, 'reason': f"Consecutive losses limit: {self.max_consecutive_losses}"}
        
        return {'allow': True, 'reason': 'Basic limits passed'}
    
    def _calculate_risk_level(self, risk_metrics: RiskMetrics, 
                            position_result: PositionSizeResult,
                            volatility: float) -> str:
        """Calculate overall risk level for the trade"""
        
        risk_factors = 0
        
        # High volatility
        if volatility > 0.20:
            risk_factors += 1
        
        # Poor risk metrics
        if risk_metrics.sharpe_ratio < 0.5:
            risk_factors += 1
        
        # High VaR
        if risk_metrics.var_95 > 0.05:
            risk_factors += 1
        
        # Low win rate
        if risk_metrics.win_rate < 0.45:
            risk_factors += 1
        
        # Large position size
        if position_result.position_size > 0.05:
            risk_factors += 1
        
        # High maximum drawdown
        if abs(risk_metrics.max_drawdown) > 0.10:
            risk_factors += 1
        
        if risk_factors >= 4:
            return "HIGH"
        elif risk_factors >= 2:
            return "MEDIUM"
        else:
            return "LOW"
    
    def _make_trade_decision(self, risk_level: str, risk_metrics: RiskMetrics,
                           position_result: PositionSizeResult) -> bool:
        """Make final trade decision based on risk assessment"""
        
        # Never trade on HIGH risk
        if risk_level == "HIGH":
            return False
        
        # Conservative approach for MEDIUM risk
        if risk_level == "MEDIUM":
            # Require high confidence and good metrics
            return (position_result.confidence > 0.7 and 
                   risk_metrics.win_rate > 0.5 and
                   risk_metrics.sharpe_ratio > 0.3)
        
        # LOW risk - trade if confidence is reasonable
        return position_result.confidence > 0.6
    
    def _generate_reasoning(self, risk_metrics: RiskMetrics, 
                          position_result: PositionSizeResult,
                          risk_level: str, allow_trade: bool) -> str:
        """Generate human-readable reasoning for the decision"""
        
        reasoning_parts = []
        
        # Risk level
        reasoning_parts.append(f"Risk level: {risk_level}")
        
        # Key metrics
        reasoning_parts.append(f"Win rate: {risk_metrics.win_rate:.1%}")
        reasoning_parts.append(f"Sharpe: {risk_metrics.sharpe_ratio:.2f}")
        reasoning_parts.append(f"VaR 95%: {risk_metrics.var_95:.2%}")
        
        # Position sizing
        reasoning_parts.append(f"Position size: {position_result.position_size:.3f}")
        reasoning_parts.append(f"Sizing method: {position_result.sizing_method.value}")
        
        # Decision
        decision_text = "APPROVED" if allow_trade else "REJECTED"
        reasoning_parts.append(f"Decision: {decision_text}")
        
        return " | ".join(reasoning_parts)
    
    def _optimize_stop_loss(self, strategy_type: str, entry_price: float,
                          take_profit: float, volatility: float) -> float:
        """Optimize stop loss using Monte Carlo simulation"""
        
        try:
            optimization = self.monte_carlo.optimize_stop_loss(
                strategy_type, entry_price, take_profit, volatility, num_simulations=500
            )
            return optimization['optimal_stop_loss']
        except Exception as e:
            logger.warning(f"Stop loss optimization failed: {e}")
            # Return default stop loss
            if strategy_type == 'LONG':
                return entry_price - config.CALM_STOP_LOSS
            else:
                return entry_price + config.SPIKE_STOP_LOSS_MIN
    
    def update_position(self, position_id: str, current_pnl: float,
                       position_data: Dict[str, Any]) -> None:
        """Update active position and risk metrics"""
        
        # Update position in active list
        for i, pos in enumerate(self.active_positions):
            if pos.get('id') == position_id:
                self.active_positions[i].update({
                    'current_pnl': current_pnl,
                    'last_update': datetime.now(),
                    **position_data
                })
                break
        
        # Update risk calculator
        self.risk_calculator.update_performance(current_pnl)
        
        # Update daily P/L
        self.daily_pnl += current_pnl
    
    def close_position(self, position_id: str, final_pnl: float,
                      close_reason: str) -> None:
        """Close position and update records"""
        
        # Remove from active positions
        closed_position = None
        for i, pos in enumerate(self.active_positions):
            if pos.get('id') == position_id:
                closed_position = self.active_positions.pop(i)
                break
        
        if closed_position:
            # Add to trade history
            trade_record = {
                'id': position_id,
                'entry_time': closed_position.get('entry_time'),
                'close_time': datetime.now(),
                'pnl': final_pnl,
                'close_reason': close_reason,
                'strategy': closed_position.get('strategy'),
                'entry_price': closed_position.get('entry_price'),
                'close_price': closed_position.get('close_price')
            }
            
            self.trade_history.append(trade_record)
            self.daily_trades += 1
            
            logger.info(f"Position closed: {position_id} | P/L: {final_pnl:.2f} | Reason: {close_reason}")
    
    def get_risk_dashboard(self) -> Dict[str, Any]:
        """Get comprehensive risk dashboard"""
        
        # Get performance metrics
        performance = self.risk_calculator.calculate_comprehensive_metrics(self.trade_history)
        risk_dashboard = self.risk_calculator.get_risk_dashboard()
        
        # Current account status
        account_status = {
            'balance': self.account_info.balance,
            'equity': self.account_info.equity,
            'daily_pnl': self.daily_pnl,
            'daily_pnl_percent': self.daily_pnl / self.account_info.balance,
            'margin_used': self.account_info.margin_used,
            'margin_available': self.account_info.margin_available,
            'active_positions': len(self.active_positions),
            'daily_trades': self.daily_trades
        }
        
        # Risk limits status
        limits_status = {
            'daily_trade_limit': f"{self.daily_trades}/{self.max_daily_trades}",
            'daily_pnl_limit': f"{self.daily_pnl/self.account_info.balance:.2%}/{self.account_info.max_risk_per_trade*3:.2%}",
            'portfolio_risk_limit': f"{len(self.active_positions)}/2",
            'emergency_stop': self.daily_pnl/self.account_info.balance <= -self.emergency_stop_loss
        }
        
        return {
            'account_status': account_status,
            'performance_metrics': {
                'total_return': performance.total_return,
                'sharpe_ratio': performance.sharpe_ratio,
                'max_drawdown': performance.max_drawdown,
                'win_rate': performance.win_rate,
                'profit_factor': performance.profit_factor,
                'var_95': performance.var_95
            },
            'risk_dashboard': risk_dashboard,
            'limits_status': limits_status,
            'recent_trades': self.trade_history[-5:],  # Last 5 trades
            'active_positions': self.active_positions
        }
    
    def reset_daily_counters(self) -> None:
        """Reset daily counters (call at start of new trading day)"""
        self.daily_trades = 0
        self.daily_pnl = 0.0
        logger.info("Daily risk counters reset")
    
    def emergency_stop(self, reason: str) -> None:
        """Emergency stop all trading activities"""
        logger.critical(f"EMERGENCY STOP TRIGGERED: {reason}")
        
        # Close all active positions (would need integration with order system)
        for position in self.active_positions:
            logger.critical(f"Emergency close position: {position.get('id')}")
        
        # Clear active positions
        self.active_positions.clear()
        
        # Set emergency flag (could be used by strategy manager)
        self.emergency_stop_active = True
