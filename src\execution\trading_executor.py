"""
Trading Executor for DEX900DN Trading System
Integrates order execution with trading strategies and risk management
"""

import logging
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List, Tuple
from dataclasses import dataclass
from enum import Enum

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))
from config.config import config
from src.execution.mt5_connection import mt5_connection
from src.execution.order_executor import OrderExecutor, OrderType, OrderResult
from src.execution.position_manager import PositionManager, ManagedPosition, ExitReason
from src.risk.risk_manager import IntegratedRiskManager, RiskDecision

# Setup logging
logging.basicConfig(level=getattr(logging, config.LOG_LEVEL))
logger = logging.getLogger(__name__)

@dataclass
class TradeSignal:
    """Trading signal from strategy"""
    strategy: str
    action: str  # 'BUY', 'SELL', 'CLOSE', 'MODIFY'
    symbol: str
    volume: Optional[float] = None
    entry_price: Optional[float] = None
    stop_loss: Optional[float] = None
    take_profit: Optional[float] = None
    confidence: float = 0.0
    reasoning: str = ""
    timestamp: datetime = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now()

@dataclass
class ExecutionResult:
    """Trade execution result"""
    success: bool
    signal: TradeSignal
    position: Optional[ManagedPosition] = None
    order_result: Optional[OrderResult] = None
    risk_decision: Optional[RiskDecision] = None
    error_message: str = ""
    execution_time: datetime = None
    
    def __post_init__(self):
        if self.execution_time is None:
            self.execution_time = datetime.now()

class TradingExecutor:
    """
    Trading Executor for DEX900DN Trading System
    
    Integrates:
    - Trading strategies (CALM and SPIKE)
    - Risk management system
    - Order execution engine
    - Position management
    - Performance tracking
    """
    
    def __init__(self, symbol: str = None, magic_number: int = None):
        self.symbol = symbol or config.SYMBOL
        self.magic_number = magic_number or config.MAGIC_NUMBER
        
        # Core components
        self.order_executor = OrderExecutor(self.symbol, self.magic_number)
        self.position_manager = PositionManager(self.symbol, self.magic_number)
        self.risk_manager = IntegratedRiskManager()
        
        # Execution tracking
        self.execution_history: List[ExecutionResult] = []
        self.active_signals: Dict[str, TradeSignal] = {}
        
        # Performance metrics
        self.total_signals = 0
        self.executed_signals = 0
        self.rejected_signals = 0
        
        # Settings
        self.auto_execute = True
        self.max_positions_per_strategy = 1
        
        logger.info(f"TradingExecutor initialized for {self.symbol}")

    def get_open_positions_count(self) -> int:
        """Get count of open positions"""
        try:
            return len(self.position_manager.get_open_positions())
        except Exception as e:
            logger.error(f"Error getting open positions count: {str(e)}")
            return 0

    def execute_signal(self, signal: TradeSignal) -> ExecutionResult:
        """
        Execute a trading signal
        
        Args:
            signal: TradeSignal to execute
            
        Returns:
            ExecutionResult with execution details
        """
        logger.info(f"Executing signal: {signal.strategy} {signal.action} {signal.symbol}")
        
        self.total_signals += 1
        
        try:
            if signal.action in ['BUY', 'SELL']:
                return self._execute_entry_signal(signal)
            elif signal.action == 'CLOSE':
                return self._execute_close_signal(signal)
            elif signal.action == 'MODIFY':
                return self._execute_modify_signal(signal)
            else:
                return ExecutionResult(
                    success=False,
                    signal=signal,
                    error_message=f"Unknown signal action: {signal.action}"
                )
        
        except Exception as e:
            logger.error(f"Signal execution error: {str(e)}")
            return ExecutionResult(
                success=False,
                signal=signal,
                error_message=f"Execution error: {str(e)}"
            )
    
    def _execute_entry_signal(self, signal: TradeSignal) -> ExecutionResult:
        """Execute entry signal (BUY/SELL)"""
        
        # Check if we already have a position for this strategy
        existing_positions = self._get_positions_by_strategy(signal.strategy)
        if len(existing_positions) >= self.max_positions_per_strategy:
            return ExecutionResult(
                success=False,
                signal=signal,
                error_message=f"Maximum positions reached for strategy {signal.strategy}"
            )
        
        # Get current market data
        symbol_info = mt5_connection.get_symbol_info(force_update=True)
        if not symbol_info:
            return ExecutionResult(
                success=False,
                signal=signal,
                error_message="Failed to get market data"
            )
        
        # Use current market price if entry price not specified
        if signal.entry_price is None:
            if signal.action == 'BUY':
                signal.entry_price = symbol_info.ask
            else:  # SELL
                signal.entry_price = symbol_info.bid
        
        # Risk assessment
        current_volatility = self._estimate_current_volatility()
        risk_decision = self.risk_manager.assess_trade_risk(
            signal.strategy,
            signal.entry_price,
            signal.stop_loss or self._calculate_default_stop_loss(signal),
            signal.take_profit or self._calculate_default_take_profit(signal),
            current_volatility
        )
        
        if not risk_decision.allow_trade:
            self.rejected_signals += 1
            return ExecutionResult(
                success=False,
                signal=signal,
                risk_decision=risk_decision,
                error_message=f"Risk management rejected trade: {risk_decision.reasoning}"
            )
        
        # Use risk-optimized position size
        volume = risk_decision.position_size
        
        # Use risk-optimized stop loss if available
        if risk_decision.recommended_stop_loss:
            signal.stop_loss = risk_decision.recommended_stop_loss
        
        # Execute the trade
        order_type = OrderType.BUY if signal.action == 'BUY' else OrderType.SELL
        
        # Calculate trailing stop for SPIKE strategy
        trailing_stop = None
        if signal.strategy == 'SPIKE':
            trailing_stop = config.SPIKE_TRAILING_STOP
        
        # Calculate partial close levels for SPIKE strategy
        partial_close_levels = []
        if signal.strategy == 'SPIKE' and signal.take_profit:
            # 50% close at 500pt profit as per specification
            partial_price = signal.take_profit
            partial_close_levels.append((partial_price, 0.5))
        
        # Open position
        success, managed_position = self.position_manager.open_position(
            strategy=signal.strategy,
            order_type=order_type,
            volume=volume,
            stop_loss=signal.stop_loss,
            take_profit=signal.take_profit,
            trailing_stop=trailing_stop,
            partial_close_levels=partial_close_levels,
            max_hold_time=self._get_max_hold_time(signal.strategy),
            risk_amount=risk_decision.risk_amount,
            comment=f"DEX900DN {signal.strategy}"  # Keep comment short for MT5 compatibility
        )
        
        if success:
            self.executed_signals += 1
            
            # Update risk manager
            self.risk_manager.update_position(
                str(managed_position.ticket),
                0.0,  # Initial P/L
                {
                    'strategy': signal.strategy,
                    'entry_price': managed_position.entry_price,
                    'volume': managed_position.volume,
                    'risk_amount': risk_decision.risk_amount
                }
            )
            
            result = ExecutionResult(
                success=True,
                signal=signal,
                position=managed_position,
                risk_decision=risk_decision
            )
        else:
            result = ExecutionResult(
                success=False,
                signal=signal,
                risk_decision=risk_decision,
                error_message="Failed to open position"
            )
        
        # Track execution
        self.execution_history.append(result)
        
        return result
    
    def _execute_close_signal(self, signal: TradeSignal) -> ExecutionResult:
        """Execute close signal"""
        
        # Find positions for this strategy
        positions = self._get_positions_by_strategy(signal.strategy)
        
        if not positions:
            return ExecutionResult(
                success=False,
                signal=signal,
                error_message=f"No open positions found for strategy {signal.strategy}"
            )
        
        # Close all positions for this strategy
        closed_count = 0
        for position in positions:
            if self.position_manager.close_position(
                position.ticket, 
                reason=ExitReason.STRATEGY_EXIT
            ):
                closed_count += 1
                
                # Update risk manager
                final_pnl = position.unrealized_pnl
                self.risk_manager.close_position(
                    str(position.ticket),
                    final_pnl,
                    "Strategy exit signal"
                )
        
        success = closed_count > 0
        
        result = ExecutionResult(
            success=success,
            signal=signal,
            error_message="" if success else "Failed to close any positions"
        )
        
        self.execution_history.append(result)
        return result
    
    def _execute_modify_signal(self, signal: TradeSignal) -> ExecutionResult:
        """Execute modify signal"""
        
        # Find positions for this strategy
        positions = self._get_positions_by_strategy(signal.strategy)
        
        if not positions:
            return ExecutionResult(
                success=False,
                signal=signal,
                error_message=f"No open positions found for strategy {signal.strategy}"
            )
        
        # Modify all positions for this strategy
        modified_count = 0
        for position in positions:
            if self.position_manager.modify_position(
                position.ticket,
                stop_loss=signal.stop_loss,
                take_profit=signal.take_profit
            ):
                modified_count += 1
        
        success = modified_count > 0
        
        result = ExecutionResult(
            success=success,
            signal=signal,
            error_message="" if success else "Failed to modify any positions"
        )
        
        self.execution_history.append(result)
        return result
    
    def _get_positions_by_strategy(self, strategy: str) -> List[ManagedPosition]:
        """Get all positions for a specific strategy"""
        all_positions = self.position_manager.get_positions()
        return [pos for pos in all_positions if pos.strategy == strategy]
    
    def _calculate_default_stop_loss(self, signal: TradeSignal) -> float:
        """Calculate default stop loss based on strategy"""
        if signal.strategy == 'CALM':
            if signal.action == 'BUY':
                return signal.entry_price - config.CALM_STOP_LOSS
            else:  # SELL
                return signal.entry_price + config.CALM_STOP_LOSS
        
        elif signal.strategy == 'SPIKE':
            if signal.action == 'BUY':
                return signal.entry_price - config.SPIKE_STOP_LOSS_MIN
            else:  # SELL
                return signal.entry_price + config.SPIKE_STOP_LOSS_MIN
        
        # Default fallback
        default_stop = 300  # 300 points
        if signal.action == 'BUY':
            return signal.entry_price - default_stop
        else:
            return signal.entry_price + default_stop
    
    def _calculate_default_take_profit(self, signal: TradeSignal) -> float:
        """Calculate default take profit based on strategy"""
        if signal.strategy == 'CALM':
            if signal.action == 'BUY':
                return signal.entry_price + config.CALM_TAKE_PROFIT_MAX
            else:  # SELL
                return signal.entry_price - config.CALM_TAKE_PROFIT_MAX
        
        elif signal.strategy == 'SPIKE':
            if signal.action == 'BUY':
                return signal.entry_price + config.SPIKE_TAKE_PROFIT
            else:  # SELL
                return signal.entry_price - config.SPIKE_TAKE_PROFIT
        
        # Default fallback
        default_tp = 200  # 200 points
        if signal.action == 'BUY':
            return signal.entry_price + default_tp
        else:
            return signal.entry_price - default_tp
    
    def _get_max_hold_time(self, strategy: str) -> Optional[timedelta]:
        """Get maximum hold time for strategy"""
        if strategy == 'CALM':
            return timedelta(hours=4)  # 4 hours max for scalping
        elif strategy == 'SPIKE':
            return timedelta(hours=2)  # 2 hours max for momentum trades
        
        return None
    
    def _estimate_current_volatility(self) -> float:
        """Estimate current market volatility"""
        # Simple volatility estimation - in production would use more sophisticated methods
        try:
            # Get recent price data and calculate volatility
            # For now, return a default value
            return 0.15  # 15% default volatility
        except:
            return 0.15
    
    def get_execution_summary(self) -> Dict[str, Any]:
        """Get execution performance summary"""
        recent_executions = [ex for ex in self.execution_history 
                           if (datetime.now() - ex.execution_time).days < 1]
        
        successful_executions = [ex for ex in recent_executions if ex.success]
        
        return {
            'total_signals': self.total_signals,
            'executed_signals': self.executed_signals,
            'rejected_signals': self.rejected_signals,
            'execution_rate': self.executed_signals / self.total_signals if self.total_signals > 0 else 0,
            'recent_executions': len(recent_executions),
            'recent_successful': len(successful_executions),
            'recent_success_rate': len(successful_executions) / len(recent_executions) if recent_executions else 0,
            'active_positions': len(self.position_manager.get_positions()),
            'position_summary': self.position_manager.get_position_summary(),
            'performance_summary': self.position_manager.get_performance_summary()
        }
    
    def emergency_close_all(self) -> int:
        """Emergency close all positions"""
        logger.critical("EMERGENCY CLOSE ALL POSITIONS")
        
        closed_count = self.position_manager.close_all_positions(ExitReason.EMERGENCY_CLOSE)
        
        # Notify risk manager
        self.risk_manager.emergency_stop("Emergency close all triggered")
        
        return closed_count
    
    def start_execution_engine(self) -> None:
        """Start the execution engine"""
        # Connect to MT5
        if not mt5_connection.is_connected():
            if not mt5_connection.connect():
                logger.error("Failed to connect to MT5")
                return
        
        # Start position monitoring
        self.position_manager.start_monitoring()
        
        logger.info("Trading execution engine started")
    
    def stop_execution_engine(self) -> None:
        """Stop the execution engine"""
        # Stop position monitoring
        self.position_manager.stop_monitoring()
        
        # Disconnect from MT5
        mt5_connection.disconnect()
        
        logger.info("Trading execution engine stopped")
    
    def get_connection_status(self) -> Dict[str, Any]:
        """Get comprehensive connection and system status"""
        return {
            'mt5_connection': mt5_connection.get_connection_status(),
            'position_manager_active': self.position_manager.monitoring_active,
            'active_positions': len(self.position_manager.get_positions()),
            'execution_summary': self.get_execution_summary(),
            'risk_dashboard': self.risk_manager.get_risk_dashboard()
        }

# Global trading executor instance
trading_executor = TradingExecutor()
