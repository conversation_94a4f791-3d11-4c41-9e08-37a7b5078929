"""
Risk Metrics Calculator for DEX900DN Trading System
Comprehensive risk metrics including VaR, drawdown, Sharpe ratio, and performance analytics
"""

import logging
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, Any, List, Tuple, Optional
from dataclasses import dataclass
from collections import deque

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))
from config.config import config

# Setup logging
logging.basicConfig(level=getattr(logging, config.LOG_LEVEL))
logger = logging.getLogger(__name__)

@dataclass
class PerformanceMetrics:
    """Comprehensive performance metrics"""
    total_return: float
    annualized_return: float
    volatility: float
    sharpe_ratio: float
    sortino_ratio: float
    max_drawdown: float
    max_drawdown_duration: int
    calmar_ratio: float
    win_rate: float
    profit_factor: float
    average_win: float
    average_loss: float
    largest_win: float
    largest_loss: float
    consecutive_wins: int
    consecutive_losses: int
    var_95: float
    var_99: float
    cvar_95: float  # Conditional VaR
    beta: float
    alpha: float
    information_ratio: float

@dataclass
class RiskAlert:
    """Risk alert data structure"""
    alert_type: str
    severity: str  # LOW, MEDIUM, HIGH, CRITICAL
    message: str
    timestamp: datetime
    metric_value: float
    threshold: float

class RiskMetricsCalculator:
    """
    Comprehensive Risk Metrics Calculator for DEX900DN Trading System
    
    Features:
    - Real-time risk monitoring
    - Portfolio performance analytics
    - Drawdown analysis
    - Value at Risk (VaR) calculations
    - Risk-adjusted returns
    - Alert system for risk thresholds
    """
    
    def __init__(self, lookback_periods: int = 252):
        self.lookback_periods = lookback_periods  # ~1 year of daily data
        
        # Performance tracking
        self.returns_history = deque(maxlen=lookback_periods)
        self.equity_curve = deque(maxlen=lookback_periods)
        self.drawdown_history = deque(maxlen=lookback_periods)
        
        # Risk thresholds - calibrated for 2% daily risk system
        self.risk_thresholds = {
            'max_drawdown': 0.15,      # 15% maximum drawdown
            'var_95': 0.10,            # 10% daily VaR (increased from 5% to allow trades)
            'volatility': 0.50,        # 50% annualized volatility (increased for DEX index)
            'consecutive_losses': 5,    # 5 consecutive losses
            'daily_loss': 0.03         # 3% daily loss
        }
        
        # Alert history
        self.alerts = deque(maxlen=100)
        
        logger.info("RiskMetricsCalculator initialized")
    
    def update_performance(self, current_pnl: float, timestamp: datetime = None) -> None:
        """
        Update performance tracking with new P/L data
        
        Args:
            current_pnl: Current profit/loss
            timestamp: Timestamp for the update
        """
        timestamp = timestamp or datetime.now()
        
        # Calculate return if we have previous equity
        if self.equity_curve:
            previous_equity = self.equity_curve[-1]
            if previous_equity != 0:
                return_pct = (current_pnl - previous_equity) / abs(previous_equity)
                self.returns_history.append(return_pct)
        
        # Update equity curve
        self.equity_curve.append(current_pnl)
        
        # Calculate and update drawdown
        if self.equity_curve:
            peak = max(self.equity_curve)
            current_drawdown = (current_pnl - peak) / peak if peak != 0 else 0
            self.drawdown_history.append(current_drawdown)
            
            # Check for risk alerts
            self._check_risk_alerts(current_pnl, current_drawdown, timestamp)
    
    def calculate_comprehensive_metrics(self, trades_data: List[Dict] = None) -> PerformanceMetrics:
        """
        Calculate comprehensive performance metrics
        
        Args:
            trades_data: Optional list of trade data for detailed analysis
            
        Returns:
            PerformanceMetrics object with all calculated metrics
        """
        if len(self.returns_history) < 2:
            return self._empty_metrics()
        
        returns = np.array(list(self.returns_history))
        equity = np.array(list(self.equity_curve))
        drawdowns = np.array(list(self.drawdown_history))
        
        # Basic return metrics
        total_return = (equity[-1] - equity[0]) / abs(equity[0]) if equity[0] != 0 else 0
        annualized_return = self._annualize_return(total_return, len(returns))
        volatility = np.std(returns) * np.sqrt(252)  # Annualized volatility
        
        # Risk-adjusted returns
        sharpe_ratio = annualized_return / volatility if volatility > 0 else 0
        
        # Sortino ratio (downside deviation)
        downside_returns = returns[returns < 0]
        downside_deviation = np.std(downside_returns) * np.sqrt(252) if len(downside_returns) > 0 else 0
        sortino_ratio = annualized_return / downside_deviation if downside_deviation > 0 else 0
        
        # Drawdown metrics
        max_drawdown = abs(min(drawdowns)) if len(drawdowns) > 0 else 0
        max_dd_duration = self._calculate_max_drawdown_duration(drawdowns)
        
        # Calmar ratio
        calmar_ratio = annualized_return / max_drawdown if max_drawdown > 0 else 0
        
        # VaR calculations
        var_95 = self._calculate_var(returns, 0.95)
        var_99 = self._calculate_var(returns, 0.99)
        cvar_95 = self._calculate_cvar(returns, 0.95)
        
        # Trade-based metrics
        if trades_data:
            trade_metrics = self._calculate_trade_metrics(trades_data)
        else:
            trade_metrics = self._estimate_trade_metrics(returns)
        
        # Market-relative metrics (simplified - would need benchmark data)
        beta = self._estimate_beta(returns)
        alpha = annualized_return - (beta * 0.05)  # Assuming 5% market return
        information_ratio = alpha / volatility if volatility > 0 else 0
        
        return PerformanceMetrics(
            total_return=total_return,
            annualized_return=annualized_return,
            volatility=volatility,
            sharpe_ratio=sharpe_ratio,
            sortino_ratio=sortino_ratio,
            max_drawdown=max_drawdown,
            max_drawdown_duration=max_dd_duration,
            calmar_ratio=calmar_ratio,
            win_rate=trade_metrics['win_rate'],
            profit_factor=trade_metrics['profit_factor'],
            average_win=trade_metrics['average_win'],
            average_loss=trade_metrics['average_loss'],
            largest_win=trade_metrics['largest_win'],
            largest_loss=trade_metrics['largest_loss'],
            consecutive_wins=trade_metrics['consecutive_wins'],
            consecutive_losses=trade_metrics['consecutive_losses'],
            var_95=var_95,
            var_99=var_99,
            cvar_95=cvar_95,
            beta=beta,
            alpha=alpha,
            information_ratio=information_ratio
        )
    
    def _calculate_var(self, returns: np.ndarray, confidence_level: float) -> float:
        """Calculate Value at Risk"""
        if len(returns) == 0:
            return 0.0
        
        percentile = (1 - confidence_level) * 100
        return -np.percentile(returns, percentile)
    
    def _calculate_cvar(self, returns: np.ndarray, confidence_level: float) -> float:
        """Calculate Conditional Value at Risk (Expected Shortfall)"""
        if len(returns) == 0:
            return 0.0
        
        var = self._calculate_var(returns, confidence_level)
        tail_returns = returns[returns <= -var]
        
        return -np.mean(tail_returns) if len(tail_returns) > 0 else 0.0
    
    def _calculate_max_drawdown_duration(self, drawdowns: np.ndarray) -> int:
        """Calculate maximum drawdown duration in periods"""
        if len(drawdowns) == 0:
            return 0
        
        max_duration = 0
        current_duration = 0
        
        for dd in drawdowns:
            if dd < 0:  # In drawdown
                current_duration += 1
                max_duration = max(max_duration, current_duration)
            else:  # Out of drawdown
                current_duration = 0
        
        return max_duration
    
    def _calculate_trade_metrics(self, trades_data: List[Dict]) -> Dict[str, float]:
        """Calculate trade-based metrics from actual trade data"""
        if not trades_data:
            return self._default_trade_metrics()
        
        pnls = [trade.get('pnl', 0) for trade in trades_data]
        winning_trades = [pnl for pnl in pnls if pnl > 0]
        losing_trades = [pnl for pnl in pnls if pnl < 0]
        
        win_rate = len(winning_trades) / len(pnls) if pnls else 0
        
        avg_win = np.mean(winning_trades) if winning_trades else 0
        avg_loss = np.mean(losing_trades) if losing_trades else 0
        
        total_wins = sum(winning_trades)
        total_losses = abs(sum(losing_trades))
        profit_factor = total_wins / total_losses if total_losses > 0 else float('inf')
        
        largest_win = max(winning_trades) if winning_trades else 0
        largest_loss = min(losing_trades) if losing_trades else 0
        
        # Calculate consecutive wins/losses
        consecutive_wins = self._max_consecutive(pnls, lambda x: x > 0)
        consecutive_losses = self._max_consecutive(pnls, lambda x: x < 0)
        
        return {
            'win_rate': win_rate,
            'profit_factor': profit_factor,
            'average_win': avg_win,
            'average_loss': avg_loss,
            'largest_win': largest_win,
            'largest_loss': largest_loss,
            'consecutive_wins': consecutive_wins,
            'consecutive_losses': consecutive_losses
        }
    
    def _estimate_trade_metrics(self, returns: np.ndarray) -> Dict[str, float]:
        """Estimate trade metrics from return data"""
        if len(returns) == 0:
            return self._default_trade_metrics()
        
        winning_returns = returns[returns > 0]
        losing_returns = returns[returns < 0]
        
        win_rate = len(winning_returns) / len(returns)
        
        avg_win = np.mean(winning_returns) if len(winning_returns) > 0 else 0
        avg_loss = np.mean(losing_returns) if len(losing_returns) > 0 else 0
        
        total_wins = np.sum(winning_returns)
        total_losses = abs(np.sum(losing_returns))
        profit_factor = total_wins / total_losses if total_losses > 0 else float('inf')
        
        largest_win = np.max(winning_returns) if len(winning_returns) > 0 else 0
        largest_loss = np.min(losing_returns) if len(losing_returns) > 0 else 0
        
        consecutive_wins = self._max_consecutive(returns, lambda x: x > 0)
        consecutive_losses = self._max_consecutive(returns, lambda x: x < 0)
        
        return {
            'win_rate': win_rate,
            'profit_factor': profit_factor,
            'average_win': avg_win,
            'average_loss': avg_loss,
            'largest_win': largest_win,
            'largest_loss': largest_loss,
            'consecutive_wins': consecutive_wins,
            'consecutive_losses': consecutive_losses
        }
    
    def _max_consecutive(self, data: np.ndarray, condition) -> int:
        """Calculate maximum consecutive occurrences of condition"""
        max_consecutive = 0
        current_consecutive = 0
        
        for value in data:
            if condition(value):
                current_consecutive += 1
                max_consecutive = max(max_consecutive, current_consecutive)
            else:
                current_consecutive = 0
        
        return max_consecutive
    
    def _annualize_return(self, total_return: float, periods: int) -> float:
        """Annualize return based on number of periods"""
        if periods == 0:
            return 0.0
        
        # Assume daily data, 252 trading days per year
        years = periods / 252
        return (1 + total_return) ** (1 / years) - 1 if years > 0 else 0.0
    
    def _estimate_beta(self, returns: np.ndarray) -> float:
        """Estimate beta (simplified - would need market data)"""
        # Simplified beta estimation - in practice would correlate with market returns
        return 1.0 + (np.std(returns) - 0.02) * 5  # Rough approximation
    
    def _check_risk_alerts(self, current_pnl: float, current_drawdown: float, 
                          timestamp: datetime) -> None:
        """Check for risk threshold breaches and generate alerts"""
        
        # Maximum drawdown alert
        if abs(current_drawdown) > self.risk_thresholds['max_drawdown']:
            alert = RiskAlert(
                alert_type="MAX_DRAWDOWN",
                severity="HIGH",
                message=f"Maximum drawdown exceeded: {abs(current_drawdown):.2%}",
                timestamp=timestamp,
                metric_value=abs(current_drawdown),
                threshold=self.risk_thresholds['max_drawdown']
            )
            self.alerts.append(alert)
            logger.warning(f"RISK ALERT: {alert.message}")
        
        # Daily VaR alert
        if len(self.returns_history) > 0:
            recent_returns = list(self.returns_history)[-20:]  # Last 20 periods
            if recent_returns:
                current_var = self._calculate_var(np.array(recent_returns), 0.95)
                if current_var > self.risk_thresholds['var_95']:
                    alert = RiskAlert(
                        alert_type="VAR_BREACH",
                        severity="MEDIUM",
                        message=f"VaR 95% exceeded: {current_var:.2%}",
                        timestamp=timestamp,
                        metric_value=current_var,
                        threshold=self.risk_thresholds['var_95']
                    )
                    self.alerts.append(alert)
                    logger.warning(f"RISK ALERT: {alert.message}")
        
        # Consecutive losses alert
        if len(self.returns_history) >= self.risk_thresholds['consecutive_losses']:
            recent_returns = list(self.returns_history)[-self.risk_thresholds['consecutive_losses']:]
            if all(r < 0 for r in recent_returns):
                alert = RiskAlert(
                    alert_type="CONSECUTIVE_LOSSES",
                    severity="HIGH",
                    message=f"Consecutive losses: {len(recent_returns)}",
                    timestamp=timestamp,
                    metric_value=len(recent_returns),
                    threshold=self.risk_thresholds['consecutive_losses']
                )
                self.alerts.append(alert)
                logger.warning(f"RISK ALERT: {alert.message}")
    
    def get_risk_dashboard(self) -> Dict[str, Any]:
        """Get comprehensive risk dashboard data"""
        metrics = self.calculate_comprehensive_metrics()
        
        # Current risk status
        current_drawdown = list(self.drawdown_history)[-1] if self.drawdown_history else 0
        recent_alerts = [alert for alert in self.alerts 
                        if (datetime.now() - alert.timestamp).days < 1]
        
        return {
            'current_metrics': {
                'total_return': metrics.total_return,
                'sharpe_ratio': metrics.sharpe_ratio,
                'max_drawdown': metrics.max_drawdown,
                'current_drawdown': current_drawdown,
                'var_95': metrics.var_95,
                'win_rate': metrics.win_rate,
                'profit_factor': metrics.profit_factor
            },
            'risk_status': {
                'overall_risk': self._assess_overall_risk(metrics),
                'active_alerts': len(recent_alerts),
                'risk_score': self._calculate_risk_score(metrics)
            },
            'thresholds': self.risk_thresholds,
            'recent_alerts': [
                {
                    'type': alert.alert_type,
                    'severity': alert.severity,
                    'message': alert.message,
                    'timestamp': alert.timestamp.isoformat()
                }
                for alert in recent_alerts
            ]
        }
    
    def _assess_overall_risk(self, metrics: PerformanceMetrics) -> str:
        """Assess overall risk level"""
        risk_factors = 0
        
        if metrics.max_drawdown > 0.10:
            risk_factors += 1
        if metrics.var_95 > self.risk_thresholds['var_95']:
            risk_factors += 1
        if metrics.sharpe_ratio < 0.5:
            risk_factors += 1
        if metrics.win_rate < 0.4:
            risk_factors += 1
        
        if risk_factors >= 3:
            return "HIGH"
        elif risk_factors >= 2:
            return "MEDIUM"
        else:
            return "LOW"
    
    def _calculate_risk_score(self, metrics: PerformanceMetrics) -> float:
        """Calculate overall risk score (0-100)"""
        # Weighted risk score
        drawdown_score = min(100, metrics.max_drawdown * 500)  # 0-100
        var_score = min(100, metrics.var_95 * 1000)  # 0-100
        volatility_score = min(100, metrics.volatility * 200)  # 0-100
        
        # Combine scores (lower is better)
        risk_score = (drawdown_score * 0.4 + var_score * 0.3 + volatility_score * 0.3)
        
        return min(100, max(0, risk_score))
    
    def _empty_metrics(self) -> PerformanceMetrics:
        """Return empty metrics when insufficient data"""
        return PerformanceMetrics(
            total_return=0.0, annualized_return=0.0, volatility=0.0,
            sharpe_ratio=0.0, sortino_ratio=0.0, max_drawdown=0.0,
            max_drawdown_duration=0, calmar_ratio=0.0, win_rate=0.0,
            profit_factor=0.0, average_win=0.0, average_loss=0.0,
            largest_win=0.0, largest_loss=0.0, consecutive_wins=0,
            consecutive_losses=0, var_95=0.0, var_99=0.0, cvar_95=0.0,
            beta=1.0, alpha=0.0, information_ratio=0.0
        )
    
    def _default_trade_metrics(self) -> Dict[str, float]:
        """Return default trade metrics"""
        return {
            'win_rate': 0.0, 'profit_factor': 0.0, 'average_win': 0.0,
            'average_loss': 0.0, 'largest_win': 0.0, 'largest_loss': 0.0,
            'consecutive_wins': 0, 'consecutive_losses': 0
        }
