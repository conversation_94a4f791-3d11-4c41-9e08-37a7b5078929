"""
DEX900DN Master System Controller
Main orchestrator for the complete trading system
"""

import logging
import time
import threading
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List
from dataclasses import dataclass
from enum import Enum

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))
from config.config import config

# Import all system components
from src.modules.cycle_engine import TimeCycleTracker
from src.modules.data_feed import DataHandler
from src.modules.phase_classifier import PhaseClassifier
from src.strategies.calm_strategy import CalmPhaseStrategy
from src.strategies.spike_strategy import SpikePhaseStrategy
from src.strategies.strategy_manager import StrategyManager
from src.risk.risk_manager import IntegratedRiskManager
from src.risk.position_sizing import AccountInfo as RiskAccountInfo
from src.execution.trading_executor import TradingExecutor, TradeSignal
from src.execution.mt5_connection import mt5_connection

# Setup logging
logging.basicConfig(level=getattr(logging, config.LOG_LEVEL))
logger = logging.getLogger(__name__)

class SystemState(Enum):
    """System operational states"""
    STOPPED = "STOPPED"
    STARTING = "STARTING"
    RUNNING = "RUNNING"
    PAUSED = "PAUSED"
    STOPPING = "STOPPING"
    ERROR = "ERROR"
    EMERGENCY_STOP = "EMERGENCY_STOP"

@dataclass
class SystemStatus:
    """Comprehensive system status"""
    state: SystemState
    uptime: timedelta
    cycles_processed: int
    trades_executed: int
    current_phase: str
    account_balance: float
    daily_pnl: float
    active_positions: int
    last_update: datetime
    errors: List[str]
    warnings: List[str]

class DEX900DNController:
    """
    Master System Controller for DEX900DN Trading System
    
    Orchestrates all components:
    - Data feed and cycle tracking
    - Phase detection
    - Strategy execution
    - Risk management
    - Order execution
    - System monitoring
    """
    
    def __init__(self):
        self.system_state = SystemState.STOPPED
        self.start_time = None
        self.stop_requested = False
        
        # Initialize all components
        logger.info("Initializing DEX900DN Trading System...")
        
        try:
            # Core components
            self.cycle_tracker = TimeCycleTracker()
            self.data_handler = DataHandler()
            self.phase_classifier = PhaseClassifier(self.cycle_tracker, self.data_handler)
            
            # Strategy components (create strategies first, then pass to manager)
            self.calm_strategy = CalmPhaseStrategy(self.data_handler)
            self.spike_strategy = SpikePhaseStrategy(self.data_handler)
            self.strategy_manager = StrategyManager(
                self.cycle_tracker, self.data_handler, self.phase_classifier,
                self.calm_strategy, self.spike_strategy
            )
            
            # Risk and execution components
            # Get real account info from MT5 and convert to risk manager format
            real_account_info = self._get_real_account_info()
            self.risk_manager = IntegratedRiskManager(real_account_info)
            self.trading_executor = TradingExecutor()
            
            # System monitoring
            self.status_lock = threading.Lock()
            self.cycles_processed = 0
            self.trades_executed = 0
            self.errors = []
            self.warnings = []
            
            # Performance tracking
            self.last_cycle_time = None
            self.cycle_times = []
            self.max_cycle_times = 1000  # Keep last 1000 cycle times

            # Circuit breaker tracking
            self.consecutive_failed_cycles = 0
            self.max_failed_cycles = 3
            self.cycle_timing_errors = []
            self.max_timing_error = 3.0  # seconds
            
            logger.info("✅ All components initialized successfully")

        except Exception as e:
            logger.error(f"❌ System initialization failed: {str(e)}")
            self.system_state = SystemState.ERROR
            raise

    def _get_real_account_info(self) -> RiskAccountInfo:
        """Get real account info from MT5 and convert to risk manager format"""
        try:
            # Connect to MT5 to get account info
            if not mt5_connection.is_connected():
                mt5_connection.connect()

            # Get real account info from MT5
            mt5_account = mt5_connection.get_account_info(force_update=True)

            if mt5_account:
                # Convert MT5 account info to risk manager format
                risk_account = RiskAccountInfo(
                    balance=mt5_account.balance,
                    equity=mt5_account.equity,
                    margin_used=mt5_account.margin,
                    margin_available=mt5_account.margin_free,
                    max_risk_per_trade=0.02,  # 2% per trade (configurable)
                    max_portfolio_risk=0.06   # 6% total portfolio risk (configurable)
                )
                logger.info(f"✅ Real account info loaded: Balance ${mt5_account.balance:,.2f}")
                return risk_account
            else:
                logger.warning("⚠️ Could not get real account info, using defaults")

        except Exception as e:
            logger.warning(f"⚠️ Error getting real account info: {str(e)}, using defaults")

        # Fallback to default values if MT5 connection fails
        return RiskAccountInfo(
            balance=10000.0,
            equity=10000.0,
            margin_used=0.0,
            margin_available=10000.0,
            max_risk_per_trade=0.02,
            max_portfolio_risk=0.06
        )

    def start_system(self) -> bool:
        """
        Start the complete trading system
        
        Returns:
            bool: True if system started successfully
        """
        if self.system_state != SystemState.STOPPED:
            logger.warning(f"Cannot start system in state: {self.system_state}")
            return False
        
        logger.info("🚀 Starting DEX900DN Trading System...")
        self.system_state = SystemState.STARTING

        try:
            # CRITICAL: Collect initial data before starting trading
            logger.info("📊 Collecting initial market data...")
            if not self._collect_initial_data():
                logger.error("Failed to collect sufficient initial data")
                self.system_state = SystemState.ERROR
                return False

            # Start execution engine
            self.trading_executor.start_execution_engine()

            # Start strategy manager
            self.strategy_manager.start()

            # Reset counters
            self.start_time = datetime.now()
            self.cycles_processed = 0
            self.trades_executed = 0
            self.errors.clear()
            self.warnings.clear()
            self.stop_requested = False

            # Start main trading loop
            self.system_state = SystemState.RUNNING
            self.main_thread = threading.Thread(target=self._main_trading_loop, daemon=True)
            self.main_thread.start()

            logger.info("✅ DEX900DN Trading System started successfully")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to start system: {str(e)}")
            self.system_state = SystemState.ERROR
            self.errors.append(f"Startup error: {str(e)}")
            return False
    
    def stop_system(self) -> bool:
        """
        Stop the trading system gracefully
        
        Returns:
            bool: True if system stopped successfully
        """
        if self.system_state not in [SystemState.RUNNING, SystemState.PAUSED]:
            logger.warning(f"Cannot stop system in state: {self.system_state}")
            return False
        
        logger.info("🛑 Stopping DEX900DN Trading System...")
        self.system_state = SystemState.STOPPING
        self.stop_requested = True
        
        try:
            # Wait for main loop to finish
            if hasattr(self, 'main_thread') and self.main_thread.is_alive():
                self.main_thread.join(timeout=10)
            
            # Stop strategy manager
            self.strategy_manager.stop()
            
            # Stop execution engine
            self.trading_executor.stop_execution_engine()
            
            self.system_state = SystemState.STOPPED
            logger.info("✅ DEX900DN Trading System stopped successfully")
            return True
            
        except Exception as e:
            logger.error(f"❌ Error stopping system: {str(e)}")
            self.system_state = SystemState.ERROR
            self.errors.append(f"Shutdown error: {str(e)}")
            return False
    
    def emergency_stop(self, reason: str = "Manual emergency stop") -> None:
        """
        Emergency stop - immediately halt all trading

        Args:
            reason: Reason for emergency stop
        """
        logger.critical(f"🚨 EMERGENCY STOP: {reason}")

        # Prevent infinite loop - check if already in emergency state
        if self.system_state == SystemState.EMERGENCY_STOP:
            logger.warning("⚠️ Emergency stop already in progress, skipping duplicate call")
            return

        self.system_state = SystemState.EMERGENCY_STOP
        self.stop_requested = True

        try:
            # Check if there are positions to close
            try:
                open_positions = self.trading_executor.get_open_positions_count()
                if open_positions > 0:
                    logger.critical(f"EMERGENCY CLOSE ALL POSITIONS ({open_positions} positions)")
                    closed_count = self.trading_executor.emergency_close_all()
                    logger.critical(f"Emergency closed {closed_count} positions")
                else:
                    logger.warning("⚠️ No positions to close during emergency stop")
            except Exception as e:
                logger.error(f"Error checking/closing positions: {str(e)}")

            # Stop all components
            if hasattr(self, 'strategy_manager'):
                self.strategy_manager.emergency_stop()
            if hasattr(self, 'risk_manager'):
                self.risk_manager.emergency_stop(reason)

            # Record emergency stop
            self.errors.append(f"EMERGENCY STOP: {reason}")

            # Transition to STOPPED state to prevent infinite loop
            self.system_state = SystemState.STOPPED

        except Exception as e:
            logger.critical(f"Error during emergency stop: {str(e)}")
            self.system_state = SystemState.ERROR
    
    def pause_system(self) -> bool:
        """Pause the trading system"""
        if self.system_state != SystemState.RUNNING:
            return False
        
        logger.info("⏸️ Pausing trading system...")
        self.system_state = SystemState.PAUSED
        return True
    
    def resume_system(self) -> bool:
        """Resume the trading system"""
        if self.system_state != SystemState.PAUSED:
            return False
        
        logger.info("▶️ Resuming trading system...")
        self.system_state = SystemState.RUNNING
        return True
    
    def _main_trading_loop(self) -> None:
        """Main trading loop - processes market data and executes strategies"""
        logger.info("📊 Main trading loop started")
        
        while not self.stop_requested and self.system_state in [SystemState.RUNNING, SystemState.PAUSED]:
            try:
                # CRITICAL: Check for emergency stop flag
                if self._check_emergency_stop():
                    logger.critical("EMERGENCY STOP FLAG DETECTED - SHUTTING DOWN SYSTEM")
                    self.emergency_stop("EMERGENCY_FLAG_DETECTED")
                    break

                cycle_start = time.time()

                # Skip processing if paused
                if self.system_state == SystemState.PAUSED:
                    time.sleep(0.1)
                    continue
                
                # Process one trading cycle
                cycle_success = self._process_trading_cycle()

                # Update performance metrics and check circuit breakers
                cycle_time = time.time() - cycle_start
                self._update_cycle_metrics(cycle_time)
                self._check_circuit_breakers(cycle_success, cycle_time)
                
                # Brief sleep to prevent excessive CPU usage
                time.sleep(0.01)  # 10ms sleep
                
            except Exception as e:
                logger.error(f"Error in main trading loop: {str(e)}")
                self.errors.append(f"Trading loop error: {str(e)}")
                
                # Check if we should emergency stop
                if len(self.errors) > 10:  # Too many errors
                    self.emergency_stop("Too many trading loop errors")
                    break
                
                time.sleep(1)  # Wait before retrying
        
        logger.info("📊 Main trading loop stopped")

    def _collect_initial_data(self) -> bool:
        """Collect sufficient initial data before starting trading"""
        import time

        logger.info("Collecting initial market data for stable volatility calculation...")

        # Target: At least 60 data points (1 minute of data)
        target_data_points = 60
        max_wait_time = 120  # Maximum 2 minutes to collect data
        start_time = time.time()

        while time.time() - start_time < max_wait_time:
            # Fetch new data
            self.data_handler.fetch_tick_data(5)  # Get 5 ticks at a time

            current_data_points = len(self.data_handler.price_buffer)
            logger.info(f"Data collection progress: {current_data_points}/{target_data_points} data points")

            if current_data_points >= target_data_points:
                logger.info(f"✅ Sufficient data collected: {current_data_points} data points")

                # Test volatility calculation
                try:
                    vol = self.data_handler.calculate_volatility(1)  # 1-minute volatility
                    logger.info(f"Initial volatility: {vol:.6f}")
                    return True
                except Exception as e:
                    logger.warning(f"Volatility calculation failed: {str(e)}")
                    continue

            time.sleep(1)  # Wait 1 second between attempts

        logger.error(f"Failed to collect sufficient data in {max_wait_time} seconds")
        return False

    def _check_emergency_stop(self) -> bool:
        """Check for emergency stop flag file"""
        try:
            import os
            emergency_file = "EMERGENCY_STOP.flag"
            return os.path.exists(emergency_file)
        except Exception as e:
            logger.error(f"Error checking emergency stop flag: {str(e)}")
            return False
    
    def _process_trading_cycle(self) -> bool:
        """Process one complete trading cycle"""

        try:
            # 1. Update market data with validation
            market_data = self.data_handler.get_latest_data()
            if not market_data:
                return False

            # CRITICAL: Validate market data for impossible values
            try:
                volatility = market_data.get('volatility_5min', 0.0)

                # Calculate RSI for validation
                rsi_value = 50.0  # Default safe value
                if hasattr(self.data_handler, 'price_buffer') and len(self.data_handler.price_buffer) >= 14:
                    prices = list(self.data_handler.price_buffer)[-14:]
                    if len(prices) >= 2:
                        import numpy as np
                        price_changes = np.diff(prices)
                        gains = np.where(price_changes > 0, price_changes, 0)
                        losses = np.where(price_changes < 0, -price_changes, 0)
                        avg_gain = np.mean(gains) if len(gains) > 0 else 0
                        avg_loss = np.mean(losses) if len(losses) > 0 else 0.001
                        if avg_loss > 0:
                            rs = avg_gain / avg_loss
                            rsi_value = min(99.9, 100 - (100 / (1 + rs)))  # Cap at 99.9

                # Validate data integrity
                self.data_handler.validate_indicators(volatility, rsi_value)

            except Exception as e:
                logger.critical(f"DATA INTEGRITY FAILURE: {str(e)}")
                logger.critical("ENTERING EMERGENCY SAFE MODE")
                self.emergency_stop("DATA_ANOMALY_DETECTED")
                return False

            # 2. Update cycle tracker
            self.cycle_tracker.update(market_data['timestamp'])

            # 3. Detect current phase
            current_phase = self.phase_classifier.classify_phase(market_data)

            # 4. Get strategy signals
            signals = self.strategy_manager.process_market_update()

            # 5. Execute signals
            for strategy_action in signals:
                try:
                    # Convert StrategyAction to TradeSignal
                    from src.execution.trading_executor import TradeSignal

                    # Map action types
                    action_map = {
                        'OPEN_POSITION': 'BUY',  # Default to BUY for CALM strategy
                        'CLOSE_POSITION': 'CLOSE',
                        'MODIFY_POSITION': 'MODIFY'
                    }

                    trade_signal = TradeSignal(
                        strategy=strategy_action.strategy,
                        action=action_map.get(strategy_action.action_type, 'BUY'),
                        symbol=config.SYMBOL,
                        volume=0.01,  # Default volume
                        confidence=getattr(strategy_action, 'confidence', 0.8),  # Default confidence
                        reasoning=getattr(strategy_action, 'reasoning', 'Strategy signal')  # Default reasoning
                    )

                    result = self.trading_executor.execute_signal(trade_signal)
                    if result.success:
                        self.trades_executed += 1
                        logger.info(f"✅ Signal executed: {strategy_action.strategy} {strategy_action.action_type}")
                    else:
                        logger.warning(f"❌ Signal failed: {result.error_message}")

                except Exception as e:
                    logger.error(f"Error executing signal: {str(e)}")
                    self.errors.append(f"Signal execution error: {str(e)}")

            # 6. Update counters
            self.cycles_processed += 1
            return True

        except Exception as e:
            logger.error(f"Error in trading cycle: {str(e)}")
            self.errors.append(f"Trading cycle error: {str(e)}")
            return False
    
    def _update_cycle_metrics(self, cycle_time: float) -> None:
        """Update cycle performance metrics"""
        self.last_cycle_time = cycle_time
        self.cycle_times.append(cycle_time)

        # Keep only recent cycle times
        if len(self.cycle_times) > self.max_cycle_times:
            self.cycle_times = self.cycle_times[-self.max_cycle_times:]

    def _check_circuit_breakers(self, cycle_success: bool, cycle_time: float) -> None:
        """Check circuit breakers and safety mechanisms"""

        # Check for consecutive failed cycles
        if not cycle_success:
            self.consecutive_failed_cycles += 1
            logger.warning(f"⚠️ Failed cycle #{self.consecutive_failed_cycles}")

            if self.consecutive_failed_cycles >= self.max_failed_cycles:
                logger.critical(f"🚨 CIRCUIT BREAKER: {self.consecutive_failed_cycles} consecutive failed cycles")
                self.emergency_stop("Cycle desync detected - too many failed cycles")
                return
        else:
            self.consecutive_failed_cycles = 0  # Reset on success

        # Check for timing errors
        if cycle_time > self.max_timing_error:
            self.cycle_timing_errors.append(cycle_time)
            logger.warning(f"⏰ Cycle timing error: {cycle_time:.2f}s > {self.max_timing_error}s")

            # Keep only recent timing errors
            if len(self.cycle_timing_errors) > 10:
                self.cycle_timing_errors = self.cycle_timing_errors[-10:]

            # If too many timing errors, trigger circuit breaker
            if len(self.cycle_timing_errors) >= 5:
                avg_error = sum(self.cycle_timing_errors) / len(self.cycle_timing_errors)
                logger.critical(f"🚨 CIRCUIT BREAKER: Excessive timing errors (avg: {avg_error:.2f}s)")
                self.emergency_stop("Excessive cycle timing errors detected")
    
    def get_system_status(self) -> SystemStatus:
        """Get comprehensive system status"""
        with self.status_lock:
            uptime = datetime.now() - self.start_time if self.start_time else timedelta(0)
            
            # Get account info
            account_balance = 0.0
            daily_pnl = 0.0
            active_positions = 0
            
            try:
                risk_dashboard = self.risk_manager.get_risk_dashboard()
                account_balance = risk_dashboard['account_status']['balance']
                daily_pnl = risk_dashboard['account_status']['daily_pnl']
                active_positions = risk_dashboard['account_status']['active_positions']
            except:
                pass
            
            # Get current phase
            current_phase = "UNKNOWN"
            try:
                latest_data = self.data_handler.get_latest_data()
                if latest_data:
                    phase_signal = self.phase_classifier.classify_phase(latest_data)
                    # Format phase nicely instead of showing raw object
                    current_phase = f"{phase_signal.phase.value} ({phase_signal.confidence:.1f}% confidence)"
            except:
                pass
            
            return SystemStatus(
                state=self.system_state,
                uptime=uptime,
                cycles_processed=self.cycles_processed,
                trades_executed=self.trades_executed,
                current_phase=current_phase,
                account_balance=account_balance,
                daily_pnl=daily_pnl,
                active_positions=active_positions,
                last_update=datetime.now(),
                errors=self.errors.copy(),
                warnings=self.warnings.copy()
            )
    
    def get_performance_metrics(self) -> Dict[str, Any]:
        """Get detailed performance metrics"""
        status = self.get_system_status()
        
        # Calculate cycle performance
        avg_cycle_time = sum(self.cycle_times) / len(self.cycle_times) if self.cycle_times else 0
        max_cycle_time = max(self.cycle_times) if self.cycle_times else 0
        min_cycle_time = min(self.cycle_times) if self.cycle_times else 0
        
        # Calculate rates
        cycles_per_second = 1.0 / avg_cycle_time if avg_cycle_time > 0 else 0
        uptime_seconds = status.uptime.total_seconds()
        trades_per_hour = (self.trades_executed / uptime_seconds * 3600) if uptime_seconds > 0 else 0
        
        return {
            'system_status': status,
            'performance': {
                'avg_cycle_time_ms': avg_cycle_time * 1000,
                'max_cycle_time_ms': max_cycle_time * 1000,
                'min_cycle_time_ms': min_cycle_time * 1000,
                'cycles_per_second': cycles_per_second,
                'trades_per_hour': trades_per_hour,
                'error_rate': len(self.errors) / max(1, self.cycles_processed),
                'uptime_hours': uptime_seconds / 3600
            },
            'component_status': {
                'cycle_tracker': self.cycle_tracker.get_status(),
                'data_handler': self.data_handler.get_status(),
                'phase_classifier': self.phase_classifier.get_status(),
                'strategy_manager': self.strategy_manager.get_status(),
                'risk_manager': self.risk_manager.get_risk_dashboard(),
                'trading_executor': self.trading_executor.get_execution_summary()
            }
        }
    
    def health_check(self) -> Dict[str, Any]:
        """Perform comprehensive health check"""
        health = {
            'overall_status': 'HEALTHY',
            'components': {},
            'issues': []
        }
        
        try:
            # Check each component
            components = [
                ('cycle_tracker', self.cycle_tracker),
                ('data_handler', self.data_handler),
                ('phase_classifier', self.phase_classifier),
                ('strategy_manager', self.strategy_manager),
                ('risk_manager', self.risk_manager),
                ('trading_executor', self.trading_executor)
            ]
            
            for name, component in components:
                try:
                    if hasattr(component, 'health_check'):
                        component_health = component.health_check()
                    else:
                        component_health = {'status': 'OK', 'message': 'No health check available'}
                    
                    health['components'][name] = component_health
                    
                    if component_health.get('status') != 'OK':
                        health['issues'].append(f"{name}: {component_health.get('message', 'Unknown issue')}")
                        
                except Exception as e:
                    health['components'][name] = {'status': 'ERROR', 'message': str(e)}
                    health['issues'].append(f"{name}: {str(e)}")
            
            # Determine overall status
            if health['issues']:
                health['overall_status'] = 'DEGRADED' if len(health['issues']) < 3 else 'UNHEALTHY'
            
            # Check system-level issues
            if len(self.errors) > 5:
                health['issues'].append(f"High error count: {len(self.errors)}")
                health['overall_status'] = 'DEGRADED'
            
            if self.system_state == SystemState.ERROR:
                health['overall_status'] = 'UNHEALTHY'
                health['issues'].append("System in error state")
            
        except Exception as e:
            health['overall_status'] = 'UNHEALTHY'
            health['issues'].append(f"Health check failed: {str(e)}")
        
        return health

# Global system controller instance
system_controller = DEX900DNController()
