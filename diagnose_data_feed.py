#!/usr/bin/env python3
"""
Data Feed Diagnostic Tool for DEX900DN Trading System
Diagnoses why the system is getting zero volatility
"""

import sys
import os
import time
import numpy as np
from datetime import datetime, timedelta

# Add src to path
sys.path.append('src')

try:
    import MetaTrader5 as mt5
    from modules.data_feed import DataHandler
    from config.config import TradingConfig

    # Create config instance
    config = TradingConfig()
    
    def diagnose_mt5_connection():
        """Diagnose MT5 connection and data availability"""
        print("🔍 DIAGNOSING MT5 CONNECTION")
        print("=" * 50)
        
        # Test 1: MT5 Initialization
        print("1. Testing MT5 initialization...")
        if not mt5.initialize():
            error_code, error_desc = mt5.last_error()
            print(f"❌ MT5 initialization failed: {error_code} - {error_desc}")
            return False
        else:
            print("✅ MT5 initialized successfully")
        
        # Test 2: Symbol availability
        print(f"\n2. Testing symbol availability: {config.SYMBOL}")
        symbol_info = mt5.symbol_info(config.SYMBOL)
        if symbol_info is None:
            print(f"❌ Symbol {config.SYMBOL} not found")
            return False
        else:
            print(f"✅ Symbol found: {config.SYMBOL}")
            print(f"   - Visible: {symbol_info.visible}")
            print(f"   - Digits: {symbol_info.digits}")
            print(f"   - Point: {symbol_info.point}")
            print(f"   - Spread: {symbol_info.spread}")
        
        # Test 3: Current tick data
        print(f"\n3. Testing current tick data...")
        tick = mt5.symbol_info_tick(config.SYMBOL)
        if tick is None:
            print(f"❌ No current tick data for {config.SYMBOL}")
            return False
        else:
            print(f"✅ Current tick data:")
            print(f"   - Time: {datetime.fromtimestamp(tick.time)}")
            print(f"   - Bid: {tick.bid}")
            print(f"   - Ask: {tick.ask}")
            print(f"   - Volume: {tick.volume}")
        
        # Test 4: Historical tick data
        print(f"\n4. Testing historical tick data...")
        ticks = mt5.copy_ticks_from(config.SYMBOL, datetime.now(), 10, mt5.COPY_TICKS_ALL)
        if ticks is None or len(ticks) == 0:
            print(f"❌ No historical tick data for {config.SYMBOL}")
            return False
        else:
            print(f"✅ Retrieved {len(ticks)} historical ticks")
            print("   Recent ticks:")
            for i, tick in enumerate(ticks[-5:]):  # Show last 5 ticks
                tick_time = datetime.fromtimestamp(tick['time'])
                print(f"   [{i+1}] {tick_time}: Bid={tick['bid']:.5f}, Ask={tick['ask']:.5f}, Vol={tick['volume']}")
        
        return True
    
    def diagnose_data_handler():
        """Diagnose DataHandler functionality"""
        print("\n🔍 DIAGNOSING DATA HANDLER")
        print("=" * 50)
        
        # Initialize DataHandler
        print("1. Initializing DataHandler...")
        handler = DataHandler()
        
        # Test 2: Fetch initial data
        print("\n2. Fetching initial tick data...")
        ticks = handler.fetch_tick_data(20)  # Get 20 ticks
        print(f"   Retrieved {len(ticks)} ticks")
        
        if len(ticks) > 0:
            print("   Sample ticks:")
            for i, tick in enumerate(ticks[-5:]):
                print(f"   [{i+1}] {tick.timestamp}: Mid={tick.mid_price:.5f}, Vol={tick.volume}")
        
        # Test 3: Check price buffer
        print(f"\n3. Checking price buffer...")
        print(f"   Buffer size: {len(handler.price_buffer)}")
        if len(handler.price_buffer) > 0:
            prices = list(handler.price_buffer)
            print(f"   Price range: {min(prices):.5f} - {max(prices):.5f}")
            print(f"   Recent prices: {prices[-10:]}")
            
            # Check for identical prices
            unique_prices = set(prices)
            print(f"   Unique prices: {len(unique_prices)}")
            if len(unique_prices) == 1:
                print("   ❌ ALL PRICES ARE IDENTICAL - This causes zero volatility!")
            else:
                print("   ✅ Price variation detected")
        
        # Test 4: Calculate volatility manually
        print(f"\n4. Manual volatility calculation...")
        if len(handler.price_buffer) >= 2:
            prices = list(handler.price_buffer)
            prices_array = np.array(prices)
            returns = np.diff(prices_array) / prices_array[:-1]
            volatility = np.std(returns)
            print(f"   Manual volatility: {volatility:.8f}")
            
            if volatility == 0.0:
                print("   ❌ ZERO VOLATILITY CONFIRMED - All prices are identical")
                print(f"   Price analysis:")
                print(f"     - All prices: {prices}")
                print(f"     - Returns: {returns}")
            else:
                print("   ✅ Non-zero volatility calculated")
        
        # Test 5: DataHandler volatility method
        print(f"\n5. Testing DataHandler volatility method...")
        try:
            vol = handler.calculate_volatility(5)
            print(f"   DataHandler volatility: {vol:.8f}")
        except Exception as e:
            print(f"   ❌ Error calculating volatility: {str(e)}")
        
        return handler
    
    def diagnose_market_conditions():
        """Diagnose current market conditions"""
        print("\n🔍 DIAGNOSING MARKET CONDITIONS")
        print("=" * 50)
        
        # Check if market is open
        print("1. Checking market status...")
        current_time = datetime.now()
        print(f"   Current time: {current_time}")
        
        # Get symbol info
        symbol_info = mt5.symbol_info(config.SYMBOL)
        if symbol_info:
            print(f"   Symbol session: {symbol_info.session_deals}")
            print(f"   Symbol volume: {symbol_info.volume}")
        
        # Check recent price movement
        print("\n2. Analyzing recent price movement...")
        ticks = mt5.copy_ticks_from(config.SYMBOL, datetime.now() - timedelta(minutes=10), 100, mt5.COPY_TICKS_ALL)
        if ticks is not None and len(ticks) > 0:
            prices = [tick['bid'] for tick in ticks]
            print(f"   Ticks in last 10 minutes: {len(ticks)}")
            print(f"   Price range: {min(prices):.5f} - {max(prices):.5f}")
            print(f"   Price change: {(max(prices) - min(prices)):.5f}")
            
            if max(prices) == min(prices):
                print("   ❌ NO PRICE MOVEMENT - Market may be closed or static")
            else:
                print("   ✅ Price movement detected")
        
    def main():
        """Run complete data feed diagnosis"""
        print("🚨 DEX900DN DATA FEED DIAGNOSTIC")
        print("=" * 60)
        
        # Step 1: MT5 Connection
        if not diagnose_mt5_connection():
            print("\n❌ MT5 connection failed - cannot proceed")
            return False
        
        # Step 2: Market Conditions
        diagnose_market_conditions()
        
        # Step 3: Data Handler
        handler = diagnose_data_handler()
        
        # Step 4: Continuous monitoring
        print("\n🔍 CONTINUOUS MONITORING (10 seconds)")
        print("=" * 50)
        
        for i in range(10):
            print(f"\nCycle {i+1}/10:")
            ticks = handler.fetch_tick_data(1)
            if ticks:
                tick = ticks[0]
                print(f"   New tick: {tick.timestamp} - Mid={tick.mid_price:.5f}")
                
                if len(handler.price_buffer) >= 2:
                    try:
                        vol = handler.calculate_volatility(1)  # 1-minute window
                        print(f"   Volatility: {vol:.8f}")
                    except Exception as e:
                        print(f"   Volatility error: {str(e)}")
            else:
                print("   No new ticks received")
            
            time.sleep(1)
        
        print("\n✅ DIAGNOSIS COMPLETE")
        return True

except ImportError as e:
    print(f"❌ Import error: {str(e)}")
    print("Make sure you're running this from the correct directory with all dependencies installed")

if __name__ == "__main__":
    main()
