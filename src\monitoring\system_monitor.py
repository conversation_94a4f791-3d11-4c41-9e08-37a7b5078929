"""
System Health Monitor for DEX900DN Trading System
Comprehensive monitoring and alerting for all system components
"""

import logging
import time
import threading
import psutil
import json
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Callable
from dataclasses import dataclass, field
from enum import Enum

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))
from config.config import config

# Setup logging
logging.basicConfig(level=getattr(logging, config.LOG_LEVEL))
logger = logging.getLogger(__name__)

class AlertLevel(Enum):
    """Alert severity levels"""
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"
    CRITICAL = "CRITICAL"

class HealthStatus(Enum):
    """Component health status"""
    HEALTHY = "HEALTHY"
    DEGRADED = "DEGRADED"
    UNHEALTHY = "UNHEALTHY"
    UNKNOWN = "UNKNOWN"

@dataclass
class Alert:
    """System alert"""
    timestamp: datetime
    level: AlertLevel
    component: str
    message: str
    details: Dict[str, Any] = field(default_factory=dict)
    acknowledged: bool = False
    resolved: bool = False

@dataclass
class HealthMetric:
    """Health monitoring metric"""
    name: str
    value: float
    threshold_warning: Optional[float] = None
    threshold_critical: Optional[float] = None
    unit: str = ""
    description: str = ""

@dataclass
class ComponentHealth:
    """Component health status"""
    name: str
    status: HealthStatus
    metrics: List[HealthMetric]
    last_check: datetime
    uptime: timedelta
    error_count: int = 0
    warning_count: int = 0

class SystemHealthMonitor:
    """
    Comprehensive System Health Monitor for DEX900DN
    
    Features:
    - Real-time component monitoring
    - Performance metrics tracking
    - Alert generation and management
    - System resource monitoring
    - Health dashboard
    - Automated recovery actions
    """
    
    def __init__(self, check_interval: float = 5.0):
        self.check_interval = check_interval
        self.monitoring_active = False
        self.monitor_thread = None
        
        # Alert management
        self.alerts: List[Alert] = []
        self.max_alerts = 1000
        self.alert_callbacks: List[Callable[[Alert], None]] = []
        
        # Component health tracking
        self.component_health: Dict[str, ComponentHealth] = {}
        self.system_start_time = datetime.now()
        
        # Performance tracking
        self.performance_history: List[Dict[str, Any]] = []
        self.max_history = 1440  # 24 hours at 1-minute intervals
        
        # Thresholds
        self.thresholds = {
            'cpu_usage_warning': 70.0,
            'cpu_usage_critical': 90.0,
            'memory_usage_warning': 80.0,
            'memory_usage_critical': 95.0,
            'cycle_time_warning': 0.1,  # 100ms
            'cycle_time_critical': 0.5,  # 500ms
            'error_rate_warning': 0.05,  # 5%
            'error_rate_critical': 0.15,  # 15%
            'connection_timeout': 30.0,  # 30 seconds
        }
        
        # Thread safety
        self.monitor_lock = threading.Lock()
        
        logger.info("SystemHealthMonitor initialized")
    
    def start_monitoring(self) -> None:
        """Start health monitoring"""
        if self.monitoring_active:
            return
        
        self.monitoring_active = True
        self.monitor_thread = threading.Thread(target=self._monitoring_loop, daemon=True)
        self.monitor_thread.start()
        
        logger.info("🔍 System health monitoring started")
    
    def stop_monitoring(self) -> None:
        """Stop health monitoring"""
        self.monitoring_active = False
        if self.monitor_thread and self.monitor_thread.is_alive():
            self.monitor_thread.join(timeout=10)
        
        logger.info("🔍 System health monitoring stopped")
    
    def _monitoring_loop(self) -> None:
        """Main monitoring loop"""
        while self.monitoring_active:
            try:
                with self.monitor_lock:
                    # Perform health checks
                    self._check_system_resources()
                    self._check_component_health()
                    self._update_performance_history()
                    self._cleanup_old_data()
                
                time.sleep(self.check_interval)
                
            except Exception as e:
                logger.error(f"Error in monitoring loop: {str(e)}")
                time.sleep(self.check_interval)
    
    def _check_system_resources(self) -> None:
        """Check system resource usage"""
        try:
            # CPU usage
            cpu_percent = psutil.cpu_percent(interval=1)
            self._check_threshold(
                'system_cpu', cpu_percent, 
                self.thresholds['cpu_usage_warning'],
                self.thresholds['cpu_usage_critical'],
                'CPU usage', '%'
            )
            
            # Memory usage
            memory = psutil.virtual_memory()
            memory_percent = memory.percent
            self._check_threshold(
                'system_memory', memory_percent,
                self.thresholds['memory_usage_warning'],
                self.thresholds['memory_usage_critical'],
                'Memory usage', '%'
            )
            
            # Disk usage
            disk = psutil.disk_usage('/')
            disk_percent = (disk.used / disk.total) * 100
            self._check_threshold(
                'system_disk', disk_percent,
                80.0, 95.0,  # Disk thresholds
                'Disk usage', '%'
            )
            
            # Network connections
            connections = len(psutil.net_connections())
            if connections > 1000:  # High connection count
                self._create_alert(
                    AlertLevel.WARNING, 'system_network',
                    f"High network connection count: {connections}"
                )
            
        except Exception as e:
            logger.error(f"Error checking system resources: {str(e)}")
    
    def _check_component_health(self) -> None:
        """Check health of all system components"""
        # This would be called by the main controller
        # For now, we'll create placeholder health checks
        
        components = [
            'cycle_tracker', 'data_handler', 'phase_classifier',
            'strategy_manager', 'risk_manager', 'trading_executor',
            'mt5_connection', 'order_executor', 'position_manager'
        ]
        
        for component in components:
            try:
                # Placeholder health check
                health = ComponentHealth(
                    name=component,
                    status=HealthStatus.HEALTHY,
                    metrics=[],
                    last_check=datetime.now(),
                    uptime=datetime.now() - self.system_start_time
                )
                
                self.component_health[component] = health
                
            except Exception as e:
                logger.error(f"Error checking {component} health: {str(e)}")
                self._create_alert(
                    AlertLevel.ERROR, component,
                    f"Health check failed: {str(e)}"
                )
    
    def _check_threshold(self, component: str, value: float, 
                        warning_threshold: float, critical_threshold: float,
                        metric_name: str, unit: str) -> None:
        """Check if a metric exceeds thresholds"""
        
        if value >= critical_threshold:
            self._create_alert(
                AlertLevel.CRITICAL, component,
                f"{metric_name} critical: {value:.1f}{unit} (threshold: {critical_threshold}{unit})"
            )
        elif value >= warning_threshold:
            self._create_alert(
                AlertLevel.WARNING, component,
                f"{metric_name} warning: {value:.1f}{unit} (threshold: {warning_threshold}{unit})"
            )
    
    def _create_alert(self, level: AlertLevel, component: str, message: str,
                     details: Dict[str, Any] = None) -> Alert:
        """Create and process a new alert"""
        alert = Alert(
            timestamp=datetime.now(),
            level=level,
            component=component,
            message=message,
            details=details or {}
        )
        
        # Add to alerts list
        self.alerts.append(alert)
        
        # Keep only recent alerts
        if len(self.alerts) > self.max_alerts:
            self.alerts = self.alerts[-self.max_alerts:]
        
        # Log the alert
        log_level = {
            AlertLevel.INFO: logging.INFO,
            AlertLevel.WARNING: logging.WARNING,
            AlertLevel.ERROR: logging.ERROR,
            AlertLevel.CRITICAL: logging.CRITICAL
        }[level]
        
        logger.log(log_level, f"🚨 ALERT [{level.value}] {component}: {message}")
        
        # Call alert callbacks
        for callback in self.alert_callbacks:
            try:
                callback(alert)
            except Exception as e:
                logger.error(f"Error in alert callback: {str(e)}")
        
        return alert
    
    def _update_performance_history(self) -> None:
        """Update performance history"""
        try:
            performance_data = {
                'timestamp': datetime.now().isoformat(),
                'cpu_percent': psutil.cpu_percent(),
                'memory_percent': psutil.virtual_memory().percent,
                'disk_percent': (psutil.disk_usage('/').used / psutil.disk_usage('/').total) * 100,
                'network_connections': len(psutil.net_connections()),
                'active_alerts': len([a for a in self.alerts if not a.resolved]),
                'component_count': len(self.component_health),
                'healthy_components': len([h for h in self.component_health.values() 
                                         if h.status == HealthStatus.HEALTHY])
            }
            
            self.performance_history.append(performance_data)
            
            # Keep only recent history
            if len(self.performance_history) > self.max_history:
                self.performance_history = self.performance_history[-self.max_history:]
                
        except Exception as e:
            logger.error(f"Error updating performance history: {str(e)}")
    
    def _cleanup_old_data(self) -> None:
        """Clean up old alerts and data"""
        cutoff_time = datetime.now() - timedelta(hours=24)
        
        # Remove old resolved alerts
        self.alerts = [alert for alert in self.alerts 
                      if not alert.resolved or alert.timestamp > cutoff_time]
    
    def add_alert_callback(self, callback: Callable[[Alert], None]) -> None:
        """Add callback function for alert notifications"""
        self.alert_callbacks.append(callback)
    
    def get_system_health(self) -> Dict[str, Any]:
        """Get comprehensive system health status"""
        with self.monitor_lock:
            # Calculate overall health
            healthy_components = len([h for h in self.component_health.values() 
                                    if h.status == HealthStatus.HEALTHY])
            total_components = len(self.component_health)
            health_percentage = (healthy_components / total_components * 100) if total_components > 0 else 0
            
            # Determine overall status
            if health_percentage >= 90:
                overall_status = HealthStatus.HEALTHY
            elif health_percentage >= 70:
                overall_status = HealthStatus.DEGRADED
            else:
                overall_status = HealthStatus.UNHEALTHY
            
            # Count alerts by level
            alert_counts = {level.value: 0 for level in AlertLevel}
            active_alerts = [a for a in self.alerts if not a.resolved]
            for alert in active_alerts:
                alert_counts[alert.level.value] += 1
            
            return {
                'overall_status': overall_status.value,
                'health_percentage': health_percentage,
                'uptime': (datetime.now() - self.system_start_time).total_seconds(),
                'component_health': {name: {
                    'status': health.status.value,
                    'last_check': health.last_check.isoformat(),
                    'uptime': health.uptime.total_seconds(),
                    'error_count': health.error_count,
                    'warning_count': health.warning_count
                } for name, health in self.component_health.items()},
                'alert_summary': alert_counts,
                'active_alerts': len(active_alerts),
                'total_alerts': len(self.alerts),
                'system_resources': self._get_current_resources()
            }
    
    def _get_current_resources(self) -> Dict[str, Any]:
        """Get current system resource usage"""
        try:
            return {
                'cpu_percent': psutil.cpu_percent(),
                'memory_percent': psutil.virtual_memory().percent,
                'disk_percent': (psutil.disk_usage('/').used / psutil.disk_usage('/').total) * 100,
                'network_connections': len(psutil.net_connections()),
                'process_count': len(psutil.pids())
            }
        except Exception as e:
            logger.error(f"Error getting system resources: {str(e)}")
            return {}
    
    def get_alerts(self, level: Optional[AlertLevel] = None, 
                  component: Optional[str] = None,
                  resolved: Optional[bool] = None,
                  limit: int = 100) -> List[Alert]:
        """Get alerts with optional filtering"""
        filtered_alerts = self.alerts
        
        if level:
            filtered_alerts = [a for a in filtered_alerts if a.level == level]
        
        if component:
            filtered_alerts = [a for a in filtered_alerts if a.component == component]
        
        if resolved is not None:
            filtered_alerts = [a for a in filtered_alerts if a.resolved == resolved]
        
        # Sort by timestamp (newest first) and limit
        filtered_alerts.sort(key=lambda x: x.timestamp, reverse=True)
        return filtered_alerts[:limit]
    
    def acknowledge_alert(self, alert_index: int) -> bool:
        """Acknowledge an alert"""
        try:
            if 0 <= alert_index < len(self.alerts):
                self.alerts[alert_index].acknowledged = True
                logger.info(f"Alert acknowledged: {self.alerts[alert_index].message}")
                return True
        except Exception as e:
            logger.error(f"Error acknowledging alert: {str(e)}")
        
        return False
    
    def resolve_alert(self, alert_index: int) -> bool:
        """Resolve an alert"""
        try:
            if 0 <= alert_index < len(self.alerts):
                self.alerts[alert_index].resolved = True
                logger.info(f"Alert resolved: {self.alerts[alert_index].message}")
                return True
        except Exception as e:
            logger.error(f"Error resolving alert: {str(e)}")
        
        return False
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """Get performance summary from history"""
        if not self.performance_history:
            return {}
        
        recent_data = self.performance_history[-60:]  # Last hour
        
        try:
            avg_cpu = sum(d['cpu_percent'] for d in recent_data) / len(recent_data)
            avg_memory = sum(d['memory_percent'] for d in recent_data) / len(recent_data)
            max_cpu = max(d['cpu_percent'] for d in recent_data)
            max_memory = max(d['memory_percent'] for d in recent_data)
            
            return {
                'period_minutes': len(recent_data),
                'avg_cpu_percent': avg_cpu,
                'avg_memory_percent': avg_memory,
                'max_cpu_percent': max_cpu,
                'max_memory_percent': max_memory,
                'alert_frequency': len([a for a in self.alerts 
                                      if a.timestamp > datetime.now() - timedelta(hours=1)]),
                'stability_score': self._calculate_stability_score(recent_data)
            }
        except Exception as e:
            logger.error(f"Error calculating performance summary: {str(e)}")
            return {}
    
    def _calculate_stability_score(self, data: List[Dict[str, Any]]) -> float:
        """Calculate system stability score (0-100)"""
        try:
            if not data:
                return 0.0
            
            # Factors affecting stability
            cpu_stability = 100 - max(0, max(d['cpu_percent'] for d in data) - 50)
            memory_stability = 100 - max(0, max(d['memory_percent'] for d in data) - 70)
            alert_penalty = min(50, len([a for a in self.alerts 
                                       if a.timestamp > datetime.now() - timedelta(hours=1)]) * 10)
            
            # Component health factor
            healthy_ratio = len([h for h in self.component_health.values() 
                               if h.status == HealthStatus.HEALTHY]) / max(1, len(self.component_health))
            health_score = healthy_ratio * 100
            
            # Combined stability score
            stability = (cpu_stability + memory_stability + health_score) / 3 - alert_penalty
            return max(0, min(100, stability))
            
        except Exception as e:
            logger.error(f"Error calculating stability score: {str(e)}")
            return 0.0
    
    def export_health_report(self, filepath: str) -> bool:
        """Export comprehensive health report to file"""
        try:
            report = {
                'timestamp': datetime.now().isoformat(),
                'system_health': self.get_system_health(),
                'performance_summary': self.get_performance_summary(),
                'recent_alerts': [
                    {
                        'timestamp': alert.timestamp.isoformat(),
                        'level': alert.level.value,
                        'component': alert.component,
                        'message': alert.message,
                        'acknowledged': alert.acknowledged,
                        'resolved': alert.resolved
                    }
                    for alert in self.get_alerts(limit=50)
                ],
                'thresholds': self.thresholds
            }
            
            with open(filepath, 'w') as f:
                json.dump(report, f, indent=2)
            
            logger.info(f"Health report exported to {filepath}")
            return True
            
        except Exception as e:
            logger.error(f"Error exporting health report: {str(e)}")
            return False

# Global health monitor instance
health_monitor = SystemHealthMonitor()
