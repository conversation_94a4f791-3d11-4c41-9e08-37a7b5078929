# DEX900DN Trading System Dependencies
# Core trading and data analysis
deriv-api>=1.0.0
MetaTrader5>=5.0.0
numpy>=1.21.0
pandas>=1.3.0
matplotlib>=3.4.0

# Technical analysis
ta-lib>=0.4.0
talib-binary>=0.4.19  # Alternative if ta-lib installation fails

# Statistical analysis and Monte Carlo
scipy>=1.7.0
scikit-learn>=1.0.0

# Time handling
pytz>=2021.1
datetime

# Configuration and logging
pyyaml>=5.4.0
python-dotenv>=0.19.0

# API and networking
requests>=2.25.0
websocket-client>=1.0.0

# Development and testing
pytest>=6.2.0
pytest-cov>=2.12.0
black>=21.0.0
flake8>=3.9.0

# Optional: Jupyter for analysis
jupyter>=1.0.0
ipykernel>=6.0.0
